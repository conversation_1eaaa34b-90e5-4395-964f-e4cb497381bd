import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as m}from"./vendor-851db8c1.js";import{M as f,G as j,e as v,b as y}from"./index-f915b394.js";let h=new f;function k({data:s,getData:x,setData:d,onCancelMembership:u,membershipLoading:b}){var n,a,t;const{dispatch:c}=m.useContext(j),[i,r]=m.useState(!1),p=async()=>{r(!0),h.setTable("user"),(await h.callRestAPI({id:s.user.id,status:1},"PUT")).error||(y(c,"Membership activated successfully",5e3,"success"),x(1,10),d(l=>({...l,user:{...l.user,status:1}})),r(!1)),r(!1)};return e.jsxs("div",{className:"max-w-md",children:[i&&e.jsx(v,{}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h2",{className:"mb-1 text-sm text-gray-600",children:"Address"}),e.jsx("p",{className:"font-medium text-gray-900",children:(s==null?void 0:s.address)||"--"})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h2",{className:"mb-1 text-sm text-gray-600",children:"Subscription ID"}),e.jsx("p",{className:"font-medium text-gray-900",children:((n=s==null?void 0:s.user)==null?void 0:n.id)||"--"})]}),e.jsxs("div",{className:"mb-8",children:[e.jsx("h2",{className:"mb-1 text-sm text-gray-600",children:"Payment method"}),e.jsx("p",{className:"font-medium text-gray-900",children:(s==null?void 0:s.payment_method)||"--"})]}),e.jsx("div",{className:"border-t border-gray-200 pt-4"}),e.jsx("button",{onClick:((a=s==null?void 0:s.user)==null?void 0:a.status)===1?u:p,className:"text-gray-600 underline hover:text-gray-800",children:((t=s==null?void 0:s.user)==null?void 0:t.status)===1?"Cancel membership":"Activate membership"})]})}let g=new f;function S({data:s,getData:x,setData:d,onClose:u}){const{dispatch:b}=m.useContext(j),[c,i]=m.useState(!1),[r,p]=m.useState({name:(s==null?void 0:s.name)||"",address:(s==null?void 0:s.address)||"",payment_method:(s==null?void 0:s.payment_method)||"",revenue_type:(s==null?void 0:s.revenue_type)||"",country:(s==null?void 0:s.country)||"",state:(s==null?void 0:s.state)||"",years_under_panel:(s==null?void 0:s.years_under_panel)||""}),n=t=>{const{name:o,value:l}=t.target;p(N=>({...N,[o]:l}))},a=async t=>{t.preventDefault(),i(!0);try{g.setTable("clubs"),(await g.callRestAPI({id:s.id,...r},"PUT")).error||(y(b,"Club updated successfully",5e3,"success"),d(l=>({...l,...r})),x(1,10),u())}catch(o){console.error("Error updating club:",o),y(b,"Error updating club",5e3,"error")}finally{i(!1)}};return e.jsxs("div",{className:"max-w-2xl",children:[c&&e.jsx(v,{}),e.jsxs("form",{onSubmit:a,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"Club Name"}),e.jsx("input",{type:"text",name:"name",value:r.name,onChange:n,className:"w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"Address"}),e.jsx("input",{type:"text",name:"address",value:r.address,onChange:n,className:"w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"Payment Method"}),e.jsx("input",{type:"text",name:"payment_method",value:r.payment_method,onChange:n,className:"w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"Revenue Type"}),e.jsx("input",{type:"text",name:"revenue_type",value:r.revenue_type,onChange:n,className:"w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"Country"}),e.jsx("input",{type:"text",name:"country",value:r.country,onChange:n,className:"w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"State"}),e.jsx("input",{type:"text",name:"state",value:r.state,onChange:n,className:"w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"Years Under Panel"}),e.jsx("input",{type:"number",name:"years_under_panel",value:r.years_under_panel,onChange:n,className:"w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"})]}),e.jsxs("div",{className:"flex justify-end gap-3 pt-4",children:[e.jsx("button",{type:"button",onClick:u,className:"rounded-md border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{type:"submit",disabled:c,className:"rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 disabled:opacity-50",children:"Save Changes"})]})]})]})}export{k as C,S as E};

import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as r,f as A}from"./vendor-851db8c1.js";import{u as C}from"./react-hook-form-687afde5.js";import{o as S}from"./yup-2824f222.js";import{c as P,a as i}from"./yup-54691517.js";import{w as U,A as $,G as F,M as R,b as q,t as T}from"./index-f915b394.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const D=({setSidebar:m})=>{var h,f,b,g;const j=P({email:i().email().required(),password:i().required(),role:i()}).required(),{dispatch:n}=r.useContext($),{dispatch:y}=r.useContext(F),[d,c]=r.useState(!1),N=A(),{register:o,handleSubmit:p,setError:u,formState:{errors:t}}=C({resolver:S(j)}),E=[{name:"role",value:"club"},{name:"role",value:"staff"},{name:"role",value:"user"},{name:"role",value:"coach"},{name:"role",value:"admin"}],x=async s=>{let k=new R;c(!0);try{const a=await k.register(s.email,s.password,s.role);if(!a.error)q(n,"Added"),N("/admin/users");else if(a.validation){const w=Object.keys(a.validation);for(let l=0;l<w.length;l++){const v=w[l];u(v,{type:"manual",message:a.validation[v]})}}}catch(a){console.log("Error",a),u("email",{type:"manual",message:a.message}),T(n,a.message)}c(!1)};return r.useEffect(()=>{y({type:"SETPATH",payload:{path:"users"}})},[]),e.jsxs("div",{className:"mx-auto  rounded",children:[e.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[e.jsx("div",{className:"flex items-center gap-3",children:e.jsx("span",{className:"text-lg font-semibold",children:"Add User"})}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 shadow-sm hover:bg-[#f4f4f4]",onClick:()=>m(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center rounded-md bg-[#4F46E5] px-3 py-2 text-white shadow-sm",onClick:async()=>{await p(x)(),m(!1)},disabled:d,children:d?"Saving...":"Save"})]})]}),e.jsxs("form",{className:" w-full p-4 text-left",onSubmit:p(x),children:[e.jsxs("div",{className:"mb-4 ",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"email",children:"Email"}),e.jsx("input",{type:"email",placeholder:"Email",...o("email"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(h=t.email)!=null&&h.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(f=t.email)==null?void 0:f.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Role"}),e.jsx("select",{name:"role",id:"role",className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...o("role"),children:E.map(s=>e.jsx("option",{name:s.name,value:s.value,defaultValue:s.value==="client",children:s.value},s.value))})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"password",children:"Password"}),e.jsx("input",{type:"password",placeholder:"******************",...o("password"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(b=t.password)!=null&&b.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(g=t.password)==null?void 0:g.message})]})]})]})},we=U(D,"user","You don't have permission to add users");export{we as default};

import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r,b as ae}from"./vendor-851db8c1.js";import{g as le,h as ne}from"./index.esm-09a3a6b8.js";import{G as ie,a1 as re,a5 as R,b as G}from"./index-f915b394.js";import{M as oe}from"./react-tooltip-7a26650a.js";function de({title:o,titleId:m,...i},u){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:u,"aria-labelledby":m},i),o?r.createElement("title",{id:m},o):null,r.createElement("path",{d:"m11.645 20.91-.007-.003-.022-.012a15.247 15.247 0 0 1-.383-.218 25.18 25.18 0 0 1-4.244-3.17C4.688 15.36 2.25 12.174 2.25 8.25 2.25 5.322 4.714 3 7.688 3A5.5 5.5 0 0 1 12 5.052 5.5 5.5 0 0 1 16.313 3c2.973 0 5.437 2.322 5.437 5.25 0 3.925-2.438 7.111-4.739 9.256a25.175 25.175 0 0 1-4.244 3.17 15.247 15.247 0 0 1-.383.219l-.022.012-.007.004-.003.001a.752.752 0 0 1-.704 0l-.003-.001Z"}))}const ce=r.forwardRef(de),h=ce,xe=({players:o=[],groups:m=[],selectedPlayers:i=[],onPlayerToggle:u,isFindBuddyEnabled:g,onFindBuddyToggle:Z,playersNeeded:d,onPlayersNeededChange:y,showPlayersNeeded:H=!0,showCurrentGroup:E=!0,showAddReservationToFindBuddy:O=!0,maximumPlayers:c=4,userProfile:a=null,setSelectedPlayers:W,onNtrpMinChange:C,onNtrpMaxChange:_,onShortBioChange:k,initialNtrpMin:D=3.5,initialNtrpMax:Q=3.5,initialShortBio:Y="",familyMembers:f=[],currentUser:n=null,onCurrentUserChange:$})=>{var L,A;const[M,q]=r.useState(""),[x,S]=r.useState(null),{dispatch:I}=r.useContext(ie),[T,X]=r.useState(Y),[J,K]=r.useState(D),[V,U]=r.useState(Q),b=parseInt(localStorage.getItem("user")),p=s=>s.guardian===b,P=s=>(localStorage.getItem("user"),s.filter(t=>!(t.guardian&&t.guardian!==b))),ee=s=>(localStorage.getItem("user"),s.filter(t=>!(t.guardian&&t.guardian!==b))),j=P(o||[]),v=m.map(s=>({...s,members:ee(s.members||[])})),B=j==null?void 0:j.filter(s=>`${s.first_name} ${s.last_name}`.toLowerCase().includes(M.toLowerCase())),z=s=>i.some(t=>t.id===s),se=()=>{const s=c-i.length;y(Math.min(d+1,s))},te=()=>{y(Math.max(d-1,0))},w=s=>{if(i.some(l=>l.id===s.id)){const l=n||a;if(s.id===(l==null?void 0:l.id)){G(I,"You cannot remove the primary player from the reservation",3e3,"warning");return}u(s);return}if(i.length>=c){G(I,`Maximum ${c} players allowed (including yourself)`,3e3,"warning");return}u(s)},F=localStorage.getItem("user");return ae.useEffect(()=>{if(o.length>0){const s=n||a||o.find(t=>t.id===parseInt(F));s&&W(t=>t.some(N=>N.id===s.id)?t:[s,...t])}},[F,o,n,a]),e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:e.jsxs("h2",{className:"text-base font-medium",children:["Add players (",i.length,"/",c,")"]})}),e.jsxs("div",{className:"p-4",children:[e.jsx("div",{className:"mb-4",children:e.jsxs("div",{className:"flex items-center gap-1 rounded-lg border border-gray-300 px-2 focus:border-blue-500 focus:outline-none",children:[e.jsx("span",{className:"w-5",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.25 2.5C12.976 2.5 16 5.524 16 9.25C16 12.976 12.976 16 9.25 16C5.524 16 2.5 12.976 2.5 9.25C2.5 5.524 5.524 2.5 9.25 2.5ZM9.25 14.5C12.1502 14.5 14.5 12.1502 14.5 9.25C14.5 6.349 12.1502 4 9.25 4C6.349 4 4 6.349 4 9.25C4 12.1502 6.349 14.5 9.25 14.5ZM15.6137 14.5532L17.7355 16.6742L16.6742 17.7355L14.5532 15.6137L15.6137 14.5532Z",fill:"#525866"})})}),e.jsx("input",{type:"text",placeholder:"search by name",value:M,onChange:s=>q(s.target.value),className:"w-full border-none bg-transparent focus:outline-none focus:ring-0"})]})}),f.length>0&&e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:"Playing as"}),e.jsx("div",{className:"relative",children:e.jsxs("select",{value:(n==null?void 0:n.id)||(a==null?void 0:a.id)||"",onChange:s=>{const t=parseInt(s.target.value);let l;t===(a==null?void 0:a.id)?l=a:l=f.find(N=>N.id===t),l&&$&&l.id!==(n==null?void 0:n.id)&&$(l)},className:"w-full appearance-none rounded-lg border border-gray-300 bg-white px-3 py-2 pr-8 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",children:[e.jsxs("option",{value:a==null?void 0:a.id,children:[a==null?void 0:a.first_name," ",a==null?void 0:a.last_name," (You)"]}),f.map(s=>e.jsxs("option",{value:s.id,children:[s.first_name," ",s.last_name," (",s.family_role||"Family Member",")"]},s.id))]})}),e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"Select who is playing in this reservation"})]}),i.length>0&&e.jsx("div",{className:"mb-4 flex flex-wrap gap-2",children:i.map(s=>{const t=p(s);return e.jsxs("div",{className:`flex items-center gap-2 rounded-full py-1 pl-1 pr-2 ${t?"border border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50":"bg-gray-100"}`,children:[e.jsxs("div",{className:"relative h-6 w-6 overflow-hidden rounded-full bg-gray-200",children:[e.jsx("img",{src:(s==null?void 0:s.photo)||"/default-avatar.png",alt:`${s.first_name} ${s.last_name}`,className:"h-full w-full object-cover"}),t&&e.jsx("div",{className:"absolute -bottom-0.5 -right-0.5 flex h-3.5 w-3.5 items-center justify-center rounded-full bg-blue-500 ring-1 ring-white",children:e.jsx(h,{className:"h-2 w-2 text-white"})})]}),e.jsx("div",{className:"flex flex-col",children:e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{className:"text-sm",children:`${s.first_name} ${s.last_name}`}),t&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsxs("span",{className:"inline-flex items-center gap-0.5 rounded-full bg-blue-100 px-1.5 py-0.5 text-xs font-medium text-blue-700",children:[e.jsx(h,{className:"h-2.5 w-2.5"}),"Family"]}),s.family_role&&e.jsx("span",{className:"text-xs font-medium capitalize text-blue-600",children:s.family_role})]})]})}),s.id!==((n==null?void 0:n.id)||(a==null?void 0:a.id))&&e.jsx("button",{onClick:l=>{l.stopPropagation(),w(s)},className:"text-gray-400 hover:text-gray-600",children:e.jsx(re,{className:"h-4 w-4"})})]},s.id)})}),e.jsxs("div",{className:"flex flex-wrap items-center gap-2 p-2",children:[e.jsx("button",{onClick:()=>S(null),className:`rounded-full border px-2 py-1 text-xs transition-colors ${x?"border-gray-300 text-gray-500 hover:bg-gray-50":"border-primaryBlue bg-primaryBlue/10 text-primaryBlue"}`,children:"All Players"}),v.map(s=>e.jsx("div",{className:"space-y-2",children:e.jsx("button",{onClick:()=>S(s.group_id),className:`rounded-full border px-2 py-1 text-xs transition-colors ${x===s.group_id?"border-primaryBlue bg-primaryBlue/10 text-primaryBlue":"border-gray-300 text-gray-500 hover:bg-gray-50"}`,children:s.group_name})},s.group_id))]}),e.jsx("div",{className:"max-h-64 space-y-2 overflow-y-auto rounded-xl bg-gray-50",children:x?e.jsx("div",{className:"p-2",children:(L=v.find(s=>s.group_id===x))==null?void 0:L.members.map(s=>{const t=p(s);return e.jsxs("div",{onClick:()=>w(s),className:`flex cursor-pointer items-center space-x-3 rounded-lg p-2 transition-colors hover:bg-white ${t?"border border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50":"hover:bg-gray-50"}`,children:[e.jsx("input",{type:"checkbox",checked:z(s.id),onChange:()=>{},className:"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"h-8 w-8 overflow-hidden rounded-full bg-gray-200 shadow-sm ring-2 ring-white",children:s.photo?e.jsx("img",{src:s.photo,alt:`${s.first_name} ${s.last_name}`,className:"h-full w-full object-cover"}):e.jsx("div",{className:"flex h-full w-full items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200",children:e.jsx(le,{className:"h-6 w-6 text-gray-400"})})}),t&&e.jsx("div",{className:"absolute -bottom-1 -right-1 z-50 flex h-6 w-6 items-center justify-center rounded-full bg-blue-500 ring-2 ring-white",children:e.jsx(ne,{className:"h-3 w-3 text-white"})})]}),e.jsx("div",{className:"flex flex-col",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("span",{className:"font-medium text-gray-900",children:[s.first_name," ",s.last_name]}),t&&e.jsxs("div",{children:[e.jsxs("span",{className:"inline-flex items-center gap-1 rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-700",children:[e.jsx(h,{className:"h-3 w-3"}),"Family"]}),s.family_role&&e.jsx("span",{className:"text-xs font-medium capitalize text-blue-600",children:s.family_role})]})]})})]},s.id)})}):e.jsx("div",{className:"p-2",children:B.length>0?B.map(s=>{const t=p(s);return e.jsxs("div",{onClick:()=>w(s),className:`flex cursor-pointer items-center space-x-3 rounded-lg p-2 transition-colors hover:bg-white ${t?"border border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50":"hover:bg-gray-50"}`,children:[e.jsx("input",{type:"checkbox",checked:z(s.id),onChange:()=>{},className:"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),e.jsxs("div",{className:"relative h-8 w-8 overflow-hidden rounded-full bg-gray-200",children:[e.jsx("img",{src:(s==null?void 0:s.photo)||"/default-avatar.png",alt:`${s.first_name} ${s.last_name}`,className:"h-full w-full object-cover"}),t&&e.jsx("div",{className:"absolute -bottom-0.5 -right-0.5 flex h-3.5 w-3.5 items-center justify-center rounded-full bg-blue-500 ring-1 ring-white",children:e.jsx(h,{className:"h-2 w-2 text-white"})})]}),e.jsx("div",{className:"flex flex-col",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("span",{className:"font-medium text-gray-900",children:[s.first_name," ",s.last_name]}),t&&e.jsxs("div",{children:[e.jsxs("span",{className:"inline-flex items-center gap-1 rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-700",children:[e.jsx(h,{className:"h-3 w-3"}),"Family"]}),s.family_role&&e.jsx("span",{className:"text-xs font-medium capitalize text-blue-600",children:s.family_role})]})]})})]},s.id)}):e.jsx("p",{className:"text-center text-sm text-gray-500",children:"No players found"})})}),E&&e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"mt-4 space-y-4",children:[x&&e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-base ",children:"Current group"}),e.jsx("span",{className:"text-base ",children:(A=v.find(s=>s.group_id===x))==null?void 0:A.group_name})]}),O&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between gap-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"relative inline-flex cursor-pointer",onClick:Z,children:[e.jsx("div",{className:`h-6 w-11 rounded-full transition-colors duration-200 ease-in-out ${g?"bg-blue-600":"bg-gray-200"}`}),e.jsx("div",{className:`absolute top-1 h-4 w-4 rounded-full bg-white transition-transform duration-200 ease-in-out ${g?"left-6":"left-1"}`})]}),e.jsx("span",{className:"text-sm",children:"Add reservation to Find Buddy system"})]}),e.jsx("div",{"data-tooltip-id":"find-buddy-tooltip",className:"rounded-full p-1",children:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M8.95829 9.1665H9.99995L9.99996 13.5415M17.7083 9.99984C17.7083 14.257 14.2572 17.7082 9.99996 17.7082C5.74276 17.7082 2.29163 14.257 2.29163 9.99984C2.29163 5.74264 5.74276 2.2915 9.99996 2.2915C14.2572 2.2915 17.7083 5.74264 17.7083 9.99984Z",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M10 6.125C9.70089 6.125 9.45837 6.36751 9.45837 6.66667C9.45837 6.96582 9.70089 7.20833 10 7.20833C10.2992 7.20833 10.5417 6.96582 10.5417 6.66667C10.5417 6.36751 10.2992 6.125 10 6.125Z",fill:"#868C98",stroke:"#868C98",strokeWidth:"0.25"})]})}),e.jsx(oe,{id:"find-buddy-tooltip",place:"top",className:"z-50 max-w-md rounded-lg border border-gray-200 bg-white p-4 text-gray-700 shadow-lg",style:{backgroundColor:"#fff",border:"1px solid #e4e4e7",color:"#000",borderRadius:"12px",padding:"12px",maxWidth:"300px"},children:"Creates a request for other players on the app to join you incase of shortage of players."})]}),g&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-3 block text-sm text-gray-500",children:"My group NTRP score"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("div",{className:"flex h-10 min-h-[40px] flex-1 shrink basis-0 items-start justify-center overflow-hidden rounded-xl border border-solid border-zinc-200 bg-white shadow-sm",children:[e.jsx("div",{className:"flex w-fit items-center justify-center gap-2 self-stretch bg-slate-50 px-2 py-2.5 text-neutral-400",children:"Min"}),e.jsx("select",{className:"flex flex-1 shrink basis-0 items-center justify-between overflow-hidden border-l border-none border-zinc-200 bg-white px-2 py-2.5 text-gray-950 outline-none focus:outline-none focus:ring-0",value:J,onChange:s=>{const t=s.target.value;K(t),C&&C(t)},children:R.map(s=>e.jsx("option",{value:s,children:s},s))})]}),e.jsxs("div",{className:"flex h-10 min-h-[40px] flex-1 shrink basis-0 items-start justify-center overflow-hidden rounded-xl border border-solid border-zinc-200 bg-white shadow-sm",children:[e.jsx("div",{className:"flex w-fit items-center justify-center gap-2 self-stretch bg-slate-50 px-2 py-2.5 text-neutral-400",children:"Max"}),e.jsx("select",{className:"flex flex-1 shrink basis-0 items-center justify-between overflow-hidden border-l border-none border-zinc-200 bg-white px-2 py-2.5 text-gray-950 outline-none focus:outline-none focus:ring-0",value:V,onChange:s=>{const t=s.target.value;U(t),_&&_(t)},children:R.map(s=>e.jsx("option",{value:s,children:s},s))})]})]})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium",children:["Short bio"," ",e.jsx("span",{className:"text-gray-500",children:"(Optional)"})]}),e.jsx("textarea",{value:T,onChange:s=>{const t=s.target.value;X(t),k&&k(t)},className:"mt-2 w-full rounded-xl border border-gray-300 p-2",rows:3})]})]})]}),H&&e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm",children:"Players needed"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("button",{onClick:te,disabled:d===0,className:`flex h-8 w-8 items-center justify-center rounded-lg border border-gray-300 text-gray-500 hover:bg-gray-50 ${d===0?"cursor-not-allowed opacity-50":""}`,children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",className:"h-5 w-5",children:e.jsx("path",{fillRule:"evenodd",d:"M4 10a.75.75 0 01.75-.75h10.5a.75.75 0 010 1.5H4.75A.75.75 0 014 10z",clipRule:"evenodd"})})}),e.jsx("span",{className:"w-8 text-center",children:d}),e.jsx("button",{onClick:se,disabled:d>=c-i.length,className:`flex h-8 w-8 items-center justify-center rounded-lg border border-gray-300 text-gray-500 hover:bg-gray-50 ${d>=c-i.length?"cursor-not-allowed opacity-50":""}`,children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",className:"h-5 w-5",children:e.jsx("path",{d:"M10.75 4.75a.75.75 0 00-1.5 0v4.5h-4.5a.75.75 0 000 1.5h4.5v4.5a.75.75 0 001.5 0v-4.5h4.5a.75.75 0 000-1.5h-4.5v-4.5z"})})})]})]})]})})]})]})},be=xe;export{be as A};

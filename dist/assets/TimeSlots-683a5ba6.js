import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import{r as h}from"./vendor-851db8c1.js";import{d as J}from"./index-f915b394.js";import{M as re}from"./react-tooltip-7a26650a.js";import{f as oe}from"./date-fns-07266b7d.js";const le=({selectedDate:N,timeRange:x,onTimeClick:M,onNext:k,nextButtonText:K="Next",startHour:C=8,endHour:L=24,interval:H=30,className:Q="",multipleSlots:T=!1,timeSlots:ae=[],onTimeSlotsChange:y,individualSelection:de=!1,isTimeSlotAvailable:me,clubTimes:j=[],isLoading:V,coachAvailability:B=[],height:W="h-fit",minBookingTime:X=30,enforceMinBookingTime:Y=!1})=>{var F;const[I,A]=h.useState([]),[o,u]=h.useState([]),[D,$]=h.useState(350),O=h.useRef(null),w=h.useCallback(()=>{const e=[];for(let t=C;t<=L;t++)for(let n=0;n<60;n+=H){const i=t===24?0:t,r=`${i.toString().padStart(2,"0")}:${n.toString().padStart(2,"0")}`,l=i>=12?"PM":"AM",d=`${i===0?12:i>12?i-12:i}:${n.toString().padStart(2,"0")} ${l}`;e.push({time24:r,time12:d})}return e},[C,L,H]);h.useEffect(()=>{if(x&&x.length>0&&x[0].from&&x[0].until){const e=w(),t=x[0].from,n=x[0].until;if(!e.find(d=>d.time12===t))return;const r=e.findIndex(d=>d.time12===n);if(r===-1)return;const l=e.findIndex(d=>d.time12===t),a=[];for(let d=l;d<r;d++)a.push(e[d].time24);u(a)}else u([])},[x,w]),h.useEffect(()=>{const e=()=>{const t=window.innerHeight;t<600?$(200):t<800?$(300):$(350)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);const _=e=>{if(!j||j.length===0)return!0;const[t,n]=e.split(":"),i=parseInt(t)*60+parseInt(n);return j.some(r=>{const[l,a]=r.from.split(":"),[d,f]=r.until.split(":"),b=parseInt(l)*60+parseInt(a),c=parseInt(d)*60+parseInt(f);return i>=b&&i<=c})},z=e=>I.some(t=>{const n=w(),i=n.findIndex(a=>a.time12===t.from),r=n.findIndex(a=>a.time12===t.until),l=n.findIndex(a=>a.time24===e);return l>=i&&l<=r}),U=e=>{if(!N||!B||B.length===0)return!0;const t=N.toLocaleDateString("en-US",{weekday:"long"}).toLowerCase(),n=B.find(r=>r.day===t);if(!n)return!1;const i=`${e}:00`;return n.timeslots.includes(i)},ee=e=>{var c,P,Z,q;if(!U(e.time24)||!_(e.time24)||T&&z(e.time24))return;const t=w(),n=t.findIndex(m=>m.time24===e.time24);if(o.includes(e.time24)){if(o.length>=2){const m=[...o].sort();if(e.time24===m[0]||e.time24===m[m.length-1]){const S=o.filter(g=>g!==e.time24);u(S);const p=[...S].sort(),E=(c=t.find(g=>g.time24===p[0]))==null?void 0:c.time12;let G;const ie=t.findIndex(g=>g.time24===p[p.length-1]),R=t[ie+1];G=(R==null?void 0:R.time12)||((P=t.find(g=>g.time24===p[p.length-1]))==null?void 0:P.time12),M({from:E,until:G})}}else o.length===1&&(u([]),M({from:"",until:""}));return}if(o.length===0)u([e.time24]);else{const m=[...o].sort(),S=m[m.length-1],p=t.findIndex(E=>E.time24===S);Math.abs(n-p)===1?u([...o,e.time24]):u([e.time24])}const r=[...o.length===0?[e.time24]:[...o,e.time24]].sort(),l=(Z=t.find(m=>m.time24===r[0]))==null?void 0:Z.time12;let a;const d=t.findIndex(m=>m.time24===r[r.length-1]),f=t[d+1];a=(f==null?void 0:f.time12)||((q=t.find(m=>m.time24===r[r.length-1]))==null?void 0:q.time12),M({from:l,until:a})},te=()=>{var f,b;if(o.length===0)return;const e=w(),t=[...o].sort(),n=(f=e.find(c=>c.time24===t[0]))==null?void 0:f.time12;let i;const r=e.findIndex(c=>c.time24===t[t.length-1]),l=e[r+1];i=(l==null?void 0:l.time12)||((b=e.find(c=>c.time24===t[t.length-1]))==null?void 0:b.time12);const a={from:n,until:i},d=[...I,a];A(d),u([]),y==null||y(d)},se=e=>{const t=I.filter((n,i)=>i!==e);A(t),y==null||y(t)},ne=e=>o.includes(e),v=w();return s.jsxs("div",{className:`rounded-lg bg-white p-4 shadow-5 ${Q} ${W}`,children:[N&&s.jsx("p",{className:"text-center font-medium",children:oe(N,"EEEE, MMMM d, yyyy")}),Y&&s.jsxs("div",{className:"mb-3 mt-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:["Minimum booking time: ",X," minutes"]}),s.jsx("div",{ref:O,className:"scrollbar-hide mb-5 mt-2 flex h-full flex-col gap-2 overflow-y-auto",style:{maxHeight:`${D}px`},children:v.map((e,t)=>{const n=_(e.time24),i=U(e.time24),r=T&&z(e.time24),l=i&&n&&!r,a=n?i?"":"Coach not available":"Club Closed";return s.jsxs("button",{onClick:()=>ee(e),disabled:!l,"data-tooltip-id":`time-${t}`,"data-tooltip-content":a,type:"button",className:`
                rounded-lg border-[1.5px] px-4 py-2 text-sm font-medium transition-colors
                ${l?ne(e.time24)?"border-[1.5px] border-primaryBlue bg-primaryBlue/10 text-primaryBlue":"border-gray-200 text-gray-500 hover:bg-gray-50":"cursor-not-allowed border-gray-200 bg-gray-100 text-gray-400"}
              `,children:[e.time12,!l&&s.jsx(re,{id:`time-${t}`,place:"top",className:"z-50 !bg-gray-900 !px-2 !py-1 !text-xs !text-white"})]},e.time24)})}),o.length>0&&s.jsxs("div",{className:"space-y-2 border-t border-gray-200 pt-4",children:[s.jsxs("div",{className:"flex items-center justify-center gap-2 text-sm",children:[s.jsx("span",{className:"font-medium",children:"From: "}),s.jsx("span",{className:"text-primaryBlue",children:(F=v.find(e=>e.time24===o.sort()[0]))==null?void 0:F.time12}),s.jsx("span",{className:"font-medium",children:"Until: "}),s.jsx("span",{className:"text-primaryBlue",children:(()=>{var i;const e=[...o].sort(),t=v.findIndex(r=>r.time24===e[e.length-1]),n=v[t+1];return(n==null?void 0:n.time12)||((i=v.find(r=>r.time24===e[e.length-1]))==null?void 0:i.time12)})()})]}),T&&s.jsx(J,{className:"mt-2 w-full rounded-lg border border-primaryBlue bg-primaryBlue/10 px-4 py-2 text-primaryBlue hover:bg-primaryBlue/20",onClick:te,disabled:o.length===0,children:"Add Time Range"})]}),T&&I.length>0&&s.jsxs("div",{className:"mt-4 space-y-2 border-t border-gray-200 pt-4",children:[s.jsx("p",{className:"text-center font-medium",children:"Selected Time Ranges"}),s.jsx("div",{className:"flex flex-col justify-center gap-2 ",children:I.map((e,t)=>s.jsxs("div",{className:"grid grid-cols-[auto_auto_auto_auto_auto] items-center gap-2 rounded-lg px-3 py-1 text-sm",children:[s.jsx("span",{className:"text-gray-500",children:"From"}),s.jsx("span",{children:e.from}),s.jsx("span",{children:"-"}),s.jsx("span",{className:"text-gray-500",children:"Until"}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("span",{children:e.until}),s.jsx("button",{onClick:()=>se(t),className:"text-primaryBlue hover:text-primaryBlue/80",children:s.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M15 9L9 15M15 15L9 9M21.25 12C21.25 17.1086 17.1086 21.25 12 21.25C6.89137 21.25 2.75 17.1086 2.75 12C2.75 6.89137 6.89137 2.75 12 2.75C17.1086 2.75 21.25 6.89137 21.25 12Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round"})})})]})]},t))})]}),k&&s.jsx("div",{className:"sticky bottom-0 bg-white pt-2",children:s.jsx(J,{className:"mt-2 w-full rounded-lg bg-primaryBlue px-4 py-2 text-white disabled:opacity-50",onClick:k,disabled:T?I.length===0:o.length===0,loading:V,children:K})})]})},he=le;export{he as T};

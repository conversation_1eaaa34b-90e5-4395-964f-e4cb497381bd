import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as a,f as Se,L as Re}from"./vendor-851db8c1.js";import{a3 as se,G as re,A as ne,R as xe,d as oe,M as le,b as F,u as de,am as _e,ab as be,a5 as ae,aE as pe,ar as he,aJ as Te,aC as ke,e as Me,T as Le,t as ve}from"./index-f915b394.js";import{o as De,g as Pe,f as Be}from"./date-fns-07266b7d.js";import{T as Oe}from"./TimeSlots-683a5ba6.js";import{C as Ee}from"./CalendarIcon-b3488133.js";import{C as $e}from"./Calendar-9031b5fe.js";import{h as Fe}from"./moment-a9aaa855.js";import{f as Ae}from"./index.esm-b72032a7.js";import{u as Ie,C as Q}from"./react-hook-form-687afde5.js";import{c as Ue,a as ee,f as ze,e as Ne}from"./yup-54691517.js";import{o as He}from"./yup-2824f222.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-tooltip-7a26650a.js";import"./ChevronLeftIcon-e5eecf9c.js";import"./ChevronRightIcon-efb4c46c.js";import"./@hookform/resolvers-67648cca.js";function We({title:r,titleId:u,...s},_){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:_,"aria-labelledby":u},s),r?a.createElement("title",{id:u},r):null,a.createElement("path",{fillRule:"evenodd",d:"M16.28 11.47a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 0 1-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 0 1 1.06-1.06l7.5 7.5Z",clipRule:"evenodd"}))}const Je=a.forwardRef(We),Ze=Je;function qe({title:r,titleId:u,...s},_){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:_,"aria-labelledby":u},s),r?a.createElement("title",{id:u},r):null,a.createElement("path",{fillRule:"evenodd",d:"M5.47 5.47a.75.75 0 0 1 1.06 0L12 10.94l5.47-5.47a.75.75 0 1 1 1.06 1.06L13.06 12l5.47 5.47a.75.75 0 1 1-1.06 1.06L12 13.06l-5.47 5.47a.75.75 0 0 1-1.06-1.06L10.94 12 5.47 6.53a.75.75 0 0 1 0-1.06Z",clipRule:"evenodd"}))}const Ve=a.forwardRef(qe),Ge=Ve;function ue({buddy:r,onSelect:u,clubSports:s}){var _;return e.jsx("button",{onClick:()=>u(r),className:"group w-full cursor-pointer rounded-xl bg-gray-50 p-3 transition-all duration-200 hover:scale-[1.01] hover:bg-gray-100 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:p-4",children:e.jsxs("div",{className:"flex gap-3 sm:gap-4",children:[e.jsx("div",{className:"w-20 flex-shrink-0 sm:w-24 md:w-32",children:e.jsx("img",{src:(r==null?void 0:r.owner_photo)||"/default-avatar.png",alt:`${r==null?void 0:r.owner_first_name} ${r==null?void 0:r.owner_last_name}`,className:"aspect-square w-full rounded-full object-cover shadow-sm",loading:"lazy"})}),e.jsxs("div",{className:"flex min-w-0 flex-1 flex-col",children:[e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"min-w-0",children:[e.jsxs("p",{className:"mb-2 break-words text-sm sm:text-base",children:[(()=>{const v=new Date(r.date),x=v.toLocaleDateString("en-US",{weekday:"long"}),h=v.toLocaleDateString("en-US",{month:"short",day:"numeric"});return De(v)?`Today (${x})`:`${x}, ${h}`})()," ","• ",se(r.start_time)," -"," ",se(r.end_time)]}),e.jsxs("div",{className:"mb-2 flex flex-wrap items-center text-xs text-gray-500 sm:text-sm",children:[e.jsxs("span",{children:["Added ",Pe(new Date(r==null?void 0:r.create_at))," ago"," "]})," ","by",e.jsxs("span",{className:"ml-1 capitalize text-gray-700",children:[r==null?void 0:r.owner_first_name," ",r==null?void 0:r.owner_last_name]})]})]}),e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"mt-1 flex-shrink-0 self-start text-gray-400 transition-transform duration-200 group-hover:translate-x-1 sm:mt-0 sm:h-5 sm:w-5",children:e.jsx("path",{d:"M4.16669 10H15.8334M15.8334 10L10 4.16669M15.8334 10L10 15.8334",stroke:"currentColor",strokeWidth:"1.67",strokeLinecap:"round",strokeLinejoin:"round"})})]}),e.jsxs("div",{className:"mt-auto flex flex-wrap items-center gap-1",children:[((r==null?void 0:r.sport_id)||(r==null?void 0:r.sport_name))&&e.jsx("span",{className:"mb-1 rounded-full border bg-white px-2 py-1 text-xs text-gray-600",children:(r==null?void 0:r.sport_name)||((_=s.find(v=>v.id===(r==null?void 0:r.sport_id)))==null?void 0:_.name)}),e.jsxs("span",{className:"mb-1 rounded-full border bg-white px-2 py-1 text-xs text-gray-600",children:["NTRP: ",r.ntrp,r.max_ntrp?` - ${r.max_ntrp}`:""]}),e.jsxs("span",{className:"mb-1 flex items-center justify-center gap-1 rounded-full border bg-white px-2 py-1 text-xs text-gray-600",children:[e.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M7.99998 1.33301C6.34313 1.33301 4.99998 2.67615 4.99998 4.33301C4.99998 5.98986 6.34313 7.33301 7.99998 7.33301C9.65683 7.33301 11 5.98986 11 4.33301C11 2.67615 9.65683 1.33301 7.99998 1.33301Z",fill:"#525866"}),e.jsx("path",{d:"M8.00025 8.33301C5.21805 8.33301 3.18228 10.1954 2.62678 12.6672C2.46036 13.4077 3.06674 13.9997 3.73252 13.9997H12.268C12.9338 13.9997 13.5401 13.4077 13.3737 12.6672C12.8182 10.1954 10.7824 8.33301 8.00025 8.33301Z",fill:"#525866"})]}),r.num_players,"/",r.num_needed]})]})]})]})},r.buddy_id||r.id)}let ye=new le;function ge({isOpen:r,onClose:u,buddy:s,onRequestJoin:_,onReserveCourt:v,fetchData:x,setSelectedBuddy:h,clubSports:C}){var j,D,b,I,T,l;const{dispatch:y}=a.useContext(re);a.useContext(ne),Se(),a.useState(!1);const[f,N]=a.useState("details"),[P,L]=a.useState(!1),[B,H]=a.useState(s==null?void 0:s.date),[i,w]=a.useState(!1),[d,R]=a.useState(!1),[U,k]=a.useState(null),[S,Z]=a.useState([{from:s==null?void 0:s.start_time,until:s==null?void 0:s.end_time}]),A=async()=>{try{const t=await ye.callRawAPI(`/v3/api/custom/courtmatchup/user/buddy/${s.buddy_id||s.id}`,"PUT",{date:B,start_time:S.from,end_time:S.until});F(y,"Date updated successfully",3e3,"success"),x()}catch(t){console.error("Error updating date:",t),F(y,"Error updating date",3e3,"error")}finally{w(!1)}},V=t=>{Z([{from:t.from,until:t.until}])};if(!s)return null;const z=(j=s==null?void 0:s.requests_to_join)==null?void 0:j.filter(t=>t.request_status==0||t.request_status==4),O=(D=s==null?void 0:s.requests_to_join)==null?void 0:D.filter(t=>t.request_status==1),W=(b=s==null?void 0:s.requests_to_join)==null?void 0:b.filter(t=>t.request_status==2);(I=s==null?void 0:s.requests_to_join)==null||I.filter(t=>t.request_status==4);const J=async(t,p)=>{const g=s.requests_to_join.find(E=>E.request_id===t);if(p===0){k({requestId:t,status:p,userName:`${g.first_name} ${g.last_name}`}),R(!0);return}await q(t,p)},q=async(t,p)=>{w(!0);try{if(!(await ye.callRawAPI("/v3/api/custom/courtmatchup/user/buddy/update-request",{request_id:t,status:p},"POST")).error){x(),F(y,"Request status updated successfully",3e3,"success");const E=s.requests_to_join.map($=>$.request_id===t?{...$,request_status:p}:$);h({...s,requests_to_join:E})}}catch(g){console.error(g),F(y,g.message,3e3,"error")}finally{w(!1),R(!1),k(null)}};return e.jsxs(e.Fragment,{children:[e.jsxs(xe,{isOpen:r,onClose:u,title:"Request details",showFooter:!1,className:"!p-0",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"px-5 pt-5",children:e.jsxs("div",{className:"flex w-full justify-between gap-2 rounded-xl bg-gray-100 p-1",children:[e.jsx("button",{onClick:()=>N("details"),className:`flex w-full items-center justify-center rounded-lg border border-transparent bg-transparent px-4 py-2 text-sm font-medium ${f==="details"?"border-gray-200 bg-white":""}`,children:"Details"}),e.jsxs("button",{onClick:()=>N("requests"),className:`flex w-full items-center justify-center rounded-lg border border-transparent bg-transparent px-4 py-2 text-sm font-medium ${f==="requests"?"border-gray-200 bg-white":""}`,children:["Requests (",(T=s==null?void 0:s.requests_to_join)==null?void 0:T.length,")"]})]})}),f==="details"?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"px-5",children:e.jsxs("div",{className:"mb-4",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"REQUEST MADE"}),e.jsxs("p",{className:"mt-1 font-medium",children:[new Date(s==null?void 0:s.create_at).toLocaleDateString("en-US",{weekday:"long",month:"short",day:"numeric"})," • ",se(s==null?void 0:s.start_time)," -"," ",se(s==null?void 0:s.end_time)]})]})}),e.jsx("div",{className:"px-5",children:e.jsxs("div",{className:"space-y-4 divide-y",children:[e.jsxs("div",{className:"py-4",children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"DATE & TIME"}),e.jsxs("button",{onClick:()=>L(!0),className:"flex items-center gap-2 rounded-lg border px-2 py-1 text-sm text-gray-500 hover:bg-gray-50",children:[e.jsx(Ee,{className:"h-5 w-5"}),"Select new date"]}),P&&e.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50",children:e.jsxs("div",{className:"w-full max-w-md rounded-xl bg-white p-4",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Select new date and time"}),e.jsx("button",{onClick:()=>L(!1),className:"rounded-lg p-1 hover:bg-gray-100",children:e.jsx(Ge,{className:"h-5 w-5 text-gray-500"})})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Date"}),e.jsx("input",{type:"date",value:B,onChange:t=>H(t.target.value),className:"mt-1 block w-full rounded-lg border border-gray-300 px-3 py-2 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Time slot"}),e.jsx("div",{className:"mt-2",children:e.jsx(Oe,{selectedDate:B,timeRange:S,onTimeClick:V,isTimeSlotAvailable:t=>!0,startHour:0,endHour:23,interval:30})})]}),e.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[e.jsx("button",{onClick:()=>L(!1),className:"rounded-lg border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{onClick:A,className:"rounded-lg bg-[#1B254B] px-4 py-2 text-sm font-medium text-white hover:bg-blue-900",children:"Save changes"})]})]})]})})]}),e.jsxs("p",{className:"mt-1 font-medium",children:[new Date(s==null?void 0:s.date).toLocaleDateString("en-US",{weekday:"long",month:"short",day:"numeric"})," • ",se(s==null?void 0:s.start_time)," -"," ",se(s==null?void 0:s.end_time)]})]}),e.jsxs("div",{className:"py-4",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"SPORT & TYPE"}),e.jsxs("p",{className:"mt-1 font-medium",children:[(s==null?void 0:s.sport_name)||((l=C==null?void 0:C.find(t=>t.id===(s==null?void 0:s.sport_id)))==null?void 0:l.name)," ",(s==null?void 0:s.type)&&`• ${s==null?void 0:s.type}`," ",(s==null?void 0:s.sub_type)&&`• ${s==null?void 0:s.sub_type}`]})]}),e.jsxs("div",{className:"py-4",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"NTRP"}),e.jsxs("p",{className:"mt-1 font-medium",children:[s==null?void 0:s.ntrp,s!=null&&s.max_ntrp?` - ${s==null?void 0:s.max_ntrp}`:""]})]}),e.jsxs("div",{className:"py-4",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"LOOKING FOR PLAYERS"}),e.jsxs("p",{className:"mt-1 font-medium",children:[s==null?void 0:s.num_players,"/",s==null?void 0:s.num_needed]})]}),e.jsxs("div",{className:"py-4",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"GROUP BIO"}),e.jsx("p",{className:"mt-1 text-sm",children:s!=null&&s.notes?s==null?void 0:s.notes:"No bio provided"})]})]})})]}):e.jsx("div",{className:"px-5",children:e.jsxs("div",{className:"space-y-6",children:[z.length>0?e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:"Pending"}),e.jsx("div",{className:"mt-4 space-y-4",children:z.map((t,p)=>e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"h-10 w-10 overflow-hidden rounded-full bg-gray-200",children:e.jsx("img",{src:t==null?void 0:t.photo,alt:"Arthur Taylor",className:"h-full w-full object-cover"})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("p",{className:"font-medium",children:[t==null?void 0:t.first_name," ",t==null?void 0:t.last_name]}),e.jsx("p",{className:"text-sm text-gray-500",children:t==null?void 0:t.email})]}),e.jsx("div",{children:e.jsxs("p",{className:"text-sm text-gray-500",children:["NTRP: ",t==null?void 0:t.ntrp]})})]}),e.jsxs("div",{className:"mt-3 flex gap-2",children:[e.jsx(oe,{onClick:()=>{J(t==null?void 0:t.request_id,1)},loading:i,className:"flex-1 rounded-lg border border-green-500 bg-green-50 py-2 text-sm font-medium text-green-600",children:e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("svg",{width:"17",height:"16",viewBox:"0 0 17 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.08716 14C4.77336 14 2.08716 11.3138 2.08716 8C2.08716 4.6862 4.77336 2 8.08716 2C11.401 2 14.0872 4.6862 14.0872 8C14.0872 11.3138 11.401 14 8.08716 14ZM7.48896 10.4L11.731 6.1574L10.8826 5.309L7.48896 8.7032L5.79156 7.0058L4.94316 7.8542L7.48896 10.4Z",fill:"#38C793"})}),e.jsx("span",{children:"Accept"})]})}),e.jsx(oe,{onClick:()=>{J(t==null?void 0:t.request_id,2)},loading:i,className:"flex-1 rounded-lg border border-red-500 bg-red-50 py-2 text-sm font-medium text-red-600",children:e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("svg",{width:"17",height:"16",viewBox:"0 0 17 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.28491 8.00065C2.28491 4.31875 5.26968 1.33398 8.95158 1.33398C12.6335 1.33398 15.6182 4.31875 15.6182 8.00065C15.6182 11.6825 12.6335 14.6673 8.95158 14.6673C5.26968 14.6673 2.28491 11.6825 2.28491 8.00065ZM7.30513 5.6471C7.10987 5.45184 6.79329 5.45184 6.59803 5.6471C6.40276 5.84236 6.40276 6.15894 6.59803 6.3542L8.24447 8.00065L6.59803 9.6471C6.40276 9.84236 6.40276 10.1589 6.59803 10.3542C6.79329 10.5495 7.10987 10.5495 7.30513 10.3542L8.95158 8.70776L10.598 10.3542C10.7933 10.5495 11.1099 10.5495 11.3051 10.3542C11.5004 10.1589 11.5004 9.84236 11.3051 9.6471L9.65869 8.00065L11.3051 6.3542C11.5004 6.15894 11.5004 5.84236 11.3051 5.6471C11.1099 5.45184 10.7933 5.45184 10.598 5.6471L8.95158 7.29354L7.30513 5.6471Z",fill:"#DF1C41"})}),e.jsx("span",{children:"Decline"})]})})]})]},p))})]}):null,O.length>0&&e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:"Accepted"}),e.jsx("div",{className:"mt-4 space-y-4",children:O.map((t,p)=>e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"h-10 w-10 overflow-hidden rounded-full bg-gray-200",children:e.jsx("img",{src:(t==null?void 0:t.photo)||"/default-avatar.png",alt:`${t==null?void 0:t.first_name} ${t==null?void 0:t.last_name}`,className:"h-full w-full object-cover"})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("p",{className:"font-medium",children:[t==null?void 0:t.first_name," ",t==null?void 0:t.last_name]}),e.jsx("p",{className:"text-sm text-gray-500",children:t==null?void 0:t.email})]}),e.jsx("div",{children:e.jsxs("p",{className:"text-sm text-gray-500",children:["NTRP: ",t==null?void 0:t.ntrp]})})]}),e.jsxs("div",{className:"mt-3 flex items-center gap-3",children:[e.jsxs("span",{className:"flex items-center gap-1 rounded-lg border border-gray-200 bg-white px-2 py-1 text-sm text-gray-500",children:[e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.00004 1.33398C4.31814 1.33398 1.33337 4.31875 1.33337 8.00065C1.33337 11.6825 4.31814 14.6673 8.00004 14.6673C11.6819 14.6673 14.6667 11.6825 14.6667 8.00065C14.6667 4.31875 11.6819 1.33398 8.00004 1.33398ZM10.387 6.6506C10.5619 6.43688 10.5304 6.12187 10.3167 5.94701C10.1029 5.77214 9.78793 5.80364 9.61306 6.01737L6.96292 9.25643L6.02026 8.31376C5.825 8.1185 5.50842 8.1185 5.31315 8.31376C5.11789 8.50903 5.11789 8.82561 5.31315 9.02087L6.64649 10.3542C6.74638 10.4541 6.88386 10.5071 7.02495 10.5C7.16604 10.493 7.29757 10.4266 7.38702 10.3173L10.387 6.6506Z",fill:"#868C98"})}),"Accepted"]}),e.jsx("button",{onClick:()=>{J(t==null?void 0:t.request_id,0)},className:"text-sm text-gray-500 hover:text-gray-900",children:"Undo"})]})]},p))})]}),W.length>0&&e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:"Declined"}),e.jsx("div",{className:"mt-4 space-y-4",children:W.map((t,p)=>e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"h-10 w-10 overflow-hidden rounded-full bg-gray-200",children:e.jsx("img",{src:(t==null?void 0:t.photo)||"/default-avatar.png",alt:`${t==null?void 0:t.first_name} ${t==null?void 0:t.last_name}`,className:"h-full w-full object-cover"})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("p",{className:"font-medium",children:[t==null?void 0:t.first_name," ",t==null?void 0:t.last_name]}),e.jsx("p",{className:"text-sm text-gray-500",children:t==null?void 0:t.email})]}),e.jsx("div",{children:e.jsxs("p",{className:"text-sm text-gray-500",children:["NTRP: ",t==null?void 0:t.ntrp]})})]}),e.jsxs("div",{className:"mt-3 flex items-center gap-3",children:[e.jsxs("span",{className:"text-sm text-gray-500",children:[e.jsx("span",{className:"mr-1 inline-block h-2 w-2 rounded-full bg-gray-400"}),"Declined"]}),e.jsx("button",{onClick:()=>{J(t==null?void 0:t.request_id,4)},className:"text-sm text-gray-500 hover:text-gray-900",children:"Undo"})]})]},p))})]})]})})]}),e.jsx("div",{className:"fixed bottom-0 w-full border-t border-gray-200 bg-white px-5 py-4",children:e.jsx("button",{onClick:()=>v(s),className:"w-full rounded-xl bg-[#1B254B] py-3 text-center font-medium text-white hover:bg-blue-900",children:"Reserve court"})})]}),d&&U&&e.jsx("div",{className:"fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50",children:e.jsxs("div",{className:"w-full max-w-md rounded-xl bg-white p-6",children:[e.jsx("h3",{className:"mb-4 text-xl font-semibold",children:"Undo Declined"}),e.jsxs("p",{className:"mb-6 text-gray-600",children:["Are you sure you want to undo decline for request from"," ",U.userName,"?"]}),e.jsx("p",{className:"mb-6 text-gray-600",children:"It will go back to Pending status."}),e.jsxs("div",{className:"flex justify-end gap-3",children:[e.jsx("button",{onClick:()=>{R(!1),k(null)},className:"rounded-lg border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{onClick:()=>q(U.requestId,U.status),className:"rounded-lg bg-[#1B254B] px-4 py-2 text-sm font-medium text-white hover:bg-blue-900",children:"Yes, undo decline"})]})]})})]})}const Ye=({isOpen:r,onClose:u,onRequestJoin:s,loading:_=!1})=>{const{user_subscription:v,user_permissions:x}=de(),[h,C]=a.useState({isOpen:!1,title:"",message:"",actionButtonText:"",actionButtonLink:"",type:"warning"}),y=()=>{if(!(v!=null&&v.planId)){C({isOpen:!0,title:"Subscription Required",message:"Please subscribe to a membership plan to join buddy requests",actionButtonText:"View Membership Plans",actionButtonLink:"/user/membership/buy",type:"warning"});return}if(!(x!=null&&x.allowBuddy)){C({isOpen:!0,title:"Plan Upgrade Required",message:`Your current plan (${x==null?void 0:x.planName}) does not include the Find a Buddy feature. Please upgrade your plan.`,actionButtonText:"Upgrade Plan",actionButtonLink:"/user/membership/buy",type:"error"});return}s()};return e.jsxs(e.Fragment,{children:[e.jsx(_e,{isOpen:h.isOpen,onClose:()=>C({...h,isOpen:!1}),title:h.title,message:h.message,actionButtonText:h.actionButtonText,actionButtonLink:h.actionButtonLink,type:h.type}),e.jsxs("div",{className:`fixed inset-0 z-50 flex items-center justify-center ${r?"":"hidden"}`,children:[e.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),e.jsxs("div",{className:"relative z-50 w-full max-w-md rounded-3xl bg-white",children:[e.jsxs("div",{className:"p-6",children:[e.jsx("h2",{className:"mb-4 text-xl font-medium",children:"Request to join"}),e.jsxs("div",{className:"flex items-start justify-center rounded-xl bg-[#F17B2C] p-3",children:[e.jsx("div",{className:"mr-2",children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.49364 2.62127L13.9236 12.1348C13.9737 12.2225 14 12.3219 14 12.4231C14 12.5243 13.9737 12.6237 13.9236 12.7114C13.8736 12.799 13.8017 12.8718 13.715 12.9224C13.6283 12.973 13.5301 12.9997 13.43 12.9997H2.57C2.46995 12.9997 2.37165 12.973 2.285 12.9224C2.19835 12.8718 2.12639 12.799 2.07636 12.7114C2.02634 12.6237 2 12.5243 2 12.4231C2 12.3219 2.02634 12.2225 2.07637 12.1348L7.50636 2.62127C7.5564 2.53363 7.62835 2.46085 7.715 2.41025C7.80165 2.35965 7.89995 2.33301 8 2.33301C8.10005 2.33301 8.19835 2.35965 8.285 2.41025C8.37165 2.46085 8.4436 2.53363 8.49364 2.62127ZM7.42998 10.1168V11.2699H8.57002V10.1168H7.42998ZM7.42998 6.08074V8.96363H8.57002V6.08074H7.42998Z",fill:"white"})})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-white",children:"IMPORTANT"}),e.jsx("p",{className:"text-sm text-white",children:"By sending request you agree to share your contact information so the request owner can get in touch with you."})]})]})]}),e.jsxs("div",{className:"flex justify-end gap-3 border-t px-6 py-4",children:[e.jsx("button",{onClick:u,className:"rounded-xl border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"}),e.jsx(oe,{onClick:y,className:"rounded-xl bg-green-800 px-4 py-2 text-white hover:bg-green-700",loading:_,children:"Send request"})]})]})]})]})},fe=Ye;new le;function je({isOpen:r,onClose:u,buddy:s,onRequestJoin:_,onReserveCourt:v,clubSports:x,fetchData:h}){var w;const{dispatch:C}=a.useContext(re),{dispatch:y}=a.useContext(ne),[f,N]=a.useState([]);Se();const[P,L]=a.useState(!1),[B,H]=a.useState("details");async function i(d){try{let R=[];if(d!=null&&d.player_ids&&(R=typeof d.player_ids=="string"?JSON.parse(d.player_ids):d.player_ids),R.length>0){const U=await be(C,y,"user",R,"user|user_id");N(U.list)}else d!=null&&d.player_details&&N(d.player_details)}catch(R){console.error(R)}}return a.useEffect(()=>{i(s)},[s]),s?(console.log(" personal request buddy",s),e.jsx(xe,{isOpen:r,onClose:u,title:"Request details",showFooter:!1,className:"!p-0",children:e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:" px-5 pt-5",children:e.jsx("div",{className:"flex w-full justify-between gap-2 rounded-xl bg-gray-100 p-1",children:e.jsx("button",{onClick:()=>H("details"),className:`flex w-full items-center justify-center rounded-lg border border-transparent bg-transparent  px-4 py-2 text-sm font-medium ${B==="details"?"border-gray-200 bg-white":""}`,children:"Details"})})}),e.jsx("div",{className:"bg-gray-100 px-5 py-1 ",children:e.jsx("p",{className:"text-sm text-gray-500",children:"REQUESTED BY"})}),e.jsxs("div",{className:"px-5 py-1",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"h-10 w-10 overflow-hidden rounded-full",children:e.jsx("img",{src:(s==null?void 0:s.owner_photo)||"/default-avatar.png",alt:`${s==null?void 0:s.owner_first_name} ${s==null?void 0:s.owner_last_name}`,className:"h-full w-full object-cover"})}),e.jsxs("div",{children:[e.jsxs("p",{className:"font-medium capitalize",children:[s==null?void 0:s.owner_first_name," ",s==null?void 0:s.owner_last_name]}),e.jsxs("p",{className:"text-sm text-gray-500",children:["NTRP: ",s==null?void 0:s.ntrp]})]})]}),e.jsxs("div",{className:"mt-4 rounded-xl bg-gray-100",children:[e.jsxs("button",{onClick:()=>L(!P),className:"flex w-full items-center gap-2 px-4 py-3",children:[e.jsx(Ze,{className:`h-5 w-5 transform transition-transform ${P?"rotate-90":""}`}),e.jsxs("span",{className:"text-sm font-medium",children:["Others(",f.length,")"]})]}),P&&e.jsx("div",{className:"px-4 pb-3",children:f.map((d,R)=>e.jsxs("div",{className:"mb-3 flex items-center gap-4",children:[e.jsx("div",{className:"h-10 w-10 overflow-hidden rounded-full",children:e.jsx("img",{src:(d==null?void 0:d.photo)||"/default-avatar.png",alt:`${d==null?void 0:d.first_name} ${d==null?void 0:d.last_name}`,className:"h-full w-full object-cover"})}),e.jsxs("div",{children:[e.jsxs("p",{className:"",children:[d==null?void 0:d.first_name," ",d==null?void 0:d.last_name]}),e.jsxs("p",{className:"text-sm text-gray-600",children:["NTRP: ",d==null?void 0:d.ntrp]})]})]},R))})]})]}),e.jsx("div",{className:"bg-gray-100 px-5 py-1 ",children:e.jsx("p",{className:"text-sm text-gray-500",children:" DETAILS"})}),e.jsxs("div",{className:"space-y-0 divide-y px-5 py-1",children:[e.jsxs("div",{className:"py-4",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"DATE & TIME"}),e.jsxs("p",{className:"mt-1 font-medium",children:[new Date(s==null?void 0:s.date).toLocaleDateString("en-US",{weekday:"long",month:"long",day:"numeric"})," • ",se(s==null?void 0:s.start_time)," -"," ",se(s==null?void 0:s.end_time)]})]}),e.jsxs("div",{className:"py-4",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"SPORT & TYPE"}),e.jsxs("p",{className:"mt-1 font-medium capitalize",children:[(s==null?void 0:s.sport_name)||((w=x==null?void 0:x.find(d=>d.id===(s==null?void 0:s.sport_id)))==null?void 0:w.name)," ",(s==null?void 0:s.type)&&`• ${s==null?void 0:s.type}`," ",(s==null?void 0:s.sub_type)&&`• ${s==null?void 0:s.sub_type}`]})]}),e.jsxs("div",{className:"py-4",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"LOOKING FOR PLAYERS"}),e.jsxs("p",{className:"mt-1 font-medium",children:[s==null?void 0:s.num_players,"/",s==null?void 0:s.num_needed]})]}),e.jsxs("div",{className:"py-4",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"NTRP Range"}),e.jsxs("p",{className:"mt-1 font-medium",children:[s==null?void 0:s.ntrp,s!=null&&s.max_ntrp?` - ${s==null?void 0:s.max_ntrp}`:""]})]}),e.jsxs("div",{className:"py-4",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"GROUP INFO"}),e.jsx("p",{className:"mt-1 text-sm",children:(s==null?void 0:s.notes)||"No description provided"})]})]}),e.jsx("div",{className:"fixed bottom-0 w-full border-t border-gray-200 bg-white px-10 py-5",children:e.jsx("button",{onClick:()=>{v(s)},className:"flex w-full items-center justify-center gap-2 rounded-xl bg-[#1B254B] py-3 text-center text-white hover:bg-blue-900",children:e.jsx("span",{children:"Reserve court"})})})]})})):null}const Ke=({isOpen:r,onClose:u,onReserveCourt:s,loading:_=!1})=>{const{user_subscription:v,user_permissions:x}=de(),[h,C]=a.useState({isOpen:!1,title:"",message:"",actionButtonText:"",actionButtonLink:"",type:"warning"}),y=()=>{if(!(v!=null&&v.planId)){C({isOpen:!0,title:"Subscription Required",message:"Please subscribe to a membership plan to join buddy requests",actionButtonText:"View Membership Plans",actionButtonLink:"/user/membership/buy",type:"warning"});return}if(!(x!=null&&x.allowBuddy)){C({isOpen:!0,title:"Plan Upgrade Required",message:`Your current plan (${x==null?void 0:x.planName}) does not include the Find a Buddy feature. Please upgrade your plan.`,actionButtonText:"Upgrade Plan",actionButtonLink:"/user/membership/buy",type:"error"});return}s()};return e.jsxs(e.Fragment,{children:[e.jsx(_e,{isOpen:h.isOpen,onClose:()=>C({...h,isOpen:!1}),title:h.title,message:h.message,actionButtonText:h.actionButtonText,actionButtonLink:h.actionButtonLink,type:h.type}),e.jsxs("div",{className:`fixed inset-0 z-[9999] flex items-center justify-center ${r?"":"hidden"}`,children:[e.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),e.jsxs("div",{className:"relative z-50 w-full max-w-md rounded-3xl bg-white",children:[e.jsxs("div",{className:"p-6",children:[e.jsx("h2",{className:"mb-4 text-xl font-medium",children:"Reserve court"}),e.jsxs("div",{className:"flex items-start justify-center rounded-xl bg-[#F17B2C] p-3",children:[e.jsx("div",{className:"mr-2",children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.49364 2.62127L13.9236 12.1348C13.9737 12.2225 14 12.3219 14 12.4231C14 12.5243 13.9737 12.6237 13.9236 12.7114C13.8736 12.799 13.8017 12.8718 13.715 12.9224C13.6283 12.973 13.5301 12.9997 13.43 12.9997H2.57C2.46995 12.9997 2.37165 12.973 2.285 12.9224C2.19835 12.8718 2.12639 12.799 2.07636 12.7114C2.02634 12.6237 2 12.5243 2 12.4231C2 12.3219 2.02634 12.2225 2.07637 12.1348L7.50636 2.62127C7.5564 2.53363 7.62835 2.46085 7.715 2.41025C7.80165 2.35965 7.89995 2.33301 8 2.33301C8.10005 2.33301 8.19835 2.35965 8.285 2.41025C8.37165 2.46085 8.4436 2.53363 8.49364 2.62127ZM7.42998 10.1168V11.2699H8.57002V10.1168H7.42998ZM7.42998 6.08074V8.96363H8.57002V6.08074H7.42998Z",fill:"white"})})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-white",children:"IMPORTANT"}),e.jsx("p",{className:"text-sm text-white",children:"By reserving a court, you agree to share your contact information so the request owner can get in touch with you."})]})]})]}),e.jsxs("div",{className:"flex justify-end gap-3 border-t px-6 py-4",children:[e.jsx("button",{onClick:u,className:"rounded-xl border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"}),e.jsx(oe,{onClick:y,className:"rounded-xl bg-green-800 px-4 py-2 text-white hover:bg-green-700",loading:_,children:"Reserve court"})]})]})]})]})},we=Ke;let ce=new le;function Qe({fetchData:r,buddyData:u,setBuddyData:s,clubSports:_,selectedRequestTab:v}){const[x,h]=a.useState("desc"),[C,y]=a.useState(!1),[f,N]=a.useState(null),[P,L]=a.useState(!1),[B,H]=a.useState(!1),{dispatch:i}=a.useContext(re);a.useContext(ne);const[w,d]=a.useState(null),[R,U]=a.useState(null),[k,S]=a.useState(!1),[Z,A]=a.useState(!1),[V,z]=a.useState({num_needed:null,ntrp:null,max_ntrp:null}),O=(t,p)=>{z(p==="all"?g=>{const E={...g};return delete E[t],E}:g=>({...g,[t]:parseInt(p)}))},W=t=>{h(t),y(!1)},q=(()=>{let t=u.filter(p=>{let g=!0;return Object.entries(V).forEach(([E,$])=>{$!==null&&(g=g&&p[E]===$)}),g});return t=[...t].sort((p,g)=>{const E=new Date(p.date),$=new Date(g.date);return x==="desc"?$-E:E-$}),t})(),j=t=>{L(!0),N(null),d(t)},D=t=>{S(!0),N(t)},b=async()=>{H(!0),console.log("buddyRequestedFor",w);try{(await ce.callRawAPI("/v3/api/custom/courtmatchup/user/buddy/join-team",{buddy_id:w.buddy_id||w.id,ntrp:R.ntrp,player_ids:[R==null?void 0:R.user_id],num_players:1},"POST")).error||(F(i,"Request sent successfully",5e3,"success"),L(!1))}catch(t){console.log(t),F(i,t.message,5e3,"error")}finally{H(!1)}},I=async()=>{A(!0),console.log("buddyRequestedFor",f);try{(await ce.callRawAPI("/v3/api/custom/courtmatchup/user/reservations",{buddy_request:he.court,buddy_id:f.buddy_id,reservation_type:1},"POST")).error||(F(i,"Reservation created successfully",5e3,"success"),S(!1))}catch(t){console.log(t),F(i,t.message,5e3,"error")}finally{A(!1)}},T=localStorage.getItem("user");async function l(){try{ce.setTable("profile");const t=await ce.callRestAPI({id:T},"GET");U(t.model)}catch(t){console.error(t)}}return a.useEffect(()=>{l()},[]),e.jsxs("div",{className:"max mx-auto mt-3 max-w-4xl bg-white p-3 sm:mt-5 sm:p-4",children:[e.jsxs("div",{className:"mb-4 flex flex-col gap-3 sm:mb-6 sm:flex-row sm:items-center sm:justify-between sm:gap-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("p",{className:"whitespace-nowrap text-sm sm:text-base",children:"Players needed"}),e.jsxs("select",{onChange:t=>O("num_needed",t.target.value),className:"rounded-xl border border-gray-200 bg-white py-1 text-xs sm:text-sm",children:[e.jsx("option",{value:"all",children:"Not set"}),e.jsx("option",{value:"1",children:"1"}),e.jsx("option",{value:"2",children:"2"}),e.jsx("option",{value:"3",children:"3"}),e.jsx("option",{value:"4",children:"4"}),e.jsx("option",{value:"5",children:"5"})]})]}),e.jsxs("div",{className:"flex flex-wrap items-center gap-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("select",{onChange:t=>O("ntrp",t.target.value),className:"rounded-xl border border-gray-200 bg-white py-1 text-xs sm:text-sm",children:[e.jsx("option",{value:"all",children:"Min NTRP"}),ae.map(t=>e.jsx("option",{value:t,children:t},t))]}),e.jsxs("select",{onChange:t=>O("max_ntrp",t.target.value),className:"rounded-xl border border-gray-200 bg-white py-1 text-xs sm:text-sm",children:[e.jsx("option",{value:"all",children:"Max NTRP"}),ae.map(t=>e.jsx("option",{value:t,children:t},t))]})]}),e.jsxs("div",{className:"relative sm:pl-2",children:[e.jsxs("button",{onClick:()=>y(!C),className:"flex items-center gap-1 rounded-xl border border-gray-200 bg-white px-2 py-1 text-xs sm:px-4 sm:py-2 sm:text-sm",children:[e.jsxs("span",{className:"whitespace-nowrap text-gray-700",children:["By date (",x==="desc"?"Latest":"Earliest",")"]}),e.jsx(pe,{size:16,className:`text-gray-400 transition-transform duration-200 ${C?"rotate-180":""}`})]}),C&&e.jsxs("div",{className:"absolute right-0 top-full z-10 mt-1 w-48 rounded-lg border border-gray-200 bg-white py-1 shadow-lg",children:[e.jsxs("button",{onClick:()=>W("desc"),className:`flex w-full items-center justify-between px-4 py-2 text-xs hover:bg-gray-50 sm:text-sm ${x==="desc"?"text-blue-600":"text-gray-700"}`,children:["Latest first",x==="desc"&&e.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})]}),e.jsxs("button",{onClick:()=>W("asc"),className:`flex w-full items-center justify-between px-4 py-2 text-xs hover:bg-gray-50 sm:text-sm ${x==="asc"?"text-blue-600":"text-gray-700"}`,children:["Earliest first",x==="asc"&&e.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})]})]})]})]})]}),e.jsx("div",{className:"max-h-[500px] space-y-3 overflow-y-auto p-1 sm:space-y-4 sm:p-2",children:q.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center rounded-lg bg-gray-50 py-8 sm:py-12",children:[e.jsx("div",{className:"mb-4 rounded-full bg-gray-100 p-3",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-gray-400",children:e.jsx("path",{d:"M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("h3",{className:"mb-1 px-4 text-center text-base font-medium text-gray-900 sm:text-lg",children:"No buddies found"})]}):q.map(t=>e.jsx(ue,{clubSports:_,buddy:t,onSelect:()=>N(t)},t.buddy_id||t.id))}),f&&v==="all-requests"&&parseInt((f==null?void 0:f.owner_user_id)||0)===parseInt(T)?e.jsx(je,{isOpen:f!==null,onClose:()=>N(null),buddy:f,onRequestJoin:j,onReserveCourt:D,clubSports:_,fetchData:r}):e.jsx(ge,{isOpen:f!==null,onClose:()=>N(null),buddy:f,clubSports:_,fetchData:r,onRequestJoin:j,onReserveCourt:D,setSelectedBuddy:N}),e.jsx(fe,{isOpen:P,onClose:()=>L(!1),onRequestJoin:b,loading:B}),e.jsx(we,{isOpen:k,onClose:()=>S(!1),onReserveCourt:I,loading:Z})]})}function Xe({fetchData:r,buddyData:u,allBuddyData:s,clubSports:_,selectedRequestTab:v}){const[x,h]=a.useState(new Date),[C,y]=a.useState(null),[f,N]=a.useState("desc"),[P,L]=a.useState(!1),{club:B}=de(),[H,i]=a.useState({num_needed:null,ntrp:null,max_ntrp:null}),[w,d]=a.useState(null),[R,U]=a.useState(u),[k,S]=a.useState(!1),[Z,A]=a.useState(!1),[V,z]=a.useState(!1),[O,W]=a.useState(!1),[J,q]=a.useState(null),[j,D]=a.useState(null),{dispatch:b}=a.useContext(re);a.useContext(ne);let I=new le;const T=c=>{S(!0),d(null),q(c)},l=c=>{A(!0),d(c)},t=async()=>{z(!0);try{(await I.callRawAPI("/v3/api/custom/courtmatchup/user/buddy/join-team",{buddy_id:J.buddy_id||J.id,ntrp:j.ntrp,player_ids:[j==null?void 0:j.user_id],num_players:1},"POST")).error||(F(b,"Request sent successfully",5e3,"success"),S(!1))}catch(c){console.log(c),F(b,c.message,5e3,"error")}finally{z(!1)}},p=async()=>{W(!0);try{(await I.callRawAPI("/v3/api/custom/courtmatchup/user/reservations",{buddy_request:he.court,buddy_id:w.buddy_id,reservation_type:1},"POST")).error||(F(b,"Reservation created successfully",5e3,"success"),A(!1))}catch(c){console.log(c),F(b,c.message,5e3,"error")}finally{W(!1)}};async function g(){try{I.setTable("profile");const c=localStorage.getItem("user"),Y=await I.callRestAPI({id:c},"GET");D(Y.model)}catch(c){console.error(c)}}const E=()=>{let c=R.filter(Y=>{let K=!0;return Object.entries(H).forEach(([X,ie])=>{ie!==null&&(K=K&&Y[X]===ie)}),K});return c=[...c].sort((Y,K)=>{const X=new Date(Y.date),ie=new Date(K.date);return f==="desc"?ie-X:X-ie}),c},$=(c,Y)=>{i(Y==="all"?K=>{const X={...K};return delete X[c],X}:K=>({...K,[c]:parseInt(Y)}))},te=c=>{N(c),L(!1)},o=async c=>{if(!c)return;const Y=new Date(Date.UTC(x.getFullYear(),x.getMonth(),c,0,0,0));y(Y);const K=Y.toISOString().split("T")[0],X=await r(1,10,[`date,cs,${K}`]);X&&U(X)},n=async()=>{y(null),U(u),await r(1,10,[])},m=()=>{h(new Date(x.getFullYear(),x.getMonth()-1,1))},M=()=>{h(new Date(x.getFullYear(),x.getMonth()+1,1))};a.useEffect(()=>{U(u)},[u]),a.useEffect(()=>{g()},[]);const G=E();return e.jsx("div",{children:e.jsxs("div",{className:"mx-auto max-w-6xl p-2 sm:p-4",children:[e.jsxs("div",{className:"flex flex-col gap-4 sm:gap-6 md:flex-row md:gap-8",children:[e.jsxs("div",{className:"h-fit w-full rounded-lg bg-white p-3 shadow-sm sm:p-4 sm:shadow-5 md:w-[350px] lg:w-[400px]",children:[e.jsx($e,{buddies:u,currentMonth:x,selectedDate:C,onDateClick:o,onPreviousMonth:m,onNextMonth:M,onDateSelect:c=>{c&&(y(c),o(c.getDate()))},daysOff:(()=>{try{return B!=null&&B.days_off?JSON.parse(B.days_off):[]}catch(c){return console.error("Error parsing days_off:",c),[]}})()}),C&&e.jsxs("div",{className:"mt-3 flex flex-wrap items-center justify-between border-t border-gray-200 pt-3 sm:mt-4 sm:pt-4",children:[e.jsxs("span",{className:"mr-2 text-xs text-gray-600 sm:text-sm",children:["Showing buddies for"," ",C.toLocaleDateString("en-US",{month:"long",day:"numeric",year:"numeric"})]}),e.jsx("button",{onClick:n,className:"text-xs text-blue-600 hover:underline sm:text-sm",children:"Clear"})]})]}),e.jsx("div",{className:"w-full",children:e.jsxs("div",{className:"space-y-3 rounded-lg bg-white p-3 text-xs shadow-sm sm:space-y-4 sm:p-5 sm:text-sm",children:[e.jsxs("div",{className:"mb-4 flex flex-col gap-3 sm:mb-6 sm:flex-row sm:items-center sm:justify-between sm:gap-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("p",{className:"whitespace-nowrap text-sm",children:"Players needed"}),e.jsxs("select",{onChange:c=>$("num_needed",c.target.value),className:"rounded-xl border border-gray-200 bg-white py-1 text-xs sm:text-sm",children:[e.jsx("option",{value:"all",children:"Not set"}),e.jsx("option",{value:"1",children:"1"}),e.jsx("option",{value:"2",children:"2"}),e.jsx("option",{value:"3",children:"3"}),e.jsx("option",{value:"4",children:"4"}),e.jsx("option",{value:"5",children:"5"})]})]}),e.jsxs("div",{className:"flex flex-wrap items-center gap-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("select",{onChange:c=>$("ntrp",c.target.value),className:"rounded-xl border border-gray-200 bg-white py-1 text-xs sm:text-sm",children:[e.jsx("option",{value:"all",children:"Min NTRP"}),ae.map(c=>e.jsx("option",{value:c,children:c},c))]}),e.jsxs("select",{onChange:c=>$("max_ntrp",c.target.value),className:"rounded-xl border border-gray-200 bg-white py-1 text-xs sm:text-sm",children:[e.jsx("option",{value:"all",children:"Max NTRP"}),ae.map(c=>e.jsx("option",{value:c,children:c},c))]})]}),e.jsxs("div",{className:"relative sm:pl-2",children:[e.jsxs("button",{onClick:()=>L(!P),className:"flex items-center gap-1 rounded-xl border border-gray-200 bg-white px-2 py-1 text-xs sm:px-4 sm:py-2 sm:text-sm",children:[e.jsxs("span",{className:"whitespace-nowrap text-gray-700",children:["By date (",f==="desc"?"Latest":"Earliest",")"]}),e.jsx(pe,{size:16,className:`text-gray-400 transition-transform duration-200 ${P?"rotate-180":""}`})]}),P&&e.jsxs("div",{className:"absolute right-0 top-full z-10 mt-1 w-48 rounded-lg border border-gray-200 bg-white py-1 shadow-lg",children:[e.jsxs("button",{onClick:()=>te("desc"),className:`flex w-full items-center justify-between px-4 py-2 text-xs hover:bg-gray-50 sm:text-sm ${f==="desc"?"text-blue-600":"text-gray-700"}`,children:["Latest first",f==="desc"&&e.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})]}),e.jsxs("button",{onClick:()=>te("asc"),className:`flex w-full items-center justify-between px-4 py-2 text-xs hover:bg-gray-50 sm:text-sm ${f==="asc"?"text-blue-600":"text-gray-700"}`,children:["Earliest first",f==="asc"&&e.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})]})]})]})]})]}),e.jsx("div",{className:"max-h-[400px] space-y-3 overflow-y-auto p-1 sm:max-h-[500px] sm:space-y-4 sm:p-2",children:G.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center rounded-lg bg-gray-50 py-8 sm:py-12",children:[e.jsx("div",{className:"mb-4 rounded-full bg-gray-100 p-3",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-gray-400",children:e.jsx("path",{d:"M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("h3",{className:"mb-1 px-4 text-center text-base font-medium text-gray-900 sm:text-lg",children:"No buddies found"})]}):G.map(c=>e.jsx(ue,{clubSports:_,buddy:c,onSelect:()=>d(c)},c.buddy_id||c.id))})]})})]}),w&&v==="all-requests"&&parseInt((w==null?void 0:w.owner_user_id)||0)===parseInt(localStorage.getItem("user"))?e.jsx(je,{isOpen:w!==null,onClose:()=>d(null),buddy:w,onRequestJoin:T,onReserveCourt:l,clubSports:_,fetchData:r}):e.jsx(ge,{isOpen:w!==null,onClose:()=>d(null),setSelectedBuddy:d,clubSports:_,buddy:w,onRequestJoin:T,onReserveCourt:l,fetchData:r}),e.jsx(fe,{isOpen:k,onClose:()=>S(!1),onRequestJoin:t,loading:V}),e.jsx(we,{isOpen:Z,onClose:()=>A(!1),onReserveCourt:p,loading:O})]})})}function es({fetchData:r,buddyData:u,clubSports:s,selectedRequestTab:_}){const[v,x]=a.useState(Fe()),[h,C]=a.useState(0),[y,f]=a.useState("desc"),[N,P]=a.useState(!1),[L,B]=a.useState({num_needed:null,ntrp:null,max_ntrp:null}),H=()=>{let o=u.filter(n=>{let m=!0;return Object.entries(L).forEach(([M,G])=>{G!==null&&(m=m&&n[M]===G)}),m});return o=[...o].sort((n,m)=>{const M=new Date(n.date),G=new Date(m.date);return y==="desc"?G-M:M-G}),o},i=(o,n)=>{B(n==="all"?m=>{const M={...m};return delete M[o],M}:m=>({...m,[o]:parseInt(n)}))},w=o=>{f(o),P(!1)},d=async()=>{if(h>0){const o=h-1;C(o),x(n=>n.clone().subtract(1,"week")),await r(1,10,{week:o})}},R=async()=>{const o=h+1;C(o),x(n=>n.clone().add(1,"week")),await r(1,10,{week:o})},U=()=>{const o=v.clone().startOf("week"),n=v.clone().endOf("week"),m=`${o.format("MMM D")} - ${n.format("MMM D")}`;return h===0?`This week (${m})`:h===1?`Next week (${m})`:`${h} weeks from now (${m})`},[k,S]=a.useState(null),[Z,A]=a.useState(!1),[V,z]=a.useState(!1),[O,W]=a.useState(!1),[J,q]=a.useState(!1),[j,D]=a.useState(null),[b,I]=a.useState(null),{dispatch:T}=a.useContext(re);a.useContext(ne);let l=new le;const t=o=>{A(!0),S(null),D(o)},p=o=>{z(!0),S(o)},g=async()=>{W(!0);try{(await l.callRawAPI("/v3/api/custom/courtmatchup/user/buddy/join-team",{buddy_id:j.buddy_id||j.id,ntrp:b.ntrp,player_ids:[b==null?void 0:b.user_id],num_players:1},"POST")).error||(F(T,"Request sent successfully",5e3,"success"),A(!1))}catch(o){console.log(o),F(T,o.message,5e3,"error")}finally{W(!1)}},E=async()=>{q(!0);try{(await l.callRawAPI("/v3/api/custom/courtmatchup/user/reservations",{buddy_request:he.court,buddy_id:k.buddy_id,reservation_type:1},"POST")).error||(F(T,"Reservation created successfully",5e3,"success"),z(!1))}catch(o){console.log(o),F(T,o.message,5e3,"error")}finally{q(!1)}};async function $(){try{l.setTable("profile");const o=localStorage.getItem("user"),n=await l.callRestAPI({id:o},"GET");I(n.model)}catch(o){console.error(o)}}a.useEffect(()=>{r(1,10,{week:0}),$()},[]);const te=H();return e.jsxs("div",{className:"w-full",children:[e.jsx("div",{className:"mx-auto mb-3 mt-3 max-w-xs rounded-xl bg-white p-1 shadow-sm sm:mb-5 sm:mt-5 sm:max-w-sm",children:e.jsxs("div",{className:"flex items-center justify-between gap-2 rounded-xl bg-gray-50 p-2 sm:gap-4",children:[e.jsx("button",{onClick:d,disabled:h===0,className:`rounded-xl bg-white p-1 text-gray-600 sm:p-2 ${h===0?"cursor-not-allowed opacity-50":"hover:text-gray-800"}`,children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"sm:h-6 sm:w-6",children:e.jsx("path",{d:"M15 18L9 12L15 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("div",{className:"text-center text-sm font-medium sm:text-lg",children:U()}),e.jsx("button",{onClick:R,className:"rounded-xl bg-white p-1 text-gray-600 hover:text-gray-800 sm:p-2",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"sm:h-6 sm:w-6",children:e.jsx("path",{d:"M9 18L15 12L9 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]})}),e.jsxs("div",{className:"mx-auto max-w-4xl space-y-3 rounded-lg bg-white p-3 shadow-sm sm:space-y-4 sm:p-4",children:[e.jsxs("div",{className:"mb-4 flex flex-col gap-3 sm:mb-6 sm:flex-row sm:items-center sm:justify-between sm:gap-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("p",{className:"whitespace-nowrap text-sm",children:"Players needed"}),e.jsxs("select",{onChange:o=>i("num_needed",o.target.value),className:"rounded-xl border border-gray-200 bg-white py-1 text-xs sm:text-sm",children:[e.jsx("option",{value:"all",children:"Not set"}),e.jsx("option",{value:"1",children:"1"}),e.jsx("option",{value:"2",children:"2"}),e.jsx("option",{value:"3",children:"3"}),e.jsx("option",{value:"4",children:"4"}),e.jsx("option",{value:"5",children:"5"})]})]}),e.jsxs("div",{className:"flex flex-wrap items-center gap-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("select",{onChange:o=>i("ntrp",o.target.value),className:"rounded-xl border border-gray-200 bg-white py-1 text-xs sm:text-sm",children:[e.jsx("option",{value:"all",children:"Min NTRP"}),ae.map(o=>e.jsx("option",{value:o,children:o},o))]}),e.jsxs("select",{onChange:o=>i("max_ntrp",o.target.value),className:"rounded-xl border border-gray-200 bg-white py-1 text-xs sm:text-sm",children:[e.jsx("option",{value:"all",children:"Max NTRP"}),ae.map(o=>e.jsx("option",{value:o,children:o},o))]})]}),e.jsxs("div",{className:"relative sm:pl-2",children:[e.jsxs("button",{onClick:()=>P(!N),className:"flex items-center gap-1 rounded-xl border border-gray-200 bg-white px-2 py-1 text-xs sm:px-4 sm:py-2 sm:text-sm",children:[e.jsxs("span",{className:"whitespace-nowrap text-gray-700",children:["By date (",y==="desc"?"Latest":"Earliest",")"]}),e.jsx(pe,{size:16,className:`text-gray-400 transition-transform duration-200 ${N?"rotate-180":""}`})]}),N&&e.jsxs("div",{className:"absolute right-0 top-full z-10 mt-1 w-48 rounded-lg border border-gray-200 bg-white py-1 shadow-lg",children:[e.jsxs("button",{onClick:()=>w("desc"),className:`flex w-full items-center justify-between px-4 py-2 text-xs hover:bg-gray-50 sm:text-sm ${y==="desc"?"text-blue-600":"text-gray-700"}`,children:["Latest first",y==="desc"&&e.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})]}),e.jsxs("button",{onClick:()=>w("asc"),className:`flex w-full items-center justify-between px-4 py-2 text-xs hover:bg-gray-50 sm:text-sm ${y==="asc"?"text-blue-600":"text-gray-700"}`,children:["Earliest first",y==="asc"&&e.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})]})]})]})]})]}),te.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center rounded-lg bg-gray-50 py-8 sm:py-12",children:[e.jsx("div",{className:"mb-4 rounded-full bg-gray-100 p-3",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-gray-400",children:e.jsx("path",{d:"M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("h3",{className:"mb-1 px-4 text-center text-base font-medium text-gray-900 sm:text-lg",children:"No buddies found"})]}):e.jsx("div",{className:"max-h-[400px] space-y-3 overflow-y-auto p-1 sm:max-h-[500px] sm:space-y-4 sm:p-2",children:te.map(o=>e.jsx(ue,{clubSports:s,buddy:o,onSelect:()=>S(o)},o.buddy_id||o.id))})]}),k&&_==="all-requests"&&parseInt((k==null?void 0:k.owner_user_id)||0)===parseInt(localStorage.getItem("user"))?e.jsx(je,{isOpen:k!==null,onClose:()=>S(null),buddy:k,onRequestJoin:t,onReserveCourt:p,clubSports:s,fetchData:r}):e.jsx(ge,{isOpen:k!==null,onClose:()=>S(null),buddy:k,clubSports:s,onRequestJoin:t,onReserveCourt:p,fetchData:r,setSelectedBuddy:S}),e.jsx(fe,{isOpen:Z,onClose:()=>A(!1),onRequestJoin:g,loading:O}),e.jsx(we,{isOpen:V,onClose:()=>z(!1),onReserveCourt:E,loading:J})]})}let me=new le;const ss=Ue().shape({sport:ee().required("Sport is required"),type:ee().when("sport",{is:r=>r,then:()=>ee().required("Type is required")}),subtype:ee().when(["sport","type"],{is:(r,u)=>r&&u,then:()=>ee().required("Subtype is required")}),days:ze().min(1,"Select at least one day"),dateFrom:ee().required("Start date is required"),dateTo:ee().required("End date is required").test("date","End date must be after start date",function(r){const{dateFrom:u}=this.parent;return!u||!r?!0:new Date(r)>new Date(u)}),startTime:ee().required("Start time is required"),endTime:ee().required("End time is required").test("time","End time must be after start time",function(r){const{startTime:u}=this.parent;return!u||!r?!0:r>u}),ntrpMin:Ne().required("Minimum NTRP is required").min(1,"Minimum NTRP must be at least 1").max(7,"Maximum NTRP must be at most 7"),ntrpMax:Ne().required("Maximum NTRP is required").min(1,"Minimum NTRP must be at least 1").max(7,"Maximum NTRP must be at most 7").test("ntrp","Maximum NTRP must be greater than minimum",function(r){const{ntrpMin:u}=this.parent;return!u||!r?!0:r>u}),notes:ee()});function ts({isOpen:r,onClose:u,clubProfile:s,userProfile:_,clubSports:v}){const[x,h]=a.useState("list"),[C,y]=a.useState(!1),{dispatch:f}=a.useContext(re),{control:N,handleSubmit:P,watch:L,setValue:B,reset:H,formState:{errors:i}}=Ie({defaultValues:{sport:"",type:"",subtype:"",days:[],dateFrom:"",dateTo:"",ntrpMin:"2",ntrpMax:"5",startTime:"",endTime:"",notes:""},resolver:He(ss)}),w=L("sport"),d=L("type"),[R,U]=a.useState([]),[k,S]=a.useState(null),[Z,A]=a.useState(null),[V,z]=a.useState(!1);a.useRef(null);const[O]=a.useState(()=>new Map),W=Array.from({length:24},(l,t)=>{const p=t,g=p<12?"AM":"PM";return{label:`${p===0?12:p>12?p-12:p}:00 ${g}`,value:`${p.toString().padStart(2,"0")}:00`}});a.useEffect(()=>{const l=t=>{if(Z!==null){const p=O.get(Z);p&&!p.contains(t.target)&&A(null)}};return document.addEventListener("mousedown",l),()=>{document.removeEventListener("mousedown",l)}},[Z]);async function J(){try{const l=await me.callRawAPI("/v3/api/custom/courtmatchup/user/buddy/get-subscriptions",{},"GET");U(l.subscriptions)}catch(l){console.error("Error fetching subscriptions:",l)}}a.useEffect(()=>{J()},[]);const q=l=>{S(l),A(null);const t=p=>{const[g]=p.split(":");return`${g.padStart(2,"0")}:00`};H({sport:l.sport_id.toString(),type:l.type,subtype:l.sub_type,days:JSON.parse(l.days),dateFrom:l.start_date,dateTo:l.end_date,startTime:t(l.start_time),endTime:t(l.end_time),ntrpMin:parseInt(l.min_ntrp),ntrpMax:parseInt(l.max_ntrp),notes:l.notes||""}),h("add")},j=()=>{S(null),H({sport:"",type:"",subtype:"",days:[],dateFrom:"",dateTo:"",ntrpMin:"2",ntrpMax:"5",startTime:"",endTime:"",notes:""}),h("list")},D=async l=>{z(!0);try{await me.callRawAPI(`/v3/api/custom/courtmatchup/user/subscribe/delete/${l}`,{},"GET"),F(f,"Subscription deleted successfully",3e3,"success"),J()}catch(t){F(f,t.message||"Error deleting subscription",3e3,"error")}finally{z(!1),A(null)}},b=()=>{var p,g,E,$,te,o;const l=(n,m)=>{const M=m.value||[],G=M.includes(n)?M.filter(c=>c!==n):[...M,n];m.onChange(G)},t=async n=>{y(!0);try{const m={sport_id:parseInt(n.sport),min_ntrp:parseFloat(n.ntrpMin),max_ntrp:parseFloat(n.ntrpMax),days:n.days,type:n.type,need_coach:1,notes:n.notes,start_date:n.dateFrom,end_date:n.dateTo,start_time:n.startTime,end_time:n.endTime,sub_type:n.subtype};k?await me.callRawAPI(`/v3/api/custom/courtmatchup/user/subscribe/edit/${k.id}`,m,"POST"):await me.callRawAPI("/v3/api/custom/courtmatchup/user/buddy/subscribe",m,"POST"),F(f,`Subscription ${k?"updated":"added"} successfully`,3e3,"success"),S(null),H({sport:"",type:"",subtype:"",days:[],dateFrom:"",dateTo:"",ntrpMin:"2",ntrpMax:"5",startTime:"",endTime:"",notes:""}),J(),h("list")}catch(m){F(f,m.message,3e3,"error"),console.error("Error saving subscription:",m)}finally{y(!1)}};return e.jsxs("div",{className:"flex flex-col",children:[e.jsx("div",{className:"mb-4 flex items-center justify-between bg-gray-100 px-5 py-1.5",children:e.jsx("p",{className:"text-sm text-gray-500",children:"DETAILS"})}),e.jsxs("form",{id:"subscription-form",onSubmit:P(t),className:"space-y-6 divide-y divide-gray-200 px-5 pb-20",children:[e.jsxs("div",{className:"space-y-6 pt-4",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Sport"}),i.sport&&e.jsx("p",{className:"text-sm text-red-500",children:i.sport.message}),e.jsx("div",{className:"flex flex-wrap items-center gap-8",children:v.filter(n=>n.status===1).map(n=>e.jsxs("label",{className:"flex items-center",children:[e.jsx(Q,{name:"sport",control:N,render:({field:m})=>e.jsx("input",{type:"radio",...m,value:n.id,checked:m.value==n.id,onChange:M=>{m.onChange(M.target.value),B("type",""),B("subtype","")},className:"h-4 w-4 text-blue-600"})}),e.jsx("span",{className:"ml-2 text-gray-900",children:n.name})]},n.id))})]}),w&&e.jsxs("div",{className:"space-y-4",children:[i.type&&e.jsx("p",{className:"text-sm text-red-500",children:i.type.message}),((p=v.find(n=>n.id==w))==null?void 0:p.sport_types.find(n=>n.type))&&e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Type"}),e.jsx("div",{className:"flex flex-wrap items-center gap-8",children:(g=v.find(n=>n.id==w))==null?void 0:g.sport_types.filter(n=>n.type).map((n,m)=>e.jsxs("label",{className:"flex items-center",children:[e.jsx(Q,{name:"type",control:N,render:({field:M})=>e.jsx("input",{type:"radio",...M,value:n.type,checked:M.value===n.type,onChange:G=>{M.onChange(G.target.value),B("subtype","")},className:"h-4 w-4 text-blue-600"})}),e.jsx("span",{className:"ml-2 text-gray-900",children:n.type})]},m))})]}),w&&d&&e.jsxs("div",{className:"space-y-4",children:[i.subtype&&e.jsx("p",{className:"text-sm text-red-500",children:i.subtype.message}),(($=(E=v.find(n=>n.id==w))==null?void 0:E.sport_types.find(n=>n.type===d))==null?void 0:$.subtype)&&e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Sub-type"}),e.jsx("div",{className:"flex flex-wrap items-center gap-8",children:(o=(te=v.find(n=>n.id==w))==null?void 0:te.sport_types.find(n=>n.type==d))==null?void 0:o.subtype.map((n,m)=>e.jsxs("label",{className:"flex items-center",children:[e.jsx(Q,{name:"subtype",control:N,render:({field:M})=>e.jsx("input",{type:"radio",...M,value:n,checked:M.value===n,className:"h-4 w-4 text-blue-600"})}),e.jsx("span",{className:"ml-2 text-gray-900",children:n.charAt(0).toUpperCase()+n.slice(1)})]},m))})]})]}),e.jsxs("div",{className:"space-y-4 py-4",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Days of the week"}),i.days&&e.jsx("p",{className:"text-sm text-red-500",children:i.days.message}),e.jsx("div",{className:"flex flex-wrap gap-3",children:["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"].map(n=>e.jsxs("label",{className:"flex items-center",children:[e.jsx(Q,{name:"days",control:N,render:({field:m})=>{var M;return e.jsx("input",{type:"checkbox",checked:((M=m.value)==null?void 0:M.includes(n))||!1,onChange:()=>l(n,m),className:"h-4 w-4 rounded border border-gray-300 text-blue-600"})}}),e.jsx("span",{className:"ml-2 text-gray-900",children:n})]},n))})]}),e.jsxs("div",{className:"space-y-4 pt-4",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Date range"}),i.dateFrom&&e.jsx("p",{className:"text-sm text-red-500",children:i.dateFrom.message}),i.dateTo&&e.jsx("p",{className:"text-sm text-red-500",children:i.dateTo.message}),e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("div",{className:"relative w-1/2",children:[e.jsx(Q,{name:"dateFrom",control:N,render:({field:n})=>e.jsxs(e.Fragment,{children:[e.jsx("input",{type:"date",...n,className:`w-full rounded-xl border ${i.dateFrom?"border-red-500":"border-gray-200"} bg-gray-50 px-4 py-2.5`}),i.dateFrom&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:i.dateFrom.message})]})}),e.jsx("span",{className:"absolute -top-2 left-3 bg-gray-50 px-1 text-xs text-gray-500",children:"From"})]}),e.jsxs("div",{className:"relative w-1/2",children:[e.jsx(Q,{name:"dateTo",control:N,render:({field:n})=>e.jsxs(e.Fragment,{children:[e.jsx("input",{type:"date",...n,className:`w-full rounded-xl border ${i.dateTo?"border-red-500":"border-gray-200"} bg-gray-50 px-4 py-2.5`}),i.dateTo&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:i.dateTo.message})]})}),e.jsx("span",{className:"absolute -top-2 left-3 bg-gray-50 px-1 text-xs text-gray-500",children:"To"})]})]})]}),e.jsxs("div",{className:"space-y-4 pt-4",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Time range"}),i.startTime&&e.jsx("p",{className:"text-sm text-red-500",children:i.startTime.message}),i.endTime&&e.jsx("p",{className:"text-sm text-red-500",children:i.endTime.message}),e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("div",{className:"relative w-1/2",children:[e.jsx(Q,{name:"startTime",control:N,render:({field:n})=>e.jsxs(e.Fragment,{children:[e.jsxs("select",{...n,className:`w-full rounded-xl border ${i.startTime?"border-red-500":"border-gray-200"} appearance-none bg-gray-50 px-4 py-2.5`,children:[e.jsx("option",{value:"",children:"Select time"}),W.map(m=>e.jsx("option",{value:m.value,children:m.label},m.value))]}),i.startTime&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:i.startTime.message})]})}),e.jsx("span",{className:"absolute -top-2 left-3 bg-gray-50 px-1 text-xs text-gray-500",children:"Start"})]}),e.jsxs("div",{className:"relative w-1/2",children:[e.jsx(Q,{name:"endTime",control:N,render:({field:n})=>e.jsxs(e.Fragment,{children:[e.jsxs("select",{...n,className:`w-full rounded-xl border ${i.endTime?"border-red-500":"border-gray-200"} appearance-none bg-gray-50 px-4 py-2.5`,children:[e.jsx("option",{value:"",children:"Select time"}),W.map(m=>e.jsx("option",{value:m.value,children:m.label},m.value))]}),i.endTime&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:i.endTime.message})]})}),e.jsx("span",{className:"absolute -top-2 left-3 bg-gray-50 px-1 text-xs text-gray-500",children:"End"})]})]})]}),e.jsxs("div",{className:"space-y-4 pt-4",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"NTRP score"}),i.ntrpMin&&e.jsx("p",{className:"text-sm text-red-500",children:i.ntrpMin.message}),i.ntrpMax&&e.jsx("p",{className:"text-sm text-red-500",children:i.ntrpMax.message}),e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("div",{className:"relative w-1/2",children:[e.jsx("span",{className:"absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400",children:"Min"}),e.jsx(Q,{name:"ntrpMin",control:N,render:({field:n})=>e.jsxs(e.Fragment,{children:[e.jsx("select",{...n,className:`w-full rounded-xl border ${i.ntrpMin?"border-red-500":"border-gray-200"} appearance-none bg-gray-50 px-4 py-2.5 pl-12`,children:ae.map(m=>e.jsx("option",{value:m,children:m.toFixed(1)},m))}),i.ntrpMin&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:i.ntrpMin.message})]})})]}),e.jsxs("div",{className:"relative w-1/2",children:[e.jsx("span",{className:"absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400",children:"Max"}),e.jsx(Q,{name:"ntrpMax",control:N,render:({field:n})=>e.jsxs(e.Fragment,{children:[e.jsx("select",{...n,className:`w-full rounded-xl border ${i.ntrpMax?"border-red-500":"border-gray-200"} appearance-none bg-gray-50 px-4 py-2.5 pl-12`,children:ae.map(m=>e.jsx("option",{value:m,children:m.toFixed(1)},m))}),i.ntrpMax&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:i.ntrpMax.message})]})})]})]})]}),e.jsxs("div",{className:"space-y-4 pt-4",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Notes"}),i.notes&&e.jsx("p",{className:"text-sm text-red-500",children:i.notes.message}),e.jsx(Q,{name:"notes",control:N,render:({field:n})=>e.jsx("textarea",{...n,className:`w-full rounded-xl border ${i.notes?"border-red-500":"border-gray-200"} bg-gray-50 px-4 py-2.5`})})]})]}),e.jsxs("div",{className:"fixed bottom-0 flex w-full flex-shrink-0 justify-end gap-4 border-t border-gray-200 bg-white px-4 py-4",children:[e.jsx("button",{type:"button",className:"flex-1 rounded-xl border border-gray-200 px-3 py-2 text-sm font-semibold text-gray-900 hover:bg-gray-50",onClick:j,children:"Cancel"}),e.jsxs(oe,{type:"submit",form:"subscription-form",className:"flex-1 rounded-xl bg-primaryBlue px-3 py-2 text-sm font-semibold text-white hover:bg-blue-700",onClick:P(t),loading:C,children:[k?"Update":"Save"," changes"]})]})]})},I=({subscription:l,index:t})=>{var g;const p=a.useRef(null);return a.useEffect(()=>(O.set(l.id,p.current),()=>O.delete(l.id)),[l.id]),e.jsxs("div",{className:"mb-4 rounded-2xl border border-gray-200 bg-gray-100 p-3 pt-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("h3",{className:"text-lg font-medium",children:["Subscription #",t+1]}),e.jsxs("div",{className:"relative",ref:p,children:[e.jsx("button",{onClick:()=>A(Z===l.id?null:l.id),className:"rounded-full p-1 text-gray-500 hover:bg-gray-200 hover:text-gray-700",children:e.jsx(Ae,{size:20})}),Z===l.id&&e.jsx("div",{className:"absolute right-0 z-50 mt-2 w-48 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5",children:e.jsxs("div",{className:"py-1",role:"menu","aria-orientation":"vertical",children:[e.jsx("button",{onClick:()=>q(l),className:"w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100",role:"menuitem",children:"Edit"}),e.jsx("button",{onClick:()=>D(l.id),disabled:V,className:"w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-gray-100",role:"menuitem",children:V?"Deleting...":"Delete"})]})})]})]}),e.jsxs("div",{className:"mt-4 space-y-2 rounded-xl bg-white p-3",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Sport"}),e.jsxs("span",{className:"font-medium",children:[l==null?void 0:l.sport_name," ",(l==null?void 0:l.type)&&`• ${l==null?void 0:l.type}`," ",(l==null?void 0:l.sub_type)&&`• ${l==null?void 0:l.sub_type}`]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Date & time"}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"font-medium",children:Be(new Date(l==null?void 0:l.start_date),"MMM dd, yyyy")}),e.jsxs("div",{className:"font-medium",children:[se(l==null?void 0:l.start_time)," -"," ",se(l==null?void 0:l.end_time)]})]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Days of the week"}),e.jsx("div",{className:"text-right",children:(g=JSON.parse(l==null?void 0:l.days))==null?void 0:g.map((E,$)=>e.jsx("div",{className:"font-medium",children:E},$))})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Min - max NTRP"}),e.jsxs("span",{className:"font-medium",children:[l==null?void 0:l.min_ntrp," - ",l==null?void 0:l.max_ntrp]})]})]})]})},T=()=>e.jsxs("div",{className:"flex flex-col",children:[e.jsx("div",{className:"bg-gray-100 px-5 py-1.5 ",children:e.jsx("p",{className:"text-sm text-gray-500",children:"REQUESTED BY"})}),e.jsx("div",{className:" p-5",children:e.jsx("p",{className:"text-gray-600",children:"Enter date/time range below and hit confirm. When Find-a-buddy request is made that fits into this range, you will be notified via email, and can go online to join the request if you like."})}),e.jsxs("div",{className:"mb-4 flex items-center justify-between bg-gray-100 px-5 py-1.5 ",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"DATES/TIMES ADDED"}),e.jsxs("button",{onClick:()=>h("add"),className:"flex items-center border-b border-blue-600 text-sm text-blue-600",children:[e.jsx(Te,{className:"mr-1"}),R.length===0?"Add subscription":"Add another"]})]}),e.jsxs("div",{className:"px-5",children:[R.map((l,t)=>e.jsx(I,{subscription:l,index:t},l.id)),R.length===0&&e.jsx("div",{className:"flex items-center justify-center p-5",children:e.jsx("p",{className:"text-gray-500",children:"Subscriptions is empty"})})]})]});return e.jsx(xe,{isOpen:r,onClose:u,title:x==="list"?"Subscriptions":k?"Edit subscription":"Add subscription",showFooter:!1,className:"!p-0",children:x==="list"?e.jsx(T,{}):e.jsx(b,{})})}let as=new Le,Ce=new le;function qs(){const[r,u]=a.useState(null),[s,_]=a.useState(!0),{dispatch:v}=a.useContext(re),{dispatch:x}=a.useContext(ne),{user_permissions:h}=de(),[C,y]=a.useState(!1),[f,N]=a.useState("all-requests"),[P,L]=a.useState([]),[B,H]=a.useState([]),[i,w]=a.useState(!1),[d,R]=a.useState(null),[U,k]=a.useState(null),[S,Z]=a.useState([]),A=[{id:"table",label:"Table"},{id:"calendar",label:"Calendar"},{id:"weekly",label:"Weekly"}],V=[{id:"all-requests",label:"All requests"},{id:"my-requests",label:"My requests"}],z=localStorage.getItem("user"),O=async(j,D,b={},I="all-requests")=>{y(!0),console.log("filters",b);try{let T=I==="all-requests"?"/v3/api/custom/courtmatchup/user/buddy/all-requests":"/v3/api/custom/courtmatchup/user/buddy/my-requests";const l=[];b&&typeof b=="object"&&Object.entries(b).forEach(([p,g])=>{g&&l.push(`${p}=${encodeURIComponent(g)}`)}),l.length>0&&(T+=`?${l.join("&")}`);const t=await Ce.callRawAPI(T,{},"GET");if(!t.error){y(!1);const p=I==="all-requests"?t.list.map(g=>({...g,source:"all-requests"})):t.my_requests.map(g=>({...g,source:"my-requests"}));return L(p),p}}catch(T){y(!1),console.log("ERROR",T),ve(x,T.message)}};async function W(){var j;try{const D=await as.getOne("user",z,{}),b=await Ce.callRawAPI(`/v3/api/custom/courtmatchup/user/club/${D.model.club_id}`,{},"GET");R(b.model),k(D.model),Z(b.sports);let I="table";if((j=b.model)!=null&&j.buddy_description)try{const T=JSON.parse(b.model.buddy_description);T.default_view&&(I=T.default_view)}catch(T){console.error("Error parsing buddy_description:",T)}u(I),_(!1)}catch(D){console.log("ERROR",D),u("table"),_(!1)}}async function J(j={}){try{const D=await O(1,10,j);H(D)}catch(D){console.log("ERROR",D),ve(x,D.message)}}a.useEffect(()=>{W(),v({type:"SETPATH",payload:{path:"find-a-buddy"}})},[]),a.useEffect(()=>{r&&(O(1,10,{}),J({}))},[r]);const q=j=>{N(j),O(1,10,{},j)};return h&&!h.allowBuddy?e.jsx(ke,{message:`Your current plan (${h==null?void 0:h.planName}) does not include find a buddy feature. Please upgrade your plan to access this feature.`}):e.jsxs(e.Fragment,{children:[(C||!r)&&e.jsx(Me,{}),r&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"bg-white px-3 sm:px-4",children:[e.jsxs("div",{className:"flex flex-col pt-4 sm:flex-row sm:items-center sm:justify-between",children:[e.jsx("h1",{className:"mb-4 text-xl font-semibold sm:mb-6 sm:text-2xl",children:"Find a buddy"}),e.jsxs("div",{className:"mb-4 flex flex-wrap items-center gap-2 sm:mb-0",children:[e.jsx("button",{onClick:()=>w(!0),className:"rounded-xl border bg-green-900 px-3 py-2 text-xs text-white sm:px-4 sm:text-sm",children:"Subscriptions"}),e.jsxs(Re,{to:"/user/create-request",className:"flex items-center rounded-xl border bg-primaryBlue px-3 py-2 text-xs text-white sm:text-sm",children:[e.jsx("svg",{width:"12",height:"12",viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M5.25 5.25V0.75H6.75V5.25H11.25V6.75H6.75V11.25H5.25V6.75H0.75V5.25H5.25Z",fill:"white"})}),e.jsx("span",{className:"ml-2",children:"Create a request"})]})]})]}),e.jsxs("div",{className:"flex flex-col pt-2 sm:flex-row sm:items-center sm:justify-between sm:pt-4",children:[e.jsx("div",{className:"mb-3 flex max-w-fit overflow-x-auto text-xs sm:mb-0 sm:text-sm",children:V.map(j=>e.jsx("button",{onClick:()=>q(j.id),className:`flex items-center gap-2 whitespace-nowrap bg-transparent px-3 py-2 sm:py-3 ${f===j.id?"border-b-2 border-primaryBlue":""}`,children:e.jsx("span",{children:j.label})},j.id))}),e.jsx("div",{className:"mb-3 flex max-w-fit divide-x overflow-x-auto rounded-xl border text-xs sm:text-sm",children:A.map(j=>e.jsx("button",{onClick:()=>u(j.id),className:`whitespace-nowrap px-2 py-2 sm:px-3 ${r===j.id?"bg-white-600":"bg-gray-100 text-gray-600"}`,children:j.label},j.id))})]})]}),e.jsx("div",{className:"px-2 py-2 sm:px-3 sm:py-3",children:e.jsxs("div",{className:"mx-auto max-w-7xl",children:[r==="table"&&e.jsx(Qe,{fetchData:O,buddyData:P,setBuddyData:L,clubSports:S,selectedRequestTab:f}),r==="calendar"&&e.jsx(Xe,{fetchData:O,buddyData:P,setBuddyData:L,allBuddyData:B,clubSports:S,selectedRequestTab:f}),r==="weekly"&&e.jsx(es,{fetchData:O,buddyData:P,setBuddyData:L,clubSports:S,selectedRequestTab:f})]})}),e.jsx(ts,{isOpen:i,onClose:()=>w(!1),clubProfile:d,userProfile:U,clubSports:S})]})]})}export{qs as default};

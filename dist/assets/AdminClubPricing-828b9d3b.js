import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{u as C,r as o}from"./vendor-851db8c1.js";import{e as j,M as k}from"./index-f915b394.js";import{C as A}from"./ClubPricing-6f15cfd0.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./InformationCircleIcon-d35f3488.js";const a=new k;function mt(){var g;const h=C(),[p,u]=o.useState(!1),[c,n]=o.useState(null),[i,E]=o.useState(null),[w,x]=o.useState([]),[S,L]=o.useState([]),l=(g=h.state)==null?void 0:g.clubId,f=o.useCallback(async()=>{try{a.setTable("surface");const t=await a.callRestAPI({},"GETALL");return(t==null?void 0:t.list)||[]}catch(t){throw new Error(`Error fetching surfaces: ${t.message}`)}},[]),d=o.useCallback(async t=>{if(!t)return[];try{a.setTable("sports");const s=await a.callRestAPI({filter:[`club_id,cs,${t}`]},"GETALL");return(s==null?void 0:s.list)||[]}catch(s){throw new Error(`Error fetching sports: ${s.message}`)}},[]),m=o.useCallback(async()=>{var t,s;if(!l){n("Club ID is missing. Please navigate back and try again.");return}u(!0),n(null);try{const r=await a.callRawAPI(`/v3/api/custom/courtmatchup/admin/profile/${l}`,{},"GET"),b=(s=(t=r==null?void 0:r.model)==null?void 0:t.club)==null?void 0:s.id;if(!b)throw new Error("Club information not found in the response");const[y,P]=await Promise.all([f(),d(b)]);x(y),L(P),E(r==null?void 0:r.model)}catch(r){console.error("Error fetching club settings:",r),n(r.message||"Failed to load club settings. Please try again.")}finally{u(!1)}},[l,f,d]);return o.useEffect(()=>{m()},[m]),e.jsxs("div",{className:"flex flex-col gap-4 p-8",children:[p&&e.jsx(j,{}),c&&e.jsx("div",{className:"mb-4 rounded-md bg-red-50 p-4 text-red-700",children:e.jsx("p",{children:c})}),!p&&!c&&i&&e.jsx(A,{role:"admin",profileSettings:i,sports:S,surfaces:w,fetchSettings:m,pricing:i==null?void 0:i.pricing})]})}export{mt as default};

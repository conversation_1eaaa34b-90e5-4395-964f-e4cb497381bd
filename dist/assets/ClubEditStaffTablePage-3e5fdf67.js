import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{b as l,f as F,r as m,j as U}from"./vendor-851db8c1.js";import{u as M}from"./react-hook-form-687afde5.js";import{o as $}from"./yup-2824f222.js";import{c as G,a as f}from"./yup-54691517.js";import{M as O,A as B,G as H,t as q,d as K,b as V}from"./index-f915b394.js";import"./react-quill-73fb9518.js";/* empty css                   */import{M as N}from"./MkdInput-0abc8dbe.js";import{S as z}from"./index-02625b16.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let c=new O;const $e=r=>{var y,j,S,w;const{dispatch:I}=l.useContext(B),E=G({user_id:f(),club_id:f(),permission_level:f(),account_details:f()}).required(),{dispatch:b}=l.useContext(H),[_,J]=l.useState({}),[g,x]=l.useState(!1),[A,h]=l.useState(!1),k=F(),[Q,D]=m.useState(0),[W,P]=m.useState(0),[X,C]=m.useState(""),[Y,T]=m.useState(""),{register:d,handleSubmit:L,setError:v,setValue:u,formState:{errors:o}}=M({resolver:$(E)}),a=U();m.useEffect(function(){(async function(){try{h(!0),c.setTable("staff");const e=await c.callRestAPI({id:r.activeId?r.activeId:Number(a==null?void 0:a.id)},"GET");e.error||(u("user_id",e.model.user_id),u("club_id",e.model.club_id),u("permission_level",e.model.permission_level),u("account_details",e.model.account_details),D(e.model.user_id),P(e.model.club_id),C(e.model.permission_level),T(e.model.account_details),h(!1))}catch(e){h(!1),console.log("error",e),q(I,e.message)}})()},[]);const R=async e=>{x(!0);try{c.setTable("staff");for(let n in _){let i=new FormData;i.append("file",_[n].file);let p=await c.uploadImage(i);e[n]=p.url}const s=await c.callRestAPI({id:r.activeId?r.activeId:Number(a==null?void 0:a.id),user_id:e.user_id,club_id:e.club_id,permission_level:e.permission_level,account_details:e.account_details},"PUT");if(!s.error)V(b,"Updated"),k("/club/staff"),b({type:"REFRESH_DATA",payload:{refreshData:!0}}),r.setSidebar(!1);else if(s.validation){const n=Object.keys(s.validation);for(let i=0;i<n.length;i++){const p=n[i];v(p,{type:"manual",message:s.validation[p]})}}x(!1)}catch(s){x(!1),console.log("Error",s),v("user_id",{type:"manual",message:s.message})}};return l.useEffect(()=>{b({type:"SETPATH",payload:{path:"staff"}})},[]),t.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Staff"}),A?t.jsx(z,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:L(R),children:[t.jsx(N,{type:"number",page:"edit",name:"user_id",errors:o,label:"User Id",placeholder:"User Id",register:d,className:""}),t.jsx(N,{type:"number",page:"edit",name:"club_id",errors:o,label:"Club Id",placeholder:"Club Id",register:d,className:""}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"permission_level",children:"Permission Level"}),t.jsx("textarea",{placeholder:"Permission Level",...d("permission_level"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(y=o.permission_level)!=null&&y.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(j=o.permission_level)==null?void 0:j.message})]}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"account_details",children:"Account Details"}),t.jsx("textarea",{placeholder:"Account Details",...d("account_details"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(S=o.account_details)!=null&&S.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(w=o.account_details)==null?void 0:w.message})]}),t.jsx(K,{type:"submit",className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",loading:g,disable:g,children:"Submit"})]})]})};export{$e as default};

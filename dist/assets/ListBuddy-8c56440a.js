import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{b as r,r as c,f as ve}from"./vendor-851db8c1.js";import{T as b,M as je,G as ye,A as _e,E as Se,c as Ne,V as Ce,K as we,b as E,t as M,a1 as De,ap as Pe}from"./index-f915b394.js";import"./index-be4468eb.js";import"./AddButton.module-98aac587.js";import{P as Le}from"./index-eb1bc208.js";import{_ as T}from"./lodash-91d5d207.js";import ke from"./Skeleton-1e8bf077.js";import{D as Ee}from"./DataTable-a2248415.js";import{H as Me}from"./HistoryComponent-a235a60c.js";new b;let p=new je,Te=new b;const be=[{header:"Request created",accessor:"update_at"},{header:"Looking for",accessor:"num_needed"},{header:"Requesting Players",accessor:"num_players"},{header:"Requested by",accessor:"user_id"},{header:"Sport",accessor:"sport_id"},{header:"Court booked",accessor:"court_booked"}],Re=({sports:i,club:l,courts:R})=>{const{dispatch:m}=r.useContext(ye),{dispatch:j}=r.useContext(_e),[$,A]=r.useState([]),[o,y]=r.useState(10),[q,B]=r.useState(0),[$e,F]=r.useState(0),[g,O]=r.useState(0),[I,H]=r.useState(!1),[z,G]=r.useState(!1),[Ae,K]=r.useState(!1),[_,h]=r.useState(!0),[V,qe]=r.useState(!1),[Z,Be]=r.useState(!1),[d,Q]=c.useState(null),X=ve(),S=r.useRef(null),[N,Y]=r.useState([]),[J,f]=r.useState(!1),[C,U]=c.useState(""),[W,ee]=c.useState(!1),[te,w]=c.useState(!1),[se,D]=c.useState(!1),[ae,re]=c.useState(null);function ne(){n(g-1,o)}function oe(){n(g+1,o)}const le=async()=>{try{p.setTable("user");const e=await p.callRestAPI({filter:[`club_id,eq,${l==null?void 0:l.id}`,"role,cs,user"]},"GETALL");Y(e.list||[])}catch(e){console.error("Error fetching members:",e),E(m,"Error fetching members",3e3,"error")}};async function n(e,a,s={},u=[]){h(!(Z||V));try{const x=await Te.getPaginate("buddy",{page:e,limit:a,filter:[...u,`courtmatchup_reservation.club_id,eq,${l==null?void 0:l.id}`],join:["sports|sport_id","user|user_id|player_ids","reservation|reservation_id"]});x&&h(!1);const{list:me,total:he,limit:fe,num_pages:k,page:v}=x;A(me),y(fe),B(k),O(v),F(he),H(v>1),G(v+1<=k)}catch(x){h(!1),console.log("ERROR",x),M(j,x.message)}}const ie=e=>{e.target.value===""?n(1,o):n(1,o,{},[`status,cs,${e.target.value}`])};r.useEffect(()=>{m({type:"SETPATH",payload:{path:"find-a-buddy"}}),le();const a=setTimeout(async()=>{await n(1,o)},700);return()=>{clearTimeout(a)}},[l==null?void 0:l.id]);const P=e=>{S.current&&!S.current.contains(e.target)&&K(!1)};r.useEffect(()=>(document.addEventListener("mousedown",P),()=>{document.removeEventListener("mousedown",P)}),[]);const ce=async e=>{w(!0);try{p.setTable("buddy"),await p.callRestAPI({id:e},"DELETE"),n(g,o)}catch(a){console.error("Error deleting buddy:",a),M(j,a.message)}finally{w(!1)}},de=e=>{const a=e.target.value;a===""?n(1,o):n(1,o,{},[`sport_id,cs,${a}`])},ue=()=>{const e=N.filter(s=>`${s.first_name||""} ${s.last_name||""}`.toLowerCase().includes(C.toLowerCase())),a=s=>{Q(s)};return t.jsx("div",{className:"fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50",children:t.jsxs("div",{className:"w-96 rounded-lg bg-white shadow-lg",children:[t.jsxs("div",{className:"mb-0 flex items-center justify-between p-4",children:[t.jsx("h3",{className:"text-lg font-medium",children:"New Find Buddy Request"}),t.jsx("button",{onClick:()=>setIsCoachModalOpen(!1),className:"text-gray-400 hover:text-gray-600",children:t.jsx(De,{className:"h-5 w-5"})})]}),t.jsx("h2",{className:"px-4 text-base font-medium",children:"Request on behalf of:"}),t.jsxs("div",{className:"p-4",children:[t.jsx("div",{className:"mb-4",children:t.jsxs("div",{className:"flex  items-center gap-1 rounded-lg border border-gray-300 px-2 focus:border-blue-500 focus:outline-none",children:[t.jsx("span",{className:"w-5",children:t.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M9.25 2.5C12.976 2.5 16 5.524 16 9.25C16 12.976 12.976 16 9.25 16C5.524 16 2.5 12.976 2.5 9.25C2.5 5.524 5.524 2.5 9.25 2.5ZM9.25 14.5C12.1502 14.5 14.5 12.1502 14.5 9.25C14.5 6.349 12.1502 4 9.25 4C6.349 4 4 6.349 4 9.25C4 12.1502 6.349 14.5 9.25 14.5ZM15.6137 14.5532L17.7355 16.6742L16.6742 17.7355L14.5532 15.6137L15.6137 14.5532Z",fill:"#525866"})})}),t.jsx("input",{type:"text",placeholder:"search by name",value:C,onChange:s=>U(s.target.value),className:"w-full border-none bg-transparent focus:outline-none focus:ring-0"})]})}),t.jsxs("div",{className:"max-h-64 space-y-2 overflow-y-auto rounded-xl bg-gray-50",children:[e.length>0&&e.map(s=>t.jsxs("div",{onClick:()=>a(s),className:"flex cursor-pointer items-center space-x-3 rounded-lg p-2 hover:bg-gray-50",children:[t.jsx("input",{type:"radio",checked:(d==null?void 0:d.id)===s.id,onChange:()=>a(s),className:"h-4 w-4 rounded-full border-gray-300 text-blue-600"}),t.jsx("div",{className:"h-8 w-8 overflow-hidden rounded-full bg-gray-200",children:t.jsx("img",{src:(s==null?void 0:s.photo)||"/default-avatar.png",alt:`${s.first_name} ${s.last_name}`,className:"h-full w-full object-cover"})}),t.jsx("span",{children:`${s.first_name} ${s.last_name}`})]},s.id)),!(e!=null&&e.length)&&t.jsx("p",{className:"text-center text-sm text-gray-500",children:"No members found"})]}),t.jsxs("div",{className:"mt-4 flex justify-between gap-3 pt-3",children:[t.jsx("button",{onClick:()=>f(!1),className:"flex-1 rounded-xl border border-gray-300 px-4 py-2 text-gray-600 hover:text-gray-800",children:"Cancel"}),t.jsx("button",{onClick:()=>{d?(f(!1),X("/club/add-find_a_buddy_requests",{state:{player:d}})):E(m,"Please select a member",3e3,"error")},className:"flex-1 rounded-xl bg-blue-900 px-4 py-2 text-white hover:bg-blue-800",children:"Save and close"})]})]})]})})},xe=T.debounce(e=>{e?n(1,o,{},[`first_name,cs,${e}`,`last_name,cs,${e}`]):n(1,o)},300),L=T.debounce(e=>{e?n(1,o,{},[`date,cs,${e}`]):n(1,o)},300),ge=e=>{const a={...e,id:e==null?void 0:e.id,date:e==null?void 0:e.date,startTime:e==null?void 0:e.start_time,endTime:e==null?void 0:e.end_time,sport_id:e==null?void 0:e.sport_id,type:e==null?void 0:e.type,sub_type:e==null?void 0:e.sub_type,reservation_type:2,price:e==null?void 0:e.price,status:e==null?void 0:e.status,player_ids:e==null?void 0:e.player_ids,coach_ids:e==null?void 0:e.coach_ids};re(a),D(!0)},pe={user_id:e=>{var a,s,u;return(a=e==null?void 0:e.user)!=null&&a.first_name?`${(s=e==null?void 0:e.user)==null?void 0:s.first_name} ${(u=e==null?void 0:e.user)==null?void 0:u.last_name}`:"--"},sport_id:e=>{var a;return(a=i==null?void 0:i.find(s=>s.id===(e==null?void 0:e.sport_id)))==null?void 0:a.name},players:e=>"2 players",update_at:e=>Pe(e==null?void 0:e.update_at),court_booked:e=>e.reservation_id?"Yes":"No"};return t.jsxs("div",{className:"h-screen px-8",children:[t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsx("div",{children:t.jsx("h3",{className:"text-2xl font-medium leading-6 text-gray-900",children:"Find a buddy"})}),t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsxs("button",{onClick:()=>f(!0),className:"inline-flex items-center gap-2 rounded-lg bg-[#1D275F] px-4 py-2 text-sm font-semibold text-white hover:bg-blue-700",children:[t.jsx("span",{children:"+"}),"Add new"]}),t.jsx(Me,{title:"Buddy History",emptyMessage:"No buddy history found",activityType:Se.find_a_buddy})]})]}),t.jsxs("div",{className:"flex flex-col justify-between gap-4 py-3 md:flex-row md:items-center",children:[t.jsxs("div",{className:"relative flex max-w-sm flex-1 items-center",children:[t.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:t.jsx(Ne,{className:"text-gray-500"})}),t.jsx("input",{type:"text",onChange:e=>xe(e.target.value),className:"block w-full rounded-lg border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"search buddy"})]}),t.jsxs("select",{className:"rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700",defaultValue:"All",onChange:de,children:[t.jsx("option",{value:"",children:"Sport: All"}),i==null?void 0:i.map(e=>t.jsx("option",{value:e.id,children:e.name}))]}),t.jsxs("div",{className:"flex flex-wrap gap-4",children:[t.jsx("input",{type:"date",className:"rounded-md border border-gray-200 text-sm text-gray-500",onChange:e=>L(e.target.value)}),t.jsx("input",{type:"date",className:"rounded-md border border-gray-200 text-sm text-gray-500",onChange:e=>L(e.target.value)}),t.jsx("select",{className:"rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700",defaultValue:"All",onChange:ie,children:t.jsx("option",{value:"",children:"Requesting party:?"})})]})]}),_?t.jsx(ke,{}):t.jsx(Ee,{columns:be,data:$,loading:_,renderCustomCell:pe,rowClassName:"hover:bg-gray-40 bg-gray-100 px-4 py-3 text-gray-500 cursor-pointer",emptyMessage:"No buddy requests available",loadingMessage:"Loading buddy requests...",onClick:e=>ge(e)}),t.jsx(Le,{currentPage:g,pageCount:q,pageSize:o,canPreviousPage:I,canNextPage:z,updatePageSize:e=>{y(e),n(1,e)},previousPage:ne,nextPage:oe,gotoPage:e=>n(e,o)}),J&&t.jsx(ue,{}),t.jsx(Ce,{isOpen:se,onClose:()=>D(!1),event:ae,users:N||[],sports:i||[],club:l,fetchData:n,courts:R||[]}),t.jsx(we,{isOpen:W,onClose:()=>ee(!1),onDelete:ce,loading:te,title:"Delete",message:"Are you sure you want to delete this find a buddy request?"})]})},Xe=Re;export{Xe as L};

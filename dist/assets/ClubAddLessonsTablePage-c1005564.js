import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as n,u as ve,f as Ne,r as a}from"./vendor-851db8c1.js";import{u as we}from"./react-hook-form-687afde5.js";import{o as Se}from"./yup-2824f222.js";import{c as Ce,a as i}from"./yup-54691517.js";import{M as Me,G as ke,A as Le,as as W,a5 as X,d as Te,b as y}from"./index-f915b394.js";import"./react-quill-73fb9518.js";/* empty css                   */import"./index-02625b16.js";import"./Calendar-9031b5fe.js";import{S as L}from"./SelectionOption-01b973e9.js";import{B as _e}from"./BackButton-11ba52b2.js";import{S as Pe}from"./SuccessModal-e9ef416e.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-tooltip-7a26650a.js";import"./ChevronLeftIcon-e5eecf9c.js";import"./ChevronRightIcon-efb4c46c.js";let c=new Me;const Ls=({setSidebar:Ae})=>{var Q,H,J;const{dispatch:h,state:Y}=n.useContext(ke),T=ve();console.log("locationss",T);const ee=Ce({club_id:i(),user_id:i(),sport:i(),level:i(),start_time:i(),end_time:i(),num_players_needed:i(),players:i(),bio:i(),status:i()}).required();n.useContext(Le),n.useState({});const[_,P]=n.useState(!1),[se,v]=n.useState(!1),N=Ne();we({resolver:Se(ee)}),n.useState([]);const[A,te]=n.useState([]),[b,ae]=n.useState([]),[j,le]=n.useState([]),[u,Re]=n.useState(new Date);n.useState(new Date);const[R,E]=n.useState("calendar"),[o,w]=a.useState([]),[p,re]=a.useState(null),[g,ne]=a.useState(null),[f,ie]=a.useState(null),[Ee,q]=a.useState(!1),[qe,ce]=a.useState([]),[d,S]=a.useState(null),[I,C]=a.useState(null),[m,B]=a.useState([]),[D,oe]=a.useState(2.5),[O,de]=a.useState(5),[U,me]=a.useState(1),[Z,ue]=a.useState(1),[$,xe]=a.useState(""),[M,G]=a.useState(""),he=["8:00 AM","8:30 AM","9:00 AM","9:30 AM","10:00 AM","10:30 AM","11:00 AM","11:30 AM","12:00 PM","12:30 PM","1:00 PM","1:30 PM"].map(s=>({label:s,value:s,selected:!1})),x=A.filter(s=>`${s.first_name||""} ${s.last_name||""}`.toLowerCase().includes(M.toLowerCase())),pe=async()=>{q(!0);try{c.setTable("user");const s=await c.callRestAPI({filter:["role,cs,user"]},"GETALL");c.setTable("user");const t=await c.callRestAPI({filter:["role,cs,coach"]},"GETALL");te(s.list),ce(t.list)}catch(s){return console.error("Error fetching users:",s),[]}finally{q(!1)}},ge=async()=>{var s,t;try{c.setTable("sports");const l=await c.callRestAPI({filter:[`club_id,eq,${(t=(s=Y.clubProfile)==null?void 0:s.club)==null?void 0:t.id}`]},"GETALL");ae(l.list),c.setTable("surface");const r=await c.callRestAPI({},"GETALL");le(r.list)}catch(l){return console.error("Error fetching sports:",l),[]}};a.useEffect(()=>{pe(),ge()},[]),n.useEffect(()=>{h({type:"SETPATH",payload:{path:"find_a_buddy_requests"}})},[]);const fe=()=>{w([...o,{from:"08:00 AM",until:"9:00 AM"}])},be=s=>{const t=o.filter((l,r)=>r!==s);w(t)},je=s=>{d?!I&&s.value>d.value?(C(s),w([...o,{from:d.value,until:s.value}]),S(null),C(null)):(S(s),C(null)):S(s)},F=s=>{m.includes(s)?B(m.filter(t=>t!==s)):B([...m,s])},z=s=>{const[t,l]=s.split(" ");let[r,k]=t.split(":");return r=parseInt(r),l==="PM"&&r<12&&(r+=12),l==="AM"&&r===12&&(r=0),`${r.toString().padStart(2,"0")}:${k}:00`},ye=async()=>{var s,t;try{if(P(!0),!p||!g||!f||!u||o.length===0){y(h,"Please fill in all required fields","error");return}const l=u.toISOString().split("T")[0],r=z(o[0].from),k=z(o[o.length-1].until),K={sport_id:parseInt(p),type:g,ntrp:parseFloat(D),max_ntrp:parseFloat(O),num_players:parseInt(U),num_needed:parseInt(Z),need_coach:!0,notes:$,player_ids:JSON.stringify(m),date:l,start_time:r,end_time:k,surface_id:parseInt(f),user_id:(t=(s=T.state)==null?void 0:s.player)==null?void 0:t.id};console.log("buddyRequestData",K),c.setTable("buddy");const V=await c.callRestAPI(K,"POST");V.error?y(h,V.message||"Error creating request",3e3,"error"):(v(!0),y(h,"Request created successfully",3e3,"success"),setTimeout(()=>{N("/club/find-a-buddy")},2e3))}catch(l){console.error("Error submitting request:",l),y(h,"Error creating request: "+l.message,3e3,"error")}finally{P(!1)}};return e.jsxs("div",{className:"mx-auto max-w-7xl p-4",children:[e.jsx(_e,{onBack:()=>{R==="details"?E("calendar"):N(-1)}}),R==="calendar"?e.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[e.jsxs("div",{className:"max-h-fit space-y-6 rounded-lg bg-white p-4 shadow-5",children:[e.jsxs("div",{children:[b.length>0&&b.map(s=>e.jsx(L,{label:s.name,selected:p===s.id,onClick:()=>re(s.id)},s.id)),!b.length&&e.jsx("p",{className:"text-center text-sm text-gray-500",children:"No sports found"})]}),e.jsx("div",{children:W.map(s=>e.jsx(L,{label:s.label,selected:g==s.id,onClick:()=>ne(s.id)},s.id))}),e.jsxs("div",{children:[j.length>0&&j.map(s=>e.jsx(L,{label:s.name,selected:f===s.id,onClick:()=>ie(s.id)},s.id)),!j.length&&e.jsx("p",{className:"text-center text-sm text-gray-500",children:"No surfaces found"})]})]}),e.jsxs("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:[e.jsx("div",{className:"mb-5 rounded-lg bg-gray-50 p-2",children:e.jsx("p",{className:"text-center text-lg font-medium",children:"Players"})}),e.jsx("div",{className:"mb-4",children:e.jsx("div",{className:"mb-4",children:e.jsxs("div",{className:"flex  items-center gap-1 rounded-lg border border-gray-300 px-2 focus:border-blue-500 focus:outline-none",children:[e.jsx("span",{className:"w-5",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.25 2.5C12.976 2.5 16 5.524 16 9.25C16 12.976 12.976 16 9.25 16C5.524 16 2.5 12.976 2.5 9.25C2.5 5.524 5.524 2.5 9.25 2.5ZM9.25 14.5C12.1502 14.5 14.5 12.1502 14.5 9.25C14.5 6.349 12.1502 4 9.25 4C6.349 4 4 6.349 4 9.25C4 12.1502 6.349 14.5 9.25 14.5ZM15.6137 14.5532L17.7355 16.6742L16.6742 17.7355L14.5532 15.6137L15.6137 14.5532Z",fill:"#525866"})})}),e.jsx("input",{type:"text",placeholder:"Search by name",value:M,onChange:s=>G(s.target.value),className:"w-full border-none bg-transparent text-sm focus:outline-none focus:ring-0"})]})})}),e.jsxs("div",{className:"max-h-[400px] space-y-2 overflow-y-auto rounded-lg bg-gray-50 p-2",children:[x.length>0&&x.map(s=>e.jsxs("div",{className:"flex items-center gap-2 p-2",children:[e.jsx("input",{type:"checkbox",className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500",checked:m.includes(s.id),onChange:()=>F(s.id)}),e.jsx("img",{src:s.photo||"/default-avatar.png",className:"h-8 w-8 rounded-full",alt:s.name}),e.jsxs("span",{children:[s.first_name," ",s.last_name]})]},s.id)),!x.length&&e.jsx("p",{className:"text-center text-sm text-gray-500",children:"No players found"})]})]}),e.jsxs("div",{className:" h-fit rounded-lg bg-white p-4",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"mb-2 rounded-lg bg-gray-50 p-2 text-center text-sm text-gray-600",children:u?u.toLocaleDateString("en-US",{weekday:"long",month:"long",day:"numeric",year:"numeric"}):"Select a date"}),e.jsx("div",{className:"space-y-2",children:he.map(s=>e.jsx("button",{onClick:()=>je(s),className:`w-full rounded-lg border p-2 text-center transition-colors ${(d==null?void 0:d.value)===s.value?"bg-blue-500 text-white":d&&!I&&s.value>d.value?"border-blue-200 bg-blue-50 hover:bg-blue-100":"border-gray-200 hover:bg-gray-50"}`,children:s.label},s.value))})]}),e.jsx("div",{className:"mt-4",children:o.map((s,t)=>e.jsxs("div",{className:"mb-2 grid grid-cols-2 text-sm",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("span",{className:"font-medium",children:[e.jsx("span",{className:"text-gray-500",children:"From:"})," ",s.from]})}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("span",{className:"font-medium",children:[e.jsx("span",{className:"text-gray-500",children:"Until:"})," ",s.until]}),e.jsx("button",{onClick:()=>be(t),className:"ml-2 rounded-full p-1 text-red-500 hover:bg-red-50",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M13 7L7 13M13 13L7 7M19.25 10C19.25 15.1086 15.1086 19.25 10 19.25C4.89137 19.25 0.75 15.1086 0.75 10C0.75 4.89137 4.89137 0.75 10 0.75C15.1086 0.75 19.25 4.89137 19.25 10Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round"})})})]})]},t))}),e.jsx("button",{onClick:fe,className:"mt-4 w-full text-sm text-blue-600",children:"+ Add another time"}),e.jsx("button",{onClick:()=>E("details"),className:"mt-6 w-full rounded-lg bg-blue-900 py-3 text-white",children:"Next: Players"})]})]}):e.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[e.jsxs("div",{className:"h-fit space-y-6 rounded-lg bg-white p-4 shadow-5",children:[e.jsx("div",{className:"rounded-lg bg-gray-50 p-2",children:e.jsx("p",{className:"text-center text-lg font-medium",children:"Summary"})}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"SPORT"}),e.jsxs("p",{children:[p&&((Q=b.find(s=>s.id===p))==null?void 0:Q.name),"•"," ",g&&((H=W.find(s=>s.id==g))==null?void 0:H.label),"• (",f&&((J=j.find(s=>s.id===f))==null?void 0:J.name),")"]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"DATE"}),e.jsx("p",{children:u==null?void 0:u.toLocaleDateString("en-US",{month:"long",day:"numeric",year:"numeric"})})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"TIME SLOTS"}),o.map((s,t)=>e.jsxs("p",{children:[s.from," - ",s.until]},t))]})]})]}),e.jsxs("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:[e.jsx("div",{className:"mb-5 rounded-lg bg-gray-50 p-2",children:e.jsx("p",{className:"text-center text-lg font-medium",children:"Players"})}),e.jsx("div",{className:"mb-4",children:e.jsx("div",{className:"mb-4",children:e.jsxs("div",{className:"flex  items-center gap-1 rounded-lg border border-gray-300 px-2 focus:border-blue-500 focus:outline-none",children:[e.jsx("span",{className:"w-5",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.25 2.5C12.976 2.5 16 5.524 16 9.25C16 12.976 12.976 16 9.25 16C5.524 16 2.5 12.976 2.5 9.25C2.5 5.524 5.524 2.5 9.25 2.5ZM9.25 14.5C12.1502 14.5 14.5 12.1502 14.5 9.25C14.5 6.349 12.1502 4 9.25 4C6.349 4 4 6.349 4 9.25C4 12.1502 6.349 14.5 9.25 14.5ZM15.6137 14.5532L17.7355 16.6742L16.6742 17.7355L14.5532 15.6137L15.6137 14.5532Z",fill:"#525866"})})}),e.jsx("input",{type:"text",placeholder:"Search by name",value:M,onChange:s=>G(s.target.value),className:"w-full border-none bg-transparent text-sm focus:outline-none focus:ring-0"})]})})}),e.jsx("div",{className:"",children:m.length>0&&e.jsx("p",{className:"mb-3 flex flex-row flex-wrap items-center gap-2 gap-y-2 text-sm text-gray-500",children:m.map(s=>{const t=A.find(l=>l.id===s);return e.jsxs("div",{className:"flex w-fit items-center gap-2 rounded-full bg-gray-50 p-1 px-2",children:[e.jsx("img",{src:(t==null?void 0:t.photo)||"/default-avatar.png",className:"h-6 w-6 rounded-full",alt:(t==null?void 0:t.first_name)+" "+(t==null?void 0:t.last_name)}),e.jsxs("div",{className:"text-sm text-black",children:[t==null?void 0:t.first_name," ",t==null?void 0:t.last_name]}),e.jsx("div",{children:e.jsx("svg",{width:"15",height:"15",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M13 7L7 13M13 13L7 7M19.25 10C19.25 15.1086 15.1086 19.25 10 19.25C4.89137 19.25 0.75 15.1086 0.75 10C0.75 4.89137 4.89137 0.75 10 0.75C15.1086 0.75 19.25 4.89137 19.25 10Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round"})})})]},s)})})}),e.jsxs("div",{className:"max-h-[400px] space-y-2 overflow-y-auto rounded-lg bg-gray-50 p-2",children:[x.length>0&&x.map(s=>e.jsxs("div",{className:"flex items-center gap-2 p-2",children:[e.jsx("input",{type:"checkbox",className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500",checked:m.includes(s.id),onChange:()=>F(s.id)}),e.jsx("img",{src:s.photo||"/default-avatar.png",className:"h-8 w-8 rounded-full",alt:s.name}),e.jsxs("span",{children:[s.first_name," ",s.last_name]})]},s.id)),!x.length&&e.jsx("p",{className:"text-center text-sm text-gray-500",children:"No players found"})]})]}),e.jsxs("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:[e.jsx("div",{className:"rounded-lg bg-gray-50 p-2",children:e.jsx("p",{className:"text-center text-lg font-medium",children:"Other details"})}),e.jsxs("div",{className:"mt-4 space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-3 block text-sm text-gray-500",children:"My group NTRP score"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("div",{className:"flex h-10 min-h-[40px] flex-1 shrink basis-0 items-start justify-center overflow-hidden rounded-xl border border-solid border-zinc-200 bg-white shadow-sm",children:[e.jsx("div",{className:"flex w-fit items-center justify-center gap-2 self-stretch bg-slate-50 px-2 py-2.5 text-neutral-400",children:"Min"}),e.jsx("select",{className:"flex flex-1 shrink basis-0 items-center justify-between overflow-hidden border-l border-none border-zinc-200 bg-white px-2 py-2.5 text-gray-950 outline-none focus:outline-none focus:ring-0",value:D,onChange:s=>oe(s.target.value),children:X.map(s=>e.jsx("option",{value:s,children:s},s))})]}),e.jsxs("div",{className:"flex h-10 min-h-[40px] flex-1 shrink basis-0 items-start justify-center overflow-hidden rounded-xl border border-solid border-zinc-200 bg-white shadow-sm",children:[e.jsx("div",{className:"flex w-fit items-center justify-center gap-2 self-stretch bg-slate-50 px-2 py-2.5 text-neutral-400",children:"Max"}),e.jsx("select",{className:"flex flex-1 shrink basis-0 items-center justify-between overflow-hidden border-l border-none border-zinc-200 bg-white px-2 py-2.5 text-gray-950 outline-none focus:outline-none focus:ring-0",value:O,onChange:s=>de(s.target.value),children:X.map(s=>e.jsx("option",{value:s,children:s},s))})]})]})]}),e.jsxs("div",{className:"flex items-center justify-between gap-2",children:[e.jsx("label",{className:"block flex-1 text-sm font-medium",children:"Players playing"}),e.jsx("select",{value:U,onChange:s=>me(Number(s.target.value)),className:"max-w-24 flex-1 flex-shrink rounded-xl border border-gray-300 p-2",children:[1,2,3,4].map(s=>e.jsx("option",{value:s,children:s},s))})]}),e.jsxs("div",{className:"flex items-center justify-between gap-2",children:[e.jsx("label",{className:"block flex-1 text-sm font-medium",children:"Players needed"}),e.jsx("select",{value:Z,onChange:s=>ue(Number(s.target.value)),className:"max-w-24 flex-1 flex-shrink rounded-xl border border-gray-300 p-2",children:[1,2,3,4].map(s=>e.jsx("option",{value:s,children:s},s))})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium",children:["Short bio ",e.jsx("span",{className:"text-gray-500",children:"(Optional)"})]}),e.jsx("textarea",{value:$,onChange:s=>xe(s.target.value),className:"mt-2 w-full rounded-xl border border-gray-300 p-2",rows:3})]}),e.jsx(Te,{onClick:ye,loading:_,className:"mt-4 w-full rounded-xl bg-blue-900 py-3 font-medium text-white disabled:opacity-50",children:_?"Submitting...":"Submit request"})]})]})]}),se&&e.jsx(Pe,{onContinue:()=>v(!1),title:"Request made successfully",onClose:()=>{v(!1),N("/club/find-a-buddy")},description:"Request made successfully. A confirmation email has been sent to the players."})]})};export{Ls as default};

import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as i,f as Ye}from"./vendor-851db8c1.js";import{B as Ie}from"./BackButton-11ba52b2.js";import{G as Pe,a0 as Ee,ao as Ae,M as He,$ as se,R as Le,a1 as Re,d as ze,H as We,E as Oe,J as Je,b as D}from"./index-f915b394.js";import{C as qe}from"./Calendar-9031b5fe.js";import{T as Fe}from"./TimeSlots-683a5ba6.js";import{S as Ge}from"./SportTypeSelection-ee0cc3da.js";import{h as A}from"./moment-a9aaa855.js";let H=new He;function st({club:x,coaches:oe,role:ae,sports:L}){const[re,R]=i.useState("selection"),[v,ce]=i.useState(null),[T,de]=i.useState(null),[B,me]=i.useState(null),[Y,K]=i.useState(new Date),[S,ue]=i.useState(null),[I,he]=i.useState(null),[V,ne]=i.useState(!1),[z,xe]=i.useState([]),ge=Ye(),pe=localStorage.getItem("user"),[X,fe]=i.useState(""),[W,be]=i.useState(""),[le,ye]=i.useState(""),[O,je]=i.useState("0.00"),[J,Ne]=i.useState(0),[ie,q]=i.useState(!1),[N,Q]=i.useState([]),[ve,Z]=i.useState(!1),[c,Se]=i.useState({id:null,hours:[]}),{dispatch:w}=i.useContext(Pe),we=i.useCallback(({sport:d,type:p,subType:t})=>{ce(d),de(p),me(t)},[]),ke=()=>{K(new Date(Y.getFullYear(),Y.getMonth()+1,1))},Ce=()=>{K(new Date(Y.getFullYear(),Y.getMonth()-1,1))},Me=async()=>{ne(!0);try{const{end_time:d,start_time:p}=se(z),t=N.filter(s=>{var l;return!((l=s.hours)!=null&&l.length)});if(t.length>0){const s=t.map(l=>{var C,_;return`${(C=l.user)==null?void 0:C.first_name} ${(_=l.user)==null?void 0:_.last_name}`}).join(", ");throw new Error(`Please set hours for the following coaches: ${s}`)}const o=N.map(s=>({coach_id:s.id,hours:s.hours,start_time:s.hours[0],end_time:s.hours[s.hours.length-1]})),g={name:X,max_participants:parseInt(W),description:le,cost_per_head:parseFloat(O),date:A(S).format("YYYY-MM-DD"),end_date:I?A(I).format("YYYY-MM-DD"):null,sport_id:parseInt(v),sub_type:B,type:T,start_time:p,end_time:d,club_id:x==null?void 0:x.id,recurring:J===1||J===!0?1:0},b={name:X,max_participants:W,cost_per_head:O,date:A(S).format("YYYY-MM-DD"),sport_id:v,coaches:N},y=Object.entries(b).filter(([s,l])=>!l||Array.isArray(l)&&!l.length).map(([s])=>s.replace(/_/g," "));if(y.length>0)throw new Error(`Please fill in the following required fields: ${y.join(", ")}`);console.log("Submitting clinic data:",g),H.setTable("clinics");const k=await H.callRestAPI(g,"POST");k.error?D(w,k.message||"Failed to create clinic",3e3,"error"):(o.forEach(async s=>{const l={clinic_id:k.data,coach_id:s.coach_id,data:JSON.stringify({working_hours:s.hours,fees:O,sport_id:parseInt(v),sub_type:B,type:T,court_ids:"",number_of_players:W})};H.setTable("clinic_coaches"),await H.callRestAPI(l,"POST")}),await We(H,{user_id:pe,activity_type:Oe.clinic,action_type:Je.CREATE,data:g,club_id:x==null?void 0:x.id,description:"Created a clinic"}),D(w,"Clinic created successfully",3e3,"success"),ge(ae==="club"?"/club/program-clinics":"/admin/program-clinics"))}catch(d){console.error("Error submitting clinic:",d),D(w,d.message,3e3,"error")}finally{ne(!1)}};i.useEffect(()=>{w({type:"SETPATH",payload:{path:"program-clinics"}})},[x]);const Te=d=>{xe([{from:d.from,until:d.until}])},Be=()=>{var F,P,G,U,E,$;const{duration:d,end_time:p,start_time:t}=se(z),o=n=>{if(!n)return 0;const[r,m]=n.split(":").map(Number);return r*60+m},g=n=>{if(!n)return"";const[r,m]=n.split(":").map(Number),u=r>=12?"PM":"AM";return`${r%12||12}:${m.toString().padStart(2,"0")} ${u}`},b=g(t),y=g(p),s=((n,r)=>{const m=[],u=o(n),h=o(r);for(let a=u;a<h;a+=30){const f=Math.floor(a/60),j=a%60,M=`${f.toString().padStart(2,"0")}:${j.toString().padStart(2,"0")}:00`;m.push({time24:M,time12:g(M)})}return m})(t,p),[l,C]=i.useState(((F=c==null?void 0:c.hours)==null?void 0:F.filter(n=>{if(!t||!p)return!0;const r=n.split(" ")[0]+":00";return o(r)>=o(t)&&o(r)<=o(p)}))||[]),_=n=>{C(r=>{const m=s.findIndex(h=>h.time12===n.time12),u=r.map(h=>s.findIndex(a=>a.time12===h)).sort((h,a)=>h-a);if(r.includes(n.time12)){if(r.length>=2){const h=u[0],a=u[u.length-1];if(m===h||m===a)return r.filter(f=>f!==n.time12)}else if(r.length===1)return[];return r}else{if(r.length===0)return[n.time12];{const h=u[0],a=u[u.length-1];if(m===h-1||m===a+1){const f=r.length+1,j=Math.floor(d*2);return f>j?(D(w,`Cannot select more than ${d} ${d===1?"hour":"hours"} (clinic duration)`,3e3,"error"),r):[...r,n.time12]}else return[n.time12]}}})},ee=()=>{if(l.filter(m=>{const[u,h]=m.split(" "),[a,f]=u.split(":").map(Number);let j=a;h==="PM"&&a!==12?j=a+12:h==="AM"&&a===12&&(j=0);const M=`${j.toString().padStart(2,"0")}:${f.toString().padStart(2,"0")}:00`;return o(M)<o(t)||o(M)>=o(p)}).length>0){D(w,`Selected hours must be between ${b} and ${y}`,3e3,"error");return}const r=l.length*.5;if(r>d){D(w,`Coach hours (${r} ${r===1?"hour":"hours"}) cannot exceed clinic duration (${d} ${d===1?"hour":"hours"})`,3e3,"error");return}Q(m=>m.map(u=>u.id===c.id?{...u,hours:l}:u)),Z(!1)};return e.jsx(Le,{isOpen:ve,onClose:()=>Z(!1),title:"Set Coach Hours",primaryButtonText:"Save Hours",onPrimaryAction:ee,children:e.jsxs("div",{className:"flex flex-col space-y-6",children:[e.jsxs("div",{className:"flex items-center space-x-3 rounded-xl bg-gradient-to-r from-blue-50 to-indigo-50 p-4",children:[e.jsx("div",{className:"h-12 w-12 overflow-hidden rounded-full shadow-sm ring-2 ring-white",children:e.jsx("img",{src:((P=c==null?void 0:c.user)==null?void 0:P.photo)||"/default-avatar.png",alt:`${(G=c==null?void 0:c.user)==null?void 0:G.first_name} ${(U=c==null?void 0:c.user)==null?void 0:U.last_name}`,className:"h-full w-full object-cover"})}),e.jsxs("div",{children:[e.jsxs("p",{className:"font-semibold text-gray-900",children:[(E=c==null?void 0:c.user)==null?void 0:E.first_name," ",($=c==null?void 0:c.user)==null?void 0:$.last_name]}),e.jsx("p",{className:"text-sm text-gray-600",children:"Select available hours"})]})]}),b&&y&&e.jsx("div",{className:"rounded-lg border border-amber-200 bg-amber-50 p-4",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("svg",{className:"h-5 w-5 text-amber-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),e.jsxs("p",{className:"font-medium text-amber-800",children:["Clinic Duration: ",b," - ",y," (",d,"h)"]})]})}),l.length>0&&e.jsx("div",{className:"rounded-lg border border-green-200 bg-green-50 p-4",children:e.jsxs("div",{className:"flex items-start space-x-2",children:[e.jsx("svg",{className:"mt-0.5 h-5 w-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),e.jsxs("div",{children:[e.jsxs("p",{className:"font-medium text-green-800",children:["Selected Hours (",l.length*.5," ",l.length===2?"hour":"hours","):"]}),e.jsx("div",{className:"text-sm text-green-700",children:(()=>{var a;if(l.length===0)return"";const n=[...l].sort((f,j)=>{const M=s.findIndex(te=>te.time12===f),De=s.findIndex(te=>te.time12===j);return M-De}),r=n[0],m=n[n.length-1],u=s.findIndex(f=>f.time12===m),h=((a=s[u+1])==null?void 0:a.time12)||m;return`From: ${r} Until: ${h}`})()})]})]})}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-3 font-medium text-gray-900",children:"Available Time Slots"}),e.jsx("div",{className:"grid grid-cols-2 gap-3",children:s.map(n=>e.jsx("button",{onClick:()=>_(n),className:`w-full rounded-lg border-2 p-3 text-center font-medium transition-all duration-200 hover:scale-105
                    ${l.includes(n.time12)?"border-primaryBlue bg-primaryBlue text-white shadow-md":"border-gray-200 text-gray-700 hover:border-primaryBlue hover:bg-blue-50 hover:text-primaryBlue"}
                  `,children:n.time12},n.time12))})]})]})})},_e=()=>{const d=t=>{const o=L==null?void 0:L.find(g=>g.id===t);return(o==null?void 0:o.name)||"Unknown Sport"},p=t=>{if(!t||t.length===0)return"No time selected";const{start_time:o,end_time:g,duration:b}=se(t);return`${o} - ${g} (${b}h)`};return e.jsxs("div",{className:"mx-auto p-4 md:container",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Create New Clinic"}),e.jsx("p",{className:"mt-2 text-gray-600",children:"Fill in the details for your clinic and select coaches."})]}),e.jsxs("div",{className:"mb-8 rounded-xl border border-blue-100 bg-blue-50 p-6",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold text-blue-900",children:"Selected Information"}),e.jsxs("button",{onClick:()=>R("selection"),className:"flex items-center space-x-2 rounded-lg border border-blue-200 bg-white px-3 py-2 text-sm font-medium text-blue-700 transition-colors hover:bg-blue-50",children:[e.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),e.jsx("span",{children:"Edit Selection"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4",children:[e.jsxs("div",{className:"rounded-lg bg-white p-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Sport"}),e.jsx("p",{className:"mt-1 text-lg font-semibold text-gray-900",children:v?d(v):"Not selected"}),T&&e.jsxs("p",{className:"mt-1 text-sm text-gray-600",children:["Type: ",T]}),B&&e.jsxs("p",{className:"text-sm text-gray-600",children:["Subtype: ",B]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Start Date"}),e.jsx("p",{className:"mt-1 text-lg font-semibold text-gray-900",children:S?A(S).format("MMM DD, YYYY"):"Not selected"})]}),e.jsxs("div",{className:"rounded-lg bg-white p-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"End Date"}),e.jsx("p",{className:"mt-1 text-lg font-semibold text-gray-900",children:I?A(I).format("MMM DD, YYYY"):"Not set (optional)"})]}),e.jsxs("div",{className:"rounded-lg bg-white p-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Time"}),e.jsx("p",{className:"mt-1 text-lg font-semibold text-gray-900",children:p(z)})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-8 lg:grid-cols-2",children:[e.jsxs("div",{className:"h-fit space-y-6 rounded-xl border border-gray-100 bg-white p-6 shadow-lg",children:[e.jsxs("div",{className:"border-b border-gray-200 pb-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Clinic Information"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Basic details about your clinic"})]}),e.jsxs("div",{className:"space-y-5",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-semibold text-gray-700",children:"Clinic Name *"}),e.jsx("input",{type:"text",value:X,onChange:t=>fe(t.target.value),placeholder:"Enter clinic name",className:"block w-full rounded-lg border border-gray-300 px-4 py-3 text-gray-900 placeholder-gray-500 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-semibold text-gray-700",children:"Maximum Players *"}),e.jsx("input",{type:"number",value:W,onChange:t=>be(t.target.value),placeholder:"Enter max number of players",min:"1",className:"block w-full rounded-lg border border-gray-300 px-4 py-3 text-gray-900 placeholder-gray-500 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-semibold text-gray-700",children:"Description"}),e.jsx("textarea",{value:le,onChange:t=>ye(t.target.value),rows:4,placeholder:"Describe your clinic...",className:"block w-full resize-none rounded-lg border border-gray-300 px-4 py-3 text-gray-900 placeholder-gray-500 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-semibold text-gray-700",children:"Recurring Event"}),e.jsxs("select",{value:J===1||J===!0?"Yes":"No",onChange:t=>Ne(t.target.value==="Yes"?1:0),className:"block w-full rounded-lg border border-gray-300 px-4 py-3 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20",children:[e.jsx("option",{value:"No",children:"No"}),e.jsx("option",{value:"Yes",children:"Yes"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-semibold text-gray-700",children:"Price per Person *"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-4",children:e.jsx("span",{className:"font-medium text-gray-500",children:"$"})}),e.jsx("input",{type:"number",value:O,onChange:t=>je(t.target.value),placeholder:"0.00",min:"0",step:"0.01",className:"block w-full rounded-lg border border-gray-300 py-3 pl-8 pr-4 text-gray-900 placeholder-gray-500 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20"})]})]})]})]}),e.jsxs("div",{className:"h-fit w-full space-y-6 rounded-xl border border-gray-100 bg-white p-6 shadow-lg",children:[e.jsx("div",{className:"border-b border-gray-200 pb-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Coaches"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Select and set hours for coaches"})]}),e.jsxs("span",{className:"rounded-full bg-primaryBlue/10 px-3 py-1 text-sm font-semibold text-primaryBlue",children:[N.length," selected"]})]})}),N.length===0?e.jsxs("div",{className:"rounded-lg border-2 border-dashed border-gray-200 p-8 text-center",children:[e.jsx("div",{className:"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100",children:e.jsx("svg",{className:"h-6 w-6 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"})})}),e.jsx("p",{className:"mb-4 text-gray-500",children:"No coaches selected yet"}),e.jsxs("button",{onClick:()=>q(!0),className:"inline-flex items-center rounded-lg bg-primaryBlue px-4 py-2 text-white transition-colors hover:bg-primaryBlue/90",children:[e.jsx("svg",{className:"mr-2 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),"Add coaches"]})]}):e.jsxs("div",{className:"space-y-3",children:[N.map(t=>{var o,g,b,y,k;return e.jsxs("div",{className:"flex items-center justify-between rounded-lg border border-gray-200 bg-gray-50 p-4 transition-all hover:shadow-sm",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"h-10 w-10 overflow-hidden rounded-full shadow-sm ring-2 ring-white",children:e.jsx("img",{src:((o=t==null?void 0:t.user)==null?void 0:o.photo)||"/default-avatar.png",alt:`${(g=t.user)==null?void 0:g.first_name} ${(b=t.user)==null?void 0:b.last_name}`,className:"h-full w-full object-cover"})}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"font-semibold text-gray-900",children:`${(y=t==null?void 0:t.user)==null?void 0:y.first_name} ${(k=t==null?void 0:t.user)==null?void 0:k.last_name}`}),e.jsx("span",{className:"text-sm text-gray-600",children:t.hours.length>0?(()=>{const s=[...t.hours].sort((ee,F)=>{const P=G=>{const[U,E]=G.split(" "),[$,n]=U.split(":").map(Number);let r=$;return E==="PM"&&$!==12&&(r+=12),E==="AM"&&$===12&&(r=0),r*60+n};return P(ee)-P(F)}),l=s.length*.5,C=s[0],_=s[s.length-1];return s.length===1?`${C} (0.5 hours)`:`${C} - ${_} (${l} ${l===1?"hour":"hours"})`})():"No hours set"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{className:"rounded-lg border border-primaryBlue bg-white px-3 py-2 text-sm font-medium text-primaryBlue transition-colors hover:bg-blue-50",onClick:()=>{Se(t),Z(!0)},children:t.hours.length>0?"Edit hours":"Set hours"}),e.jsx("button",{onClick:()=>Q(s=>s.filter(l=>l.id!==t.id)),className:"rounded-lg border border-red-200 p-2 text-red-400 transition-colors hover:bg-red-50 hover:text-red-600",title:"Remove coach",children:e.jsx(Re,{className:"h-4 w-4"})})]})]},t.id)}),e.jsx("button",{onClick:()=>q(!0),className:"w-full rounded-lg border-2 border-dashed border-gray-300 p-3 text-center text-sm font-medium text-gray-600 transition-colors hover:border-primaryBlue hover:text-primaryBlue",children:"+ Add more coaches"})]}),e.jsx("div",{className:"border-t border-gray-200 pt-6",children:e.jsx(ze,{onClick:Me,loading:V,className:`w-full rounded-lg py-4 text-lg font-semibold text-white shadow-lg transition-all duration-200
                  ${V?"cursor-not-allowed bg-primaryBlue/70":"transform bg-primaryBlue hover:-translate-y-0.5 hover:bg-primaryBlue/90 hover:shadow-xl"}
                `,children:V?e.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[e.jsxs("svg",{className:"h-5 w-5 animate-spin",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),e.jsx("span",{children:"Creating Clinic..."})]}):e.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[e.jsx("span",{children:"Create Clinic"}),e.jsx("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 7l5 5m0 0l-5 5m5-5H6"})})]})})})]})]})]})},$e=()=>{if(re==="details")R("selection");else return!0;return!1};return console.log("selected coaches",N),e.jsxs(e.Fragment,{children:[e.jsx(Ie,{onBack:$e}),re==="selection"?e.jsx("div",{className:"mx-auto max-w-7xl p-4",children:e.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[e.jsx(Ge,{onSelectionChange:we,sports:L||[],initialSport:v,initialType:T,initialSubType:B},`${v}-${T}-${B}`),e.jsx("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:e.jsx(qe,{currentMonth:Y,selectedDate:S,selectedEndDate:I,onDateSelect:ue,onEndDateSelect:he,allowRangeSelection:!0,onMonthChange:K,showNextButton:!1,nextButtonText:"Next",onNextMonth:ke,onPreviousMonth:Ce,daysOff:x!=null&&x.days_off?JSON.parse(x.days_off):[],onNextButtonClick:()=>{R("details")}})}),S&&e.jsx(Fe,{onTimeClick:Te,selectedDate:S,timeRange:z,timeSlots:Ee(),onNext:()=>{R("details")},nextButtonText:"Next",startHour:0,endHour:24,interval:30,className:"h-fit rounded-lg bg-white p-4 shadow-5",multipleSlots:!1,individualSelection:!0,clubTimes:x!=null&&x.times?JSON.parse(x.times):[],isTimeSlotAvailable:()=>!0})]})}):_e(),ie&&e.jsx(Ae,{coaches:oe,selectedCoaches:N,setSelectedCoaches:Q,isOpen:ie,onClose:()=>q(!1),onSave:()=>q(!1)}),e.jsx(Be,{})]})}export{st as A};

import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as o,r as Z}from"./vendor-851db8c1.js";import{ae as P,af as O,ag as E,ah as F}from"./index-f915b394.js";function $({isOpen:u,onClose:h,contactOptions:i}){if(!u)return null;const d=n=>{n.type==="phone"?window.location.href=`tel:${n.value}`:n.type==="email"&&(window.location.href=`mailto:${n.value}`),h()};return e.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50",children:e.jsxs("div",{className:"mx-4 w-full max-w-md rounded-lg bg-white p-6 shadow-lg",children:[e.jsx("h3",{className:"mb-4 text-lg font-semibold text-gray-900",children:"Contact Club"}),e.jsx("p",{className:"mb-6 text-sm text-gray-600",children:"How would you like to contact the club?"}),e.jsx("div",{className:"space-y-3",children:i.map((n,f)=>e.jsxs("button",{onClick:()=>d(n),className:"flex w-full items-center gap-3 rounded-lg border border-gray-200 p-3 text-left hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("div",{className:"flex h-10 w-10 items-center justify-center rounded-full bg-blue-100",children:n.type==="phone"?e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M2 3C2 2.44772 2.44772 2 3 2H5.15287C5.64171 2 6.0589 2.35341 6.13927 2.8356L6.87858 7.27147C6.95075 7.70451 6.73206 8.13397 6.3394 8.3303L4.79126 9.10437C5.90756 11.8783 8.12168 14.0924 10.8956 15.2087L11.6697 13.6606C11.866 13.2679 12.2955 13.0492 12.7285 13.1214L17.1644 13.8607C17.6466 13.9411 18 14.3583 18 14.8471V17C18 17.5523 17.5523 18 17 18H15C7.8203 18 2 12.1797 2 5V3Z",fill:"#3B82F6"})}):e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M2.5 6.5L9.455 11.952C9.782 12.183 10.218 12.183 10.545 11.952L17.5 6.5M4 4H16C17.105 4 18 4.895 18 6V14C18 15.105 17.105 16 16 16H4C2.895 16 2 15.105 2 14V6C2 4.895 2.895 4 4 4Z",stroke:"#3B82F6",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-gray-900",children:n.label}),e.jsx("div",{className:"text-sm text-gray-500",children:n.value})]})]},f))}),e.jsx("div",{className:"mt-6 flex justify-end",children:e.jsx("button",{onClick:h,className:"rounded-lg border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500",children:"Cancel"})})]})})}function D({clubName:u,description:h,imageList:i,clubLogo:d,slideshowDelay:n=6e3,buttonText:f="Let the club know you're interested",phone:p,email:v}){const[g,y]=o.useState(!1),[b,x]=o.useState(0),[V,N]=o.useState(!1),[k,H]=o.useState([]),[r,m]=o.useState(!0);console.log("imageList",i);const s=o.useMemo(()=>{if(!i||!Array.isArray(i))return[];const l=a=>a&&a.url,t=i.filter(l),C=[];for(let a=0;a<t.length;a+=3)C.push(t.slice(a,a+3));return C},[i]);Z.useEffect(()=>{if(s.length<=1||!r)return;const l=setInterval(()=>{x(t=>(t+1)%s.length)},n);return()=>clearInterval(l)},[s,n,r]);const M=l=>{x(l)},L=()=>{s.length<=1||x(l=>(l-1+s.length)%s.length)},S=()=>{s.length<=1||x(l=>(l+1)%s.length)},w=()=>s.length<=1?null:e.jsx("div",{className:"absolute bottom-4 left-1/2 flex -translate-x-1/2 gap-2",children:s.map((l,t)=>e.jsx("button",{onClick:()=>M(t),className:`h-2 w-2 rounded-full transition-colors ${b===t?"bg-white":"bg-white/50"}`},t))}),j=()=>s.length<=1?null:e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:l=>{l.stopPropagation(),L()},className:"absolute left-2 top-1/2 -translate-y-1/2 rounded-full bg-black/50 p-2 text-white hover:bg-black/70",children:e.jsx(P,{className:"h-6 w-6"})}),e.jsx("button",{onClick:l=>{l.stopPropagation(),S()},className:"absolute right-2 top-1/2 -translate-y-1/2 rounded-full bg-black/50 p-2 text-white hover:bg-black/70",children:e.jsx(O,{className:"h-6 w-6"})})]}),c=(l,t)=>l!=null&&l.url?e.jsx("div",{className:"relative h-full w-full overflow-hidden rounded-lg bg-gray-100",children:l.type==="video"?I(l):l.type==="pdf"?e.jsx("div",{className:"flex h-full w-full items-center justify-center rounded-lg bg-red-50",children:e.jsxs("div",{className:"text-center",children:[e.jsx("svg",{width:"48",height:"48",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"mx-auto mb-3",children:e.jsx("path",{d:"M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.89 22 5.99 22H18C19.1 22 20 21.1 20 20V8L14 2ZM18 20H6V4H13V9H18V20Z",fill:"#DC2626"})}),e.jsx("p",{className:"text-sm font-medium text-red-600",children:"PDF Document"}),e.jsx("p",{className:"mt-1 text-xs text-red-500",children:"Click to view"})]})}):e.jsx("img",{src:l.url,alt:`Media ${t+1}`,className:"h-full w-full object-cover"})}):null,I=l=>l!=null&&l.url?e.jsxs("div",{className:"relative h-full w-full",children:[e.jsx("video",{src:l.url,className:"h-full w-full object-cover",loop:!0,muted:g,autoPlay:!0,playsInline:!0}),e.jsx("button",{onClick:()=>y(!g),className:"absolute right-2 top-2 rounded-full bg-black/50 p-2 text-white hover:bg-black/70",children:g?e.jsx(E,{className:"h-5 w-5"}):e.jsx(F,{className:"h-5 w-5"})})]}):null,B=()=>{if(!s.length)return null;const l=s[b]||[],t=l.length;return t===0?null:t===1?e.jsxs("div",{className:"relative h-full w-full",children:[e.jsx("div",{className:"h-full",children:c(l[0],0)}),j(),w(),s.length>1&&e.jsx("button",{onClick:()=>m(!r),className:"absolute right-2 top-2 rounded-full bg-black/50 p-2 text-white hover:bg-black/70",children:r?e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M6 4H10V20H6V4ZM14 4H18V20H14V4Z",fill:"white"})}):e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 5V19L19 12L8 5Z",fill:"white"})})})]}):t===2?e.jsxs("div",{className:"relative h-full w-full",children:[e.jsxs("div",{className:"grid h-full w-full grid-rows-2 gap-4",children:[e.jsx("div",{className:"h-full min-h-0",children:c(l[0],0)}),e.jsx("div",{className:"h-full min-h-0",children:c(l[1],1)})]}),j(),w(),s.length>1&&e.jsx("button",{onClick:()=>m(!r),className:"absolute right-2 top-2 rounded-full bg-black/50 p-2 text-white hover:bg-black/70",children:r?e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M6 4H10V20H6V4ZM14 4H18V20H14V4Z",fill:"white"})}):e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 5V19L19 12L8 5Z",fill:"white"})})})]}):e.jsxs("div",{className:"relative h-full w-full",children:[e.jsxs("div",{className:"grid h-full w-full grid-rows-[1fr_1fr] gap-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx("div",{className:"h-full min-h-0",children:c(l[0],0)}),e.jsx("div",{className:"h-full min-h-0",children:c(l[1],1)})]}),e.jsx("div",{className:"h-full min-h-0",children:c(l[2],2)})]}),j(),w(),s.length>1&&e.jsx("button",{onClick:()=>m(!r),className:"absolute right-2 top-2 rounded-full bg-black/50 p-2 text-white hover:bg-black/70",children:r?e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M6 4H10V20H6V4ZM14 4H18V20H14V4Z",fill:"white"})}):e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 5V19L19 12L8 5Z",fill:"white"})})})]})};return e.jsxs("div",{className:"flex flex-grow flex-col bg-white",children:[e.jsxs("header",{className:"flex items-center justify-between p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[d?e.jsx("img",{src:d,alt:"Club Logo",className:"h-10 w-10 rounded-lg object-cover"}):e.jsx("span",{className:"text-2xl",children:"🎾"}),e.jsx("span",{className:"text-xl font-semibold",children:u})]}),e.jsx("div",{className:"flex gap-3",children:e.jsxs("button",{className:"flex items-center rounded-xl border border-gray-300 px-4 py-2 text-gray-500",children:[e.jsx("span",{children:"Log in"}),e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"ml-2",children:e.jsx("path",{d:"M12.2917 3.125L16.0417 3.125C16.5019 3.125 16.875 3.4981 16.875 3.95833V16.0417C16.875 16.5019 16.5019 16.875 16.0417 16.875H12.2917M12.5 10H3.125M12.5 10L9.58333 12.9167M12.5 10L9.58333 7.08334",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})]})})]}),e.jsxs("main",{className:"flex flex-1 flex-col gap-8 p-4 md:flex-row md:p-8",children:[e.jsx("div",{className:"flex-1",children:e.jsx("div",{className:"relative h-[600px] w-full md:mx-auto md:max-w-6xl",children:B()})}),e.jsxs("div",{className:"flex flex-1 flex-col justify-between",children:[e.jsxs("div",{className:"flex max-h-[400px] flex-col gap-4 lg:max-h-[500px] 2xl:max-h-[550px]",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h1",{className:"text-2xl font-medium",children:"Welcome!"}),e.jsxs("button",{className:"flex items-center gap-2 text-gray-600",children:[e.jsx("span",{children:"Visit website"}),e.jsx("svg",{width:"21",height:"20",viewBox:"0 0 21 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M11.2958 9.99926L7.58331 6.28676L8.64381 5.22626L13.4168 9.99926L8.64381 14.7723L7.58331 13.7118L11.2958 9.99926Z",fill:"#525866"})})]})]}),e.jsx("div",{className:"h-full space-y-4 overflow-y-auto",children:e.jsx("p",{className:"text-gray-600",children:h})})]}),e.jsxs("div",{className:"mt-0 rounded-xl p-4 shadow-5",children:[e.jsx("button",{onClick:()=>{const l=[];p&&l.push({type:"phone",value:p,label:"Call Club"}),v&&l.push({type:"email",value:v,label:"Email Club"}),l.length>0&&(H(l),N(!0))},className:"w-full rounded-xl bg-[#176448] py-2 text-white",children:e.jsxs("span",{className:"flex items-center justify-center gap-2",children:[e.jsxs("svg",{width:"25",height:"25",viewBox:"0 0 25 25",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M16 3.5H9V4.75C9 5.16421 8.66421 5.5 8.25 5.5C7.83579 5.5 7.5 5.16421 7.5 4.75V3.5H5.25C4.2835 3.5 3.5 4.2835 3.5 5.25V8H21.5V5.25C21.5 4.2835 20.7165 3.5 19.75 3.5H17.5V4.75C17.5 5.16421 17.1642 5.5 16.75 5.5C16.3358 5.5 16 5.16421 16 4.75V3.5Z",fill:"white"}),e.jsx("path",{d:"M21.5 9.5H3.5V19.75C3.5 20.7165 4.2835 21.5 5.25 21.5H19.75C20.7165 21.5 21.5 20.7165 21.5 19.75V9.5Z",fill:"white"})]}),f]})}),e.jsx("p",{className:"mt-5 text-center text-sm text-gray-500",children:"TAKES ONLY 2 MINUTES!"})]})]})]}),e.jsxs("footer",{className:"mt-auto flex items-center justify-center gap-2 border-t p-0 text-center text-sm text-gray-500",children:[e.jsx("p",{children:"Powered by"}),e.jsx("img",{loading:"lazy",src:"/courtmatchup-logo.png",alt:"",className:"my-auto aspect-square w-28 shrink-0 self-stretch object-contain"})]}),e.jsx($,{isOpen:V,onClose:()=>N(!1),contactOptions:k})]})}export{D as S};

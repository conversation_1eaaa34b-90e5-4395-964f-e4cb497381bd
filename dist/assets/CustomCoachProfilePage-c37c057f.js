import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r,b as D,k as re,f as le}from"./vendor-851db8c1.js";import{M as Y,G as X,b as k,e as Q,T as oe,A as ne,d as ae,t as ee,u as ie,f as ce,h as de,i as pe,F as me,j as ue,k as xe,l as he}from"./index-f915b394.js";import{B as ye}from"./BackButton-11ba52b2.js";import{S as fe}from"./StripeConnectionStatus-fadf3534.js";import{u as ge}from"./react-hook-form-687afde5.js";import{o as be}from"./yup-2824f222.js";import{c as je,a as Ne}from"./yup-54691517.js";import"./index-02625b16.js";import{I as ve}from"./ImageCropModal-34566426.js";import{L as we}from"./index.esm-3a36c7d6.js";import{b as se}from"./index.esm-9c6194ba.js";import{M as te}from"./react-tooltip-7a26650a.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@hookform/resolvers-67648cca.js";import"./react-image-crop-1f5038af.js";const _e=new Y;function Ce(){const[h,_]=r.useState([{account_nickname:"",account_number:"",account_routing_number:"",account_type:"savings"}]);r.useState(!1),r.useState(null),r.useState({}),r.useState(!1),r.useState({account_nickname:"",account_number:"",account_routing_number:"",account_type:"savings"});const{dispatch:p}=D.useContext(X),[P,g]=r.useState(!0),T=D.useRef(null),u=n=>{console.log("Stripe connection status changed:",n)};r.useEffect(()=>{b()},[]);const b=async()=>{g(!0);try{const n=await _e.callRawAPI("/v3/api/custom/courtmatchup/coach/profile",{},"GET"),N=JSON.parse(n.account_details||"[]");N.length>0&&Array.isArray(N)&&_(N)}catch(n){k(p,n.message,3e3,"error")}finally{g(!1)}};return e.jsxs("div",{className:"mx-auto flex w-full flex-col bg-white px-4 pb-7",children:[P&&e.jsx(Q,{}),e.jsxs("div",{className:"mx-auto flex w-full max-w-2xl flex-col justify-center",children:[e.jsx(fe,{ref:T,onConnectionStatusChange:u,successMessage:"You can now pay staffs and coaches from your club.",noConnectionMessage:"Connect your Stripe account to pay your club staffs and coaches. This is required for processing payments on the platform."}),e.jsx("div",{className:"flex w-full flex-col self-center",children:e.jsx("p",{className:"mt-6 text-xs leading-4 text-neutral-400",children:"Note: Multiple accounts can be set up if you want to change accounts later on a particular month or year."})})]})]})}let J=new Y,Se=new oe;const ke=()=>{const h=je({email:Ne().email().required()}).required(),{dispatch:_}=D.useContext(ne),[p,P]=r.useState("");D.useState({});const[g,T]=r.useState("");r.useState(!1);const[u,b]=r.useState(!1),[n,N]=r.useState({}),[C,I]=r.useState(!0),[Z,A]=r.useState(null),[L,j]=r.useState(""),{dispatch:S}=D.useContext(X),[$,B]=r.useState(!1),[K,E]=r.useState(null),[O,G]=r.useState(!1),[M,t]=r.useState(null),{register:l,handleSubmit:x,setError:s,setValue:c,formState:{errors:y}}=ge({resolver:be(h)}),f=localStorage.getItem("user");async function m(){var i;I(!0);try{const o=await Se.getList("profile",{filter:[`user_id,eq,${f}`],join:["user|user_id"]}),a=(i=o==null?void 0:o.list)==null?void 0:i[0];if(a){const d=a.user||{},U=a.id,F={...a,...d,profile_id:U,user_id:d.id};N(F),c("email",d==null?void 0:d.email),c("first_name",d==null?void 0:d.first_name),c("last_name",d==null?void 0:d.last_name),c("phone",d==null?void 0:d.phone),c("bio",d==null?void 0:d.bio),P(d==null?void 0:d.email),T(d==null?void 0:d.photo),c("gender",a==null?void 0:a.gender),c("address",a==null?void 0:a.address),c("city",a==null?void 0:a.city),c("state",a==null?void 0:a.state),c("zip_code",a==null?void 0:a.zip_code),_({type:"UPDATE_PROFILE",payload:F}),I(!1)}}catch(o){ee(_,o.response.data.message?o.response.data.message:o.message)}}const v=["email","first_name","last_name","phone","bio","photo","alternative_phone","age_group","family_role","password","verify","status"],w=["gender","address","city","state","zip_code","date_of_birth","country","house_no"];console.log("User Profile Data:",{user_id:n==null?void 0:n.user_id,profile_id:n==null?void 0:n.profile_id,defaultValues:n});const z=async(i,o)=>{try{b(!0);const a={[i]:o},d=v.includes(i),U=w.includes(i);if(d){J.setTable("user");const F=await J.callRestAPI({id:n==null?void 0:n.user_id,...a},"PUT");F.error?W(F):(k(S,"Profile Updated",4e3),A(null),j(""),m())}else if(U){J.setTable("profile");const F=await J.callRestAPI({id:n==null?void 0:n.profile_id,...a},"PUT");F.error?W(F):(k(S,"Profile Updated",4e3),A(null),j(""),m())}else{k(S,"Unknown field type: "+i,4e3,"error"),b(!1);return}b(!1)}catch(a){b(!1),s(i,{type:"manual",message:a!=null&&a.message&&a==null?void 0:a.message}),ee(_,a!=null&&a.message&&a==null?void 0:a.message)}},W=i=>{if(i.validation){const o=Object.keys(i.validation);for(let a=0;a<o.length;a++){const d=o[a];s(d,{type:"manual",message:i.validation[d]})}}},H=i=>{try{if(i.size>2*1024*1024){k(S,"File size exceeds 2MB limit. Please choose a smaller file.",3e3,"error");return}t(i.type);const o=new FileReader;o.onload=()=>{E(o.result),G(!0)},o.readAsDataURL(i)}catch(o){k(S,o==null?void 0:o.message,3e3,"error"),console.log(o)}},q=async i=>{try{B(!0);const o=M==="image/png",a=new File([i],`cropped_profile.${o?"png":"jpg"}`,{type:o?"image/png":"image/jpeg"});let d=new FormData;d.append("file",a);let U=await J.uploadImage(d);z("photo",U==null?void 0:U.url)}catch(o){k(S,o==null?void 0:o.message,3e3,"error"),console.log(o)}finally{B(!1)}},V=()=>{z("photo",null),N({...n,photo:null})};return D.useEffect(()=>{m()},[]),e.jsxs("div",{className:"",children:[C||$&&e.jsx(Q,{}),e.jsx("div",{className:"",children:e.jsxs("div",{className:"",children:[e.jsx("div",{className:"mb-6",children:e.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Profile details"})}),e.jsx(ve,{isOpen:O,onClose:()=>G(!1),image:K,onCropComplete:q}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:g||"/default-avatar.png",alt:"Profile",className:"h-24 w-24 rounded-full object-cover"}),e.jsx("div",{children:e.jsxs("div",{className:"flex flex-col items-start justify-between gap-2",children:[e.jsx("p",{className:" font-medium text-gray-700",children:"Profile Picture"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:V,disabled:!g,className:"rounded-xl border border-red-600 px-3 py-1.5  text-red-600 disabled:opacity-50",children:"Remove"}),e.jsxs("label",{className:"cursor-pointer rounded-xl border border-gray-300 px-3 py-1.5  text-gray-700",children:["Change Photo",e.jsx("input",{type:"file",accept:"image/*",onChange:i=>H(i.target.files[0]),className:"hidden"})]})]}),e.jsx("p",{className:"text-xs text-gray-500",children:"Min 400x400px, PNG or JPEG"})]})})]}),e.jsx("div",{className:"space-y-4",children:[{key:"first_name",label:"First Name"},{key:"last_name",label:"Last Name"},{key:"gender",label:"Gender",type:"select",options:["male","female","other"]},{key:"email",label:"Email",note:"Your email is not shared with other users."},{key:"phone",label:"Phone"},{key:"address",label:"Address"},{key:"city",label:"City"},{key:"state",label:"State"},{key:"zip_code",label:"Zip Code"},{key:"bio",label:"Bio",type:"textarea"}].map(i=>e.jsx("div",{children:Z===i.key?e.jsxs("div",{children:[e.jsxs("label",{className:"flex justify-between",children:[e.jsx("span",{className:" font-medium text-gray-700",children:i.label}),e.jsx("button",{onClick:()=>A(null),className:" text-primaryBlue underline hover:text-primaryBlue",children:"Cancel"})]}),i.type==="select"?e.jsxs("select",{value:L,onChange:o=>j(o.target.value),className:"mt-1 w-full rounded-md border border-gray-300 p-2",children:[e.jsxs("option",{value:"",children:["Select ",i.label.toLowerCase()]}),i.options.map(o=>e.jsx("option",{value:o,children:o.charAt(0).toUpperCase()+o.slice(1)},o))]}):i.type==="textarea"?e.jsx("textarea",{value:L,onChange:o=>j(o.target.value),rows:4,className:"mt-1 w-full rounded-md border border-gray-300 p-2"}):e.jsx("input",{type:"text",value:L,onChange:o=>j(o.target.value),className:"mt-1  w-full rounded-xl border border-gray-300 p-2"}),i.note&&e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:i.note}),e.jsx("div",{className:"mt-2",children:e.jsx(ae,{loading:u,onClick:()=>z(i.key,L),className:"rounded-xl bg-primaryBlue px-4 py-2  font-medium text-white hover:bg-primaryBlue",children:"Save"})})]}):e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:" font-medium text-gray-500",children:i.label}),e.jsx("button",{onClick:()=>{A(i.key),j((n==null?void 0:n[i.key])||"")},className:" text-primaryBlue hover:text-indigo-800",children:"Edit"})]}),e.jsx("p",{className:"mt-1",children:(n==null?void 0:n[i.key])||"--"}),i.note&&e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:i.note})]})},i.key))})]})]})})]})};let Pe=new Y;const Te=()=>{const[h,_]=r.useState({}),[p,P]=r.useState(""),[g,T]=r.useState(""),[u,b]=r.useState(""),[n,N]=r.useState("date"),[C,I]=r.useState("desc"),[Z,A]=r.useState([]),[L,j]=r.useState(!1),[S,$]=r.useState(null),[B,K]=r.useState("all");ie();const E=s=>{_(c=>({...c,[s]:!c[s]}))},O=async()=>{j(!0);try{const s=await Pe.callRawAPI("/v3/api/custom/courtmatchup/coach/bookings/billing/coach-invoices",{},"GET");$(s),A(s.invoices||[])}catch(s){console.log(s)}finally{j(!1)}},M=[...Z.filter(s=>{var m,v,w,z,W;const c=((m=s.user_first_name)==null?void 0:m.toLowerCase().includes(p.toLowerCase()))||((v=s.user_last_name)==null?void 0:v.toLowerCase().includes(p.toLowerCase()))||((w=s.receipt_id)==null?void 0:w.toLowerCase().includes(p.toLowerCase()))||((z=s.status)==null?void 0:z.toLowerCase().includes(p.toLowerCase())),y=B==="all"||((W=s.invoice_type)==null?void 0:W.toLowerCase())===B.toLowerCase(),f=(()=>{if(!g&&!u)return!0;if(!s.date)return!1;const H=new Date(s.date),q=g?new Date(g):null,V=u?new Date(u):null;return q&&V?H>=q&&H<=V:q?H>=q:V?H<=V:!0})();return c&&y&&f})].sort((s,c)=>{let y=0;if(n==="date"){const f=new Date(s.date||s.create_at),m=new Date(c.date||c.create_at);y=f.getTime()-m.getTime()}else if(n==="amount")y=(s.amount||0)-(c.amount||0);else if(n==="status")y=(s.status||"").localeCompare(c.status||"");else if(n==="customer"){const f=`${s.user_first_name||""} ${s.user_last_name||""}`.trim(),m=`${c.user_first_name||""} ${c.user_last_name||""}`.trim();y=f.localeCompare(m)}return C==="desc"?-y:y}),t=()=>{n==="date"?(N("amount"),I("desc")):n==="amount"?(N("status"),I("asc")):n==="status"?(N("customer"),I("asc")):(N("date"),I(C==="desc"?"asc":"desc"))},l=(s,c="usd")=>new Intl.NumberFormat("en-US",{style:"currency",currency:c.toUpperCase()}).format(s),x=s=>s?new Date(s).toLocaleDateString("en-US",{month:"2-digit",day:"2-digit",year:"2-digit"}):"N/A";return r.useEffect(()=>{O()},[]),L?e.jsx("div",{className:"flex w-full items-center justify-center py-8",children:e.jsx("div",{className:"text-lg",children:"Loading invoices..."})}):e.jsxs("div",{className:"w-full",children:[e.jsx("div",{className:"mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between",children:e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-semibold",children:"My invoices"}),S&&e.jsxs("p",{className:"mt-1 text-sm text-gray-600",children:["Total: ",S.total_invoices||0," invoices • Total Earnings: ",l(S.total_earnings||0)]})]})}),e.jsxs("div",{className:"mb-6 flex flex-col gap-3 sm:flex-row",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx(ce,{className:"absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400"}),e.jsx("input",{type:"text",value:p,onChange:s=>P(s.target.value),placeholder:"Search invoices...",className:"w-full rounded-lg border border-gray-200 py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{className:"flex w-full gap-2 sm:w-auto",children:[e.jsx("input",{type:"date",value:g,onChange:s=>T(s.target.value),placeholder:"Start date",className:"w-full rounded-lg border border-gray-200 px-3 py-2 text-sm sm:w-auto"}),e.jsx("input",{type:"date",value:u,onChange:s=>b(s.target.value),placeholder:"End date",className:"w-full rounded-lg border border-gray-200 px-3 py-2 text-sm sm:w-auto"}),(g||u)&&e.jsx("button",{onClick:()=>{T(""),b("")},className:"rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-500 hover:bg-gray-50 hover:text-gray-700",title:"Clear dates",children:"✕"})]}),e.jsxs("button",{onClick:t,className:"flex w-full items-center justify-center gap-2 rounded-lg border border-gray-200 px-4 py-2 hover:bg-gray-50 sm:w-auto",children:["By ",n," ",C==="desc"?"↓":"↑",e.jsx(we,{className:"transform"})]})]}),e.jsx("div",{className:"space-y-4",children:M.length===0?e.jsx("div",{className:"py-8 text-center text-gray-500",children:"No invoices found."}):M.map(s=>{var c;return e.jsxs("div",{className:"rounded-lg border border-gray-100 bg-gray-50 p-3 shadow-sm",children:[e.jsxs("button",{onClick:()=>E(s.id),className:"flex w-full flex-col gap-2 sm:flex-row sm:items-center sm:justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(de,{className:`transform transition-transform ${h[s.id]?"":"-rotate-90"}`}),e.jsx("span",{className:"text-sm sm:text-base",children:s.invoice_type||s.type}),s.status&&e.jsx("span",{className:`rounded-full px-2 py-1 text-xs ${s.status==="completed"?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:s.status})]}),e.jsxs("div",{className:"flex items-center justify-between gap-4 pl-6 sm:pl-0",children:[e.jsx("span",{className:"text-sm text-gray-600",children:x(s.date)}),e.jsx("span",{className:"font-medium",children:l(s.amount,s.currency)})]})]}),h[s.id]&&e.jsxs("div",{className:"mt-4 space-y-3 border-t border-gray-200 p-4",children:[s.receipt_id&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Invoice Number"}),e.jsxs("span",{children:["#",s.receipt_id]})]}),s.user_first_name&&s.user_last_name&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Customer"}),e.jsxs("span",{children:[s.user_first_name," ",s.user_last_name]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Created on"}),e.jsx("span",{children:x(s.create_at)})]}),s.total_amount&&s.total_amount!==s.amount&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Total amount"}),e.jsx("span",{children:l(s.total_amount,s.currency)})]}),s.valid_until&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Period"}),e.jsxs("span",{children:[x(s.create_at)," -"," ",x(s.valid_until)]})]}),s.club_name&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Club"}),e.jsx("span",{children:s.club_name})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Payment method"}),e.jsx("span",{children:s.payment_method})]}),s.reservation_type&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Reservation type"}),e.jsx("span",{className:"capitalize",children:(c=pe.find(y=>y.value===s.reservation_type))==null?void 0:c.label})]}),e.jsx("button",{className:"mt-2 rounded-lg border border-gray-200 px-4 py-2 text-sm hover:bg-gray-50",onClick:()=>{const y=`
                        <div style="padding: 20px; font-family: Arial, sans-serif;">
                          <h2>Invoice Receipt</h2>
                          <p><strong>Receipt ID:</strong> ${s.receipt_id||"N/A"}</p>
                          <p><strong>Amount:</strong> ${l(s.amount,s.currency)}</p>
                          <p><strong>Date:</strong> ${x(s.date)}</p>
                          <p><strong>Status:</strong> ${s.status}</p>
                          <p><strong>Customer:</strong> ${s.user_first_name} ${s.user_last_name}</p>
                          <p><strong>Payment Method:</strong> ${s.payment_method}</p>
                          ${s.club_name?`<p><strong>Club:</strong> ${s.club_name}</p>`:""}
                        </div>
                      `,f=window.open("","_blank");f.document.write(y),f.document.close(),f.print()},children:"Print receipt"})]})]},s.id)})})]})},R=new Y,Ie=()=>{var O,G,M;const[h,_]=r.useState({sport_id:"",type:"",sub_type:"",price:""}),[p,P]=r.useState({}),[g,T]=r.useState(!1),[u,b]=r.useState(!1),[n,N]=r.useState(!0),{dispatch:C}=r.useContext(X),[I,Z]=r.useState([]),[A,L]=r.useState(null),j=(I==null?void 0:I.filter(t=>t.status===1))||[];r.useEffect(()=>{(async()=>{N(!0);try{const l=await R.callRawAPI("/v3/api/custom/courtmatchup/coach/profile",{},"GET");L(l);const x=await R.callRawAPI(`/v3/api/custom/courtmatchup/user/club/${l.club_id}`,{},"GET");Z(x.sports)}catch(l){console.error("Error fetching data:",l),k(C,l.message,3e3,"error")}finally{N(!1)}})()},[C]);const S=t=>{var y,f,m;const l={};t.sport_id||(l.sport_id="Sport is required");const x=j.find(v=>v.id===parseInt(t.sport_id)),s=(y=x==null?void 0:x.sport_types)==null?void 0:y.some(v=>v.type&&v.type.trim()!=="");s&&!t.type&&(l.type="Type is required");let c=!1;if(s&&t.type&&t.type!=="All"){const v=(f=x==null?void 0:x.sport_types)==null?void 0:f.find(w=>w.type===t.type);c=(m=v==null?void 0:v.subtype)==null?void 0:m.some(w=>w&&w.trim()!==""),c&&!t.sub_type&&(l.sub_type="Sub-type is required")}return t.price||(l.price="Price is required"),t.price&&Number(t.price)<=0&&(l.price="Price must be greater than 0"),t.type==="All"&&(delete l.type,t.sub_type==="All"&&delete l.sub_type),l},$=t=>{const{name:l,value:x}=t.target;_(s=>({...s,[l]:x})),P(s=>({...s,[l]:""}))},B=async()=>{var c,y,f;const t={...h},l=j.find(m=>m.id===parseInt(h.sport_id));if(!((c=l==null?void 0:l.sport_types)==null?void 0:c.some(m=>m.type&&m.type.trim()!=="")))t.type="",t.sub_type="";else if(t.type&&t.type!=="All"){const m=(y=l==null?void 0:l.sport_types)==null?void 0:y.find(w=>w.type===t.type);((f=m==null?void 0:m.subtype)==null?void 0:f.some(w=>w&&w.trim()!==""))||(t.sub_type="")}const s=S(t);if(Object.keys(s).length>0){P(s);return}b(!0);try{R.setTable("coach_sports"),await R.callRawAPI("/v3/api/custom/courtmatchup/coach/profile-edit",{sport_ids:[t]},"POST"),_({sport_id:"",type:"",sub_type:"",price:""});const m=await R.callRawAPI("/v3/api/custom/courtmatchup/coach/profile",{},"GET");L(m),k(C,"Sport added successfully",3e3,"success")}catch(m){console.log(m),k(C,m.message,3e3,"error")}finally{b(!1)}},K=async t=>{T(!0);try{R.setTable("coach_sports"),await R.callRestAPI({id:t},"DELETE");const l=await R.callRawAPI("/v3/api/custom/courtmatchup/coach/profile",{},"GET");L(l),k(C,"Sport deleted successfully",3e3,"success")}catch(l){console.log(l),k(C,l.message,3e3,"error")}finally{T(!1)}},E=j.find(t=>t.id===parseInt(h.sport_id));return n?e.jsx(Q,{}):e.jsxs("div",{className:"w-full",children:[g&&e.jsx(Q,{}),e.jsx("h1",{className:"mb-6 text-xl font-semibold text-gray-900",children:"Sports you offer"}),e.jsxs("div",{className:"space-y-6",children:[((O=A==null?void 0:A.sports)==null?void 0:O.length)>0?e.jsx("div",{className:"mt-6 rounded-xl border border-gray-200 p-4 shadow-sm",children:e.jsx("ul",{className:"divide-y divide-gray-200",children:A.sports.map((t,l)=>{var x;return e.jsxs("li",{className:"flex items-center justify-between py-3 transition-colors hover:bg-gray-50",children:[e.jsxs("div",{className:"grid grid-cols-4 gap-4",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Sport"}),e.jsx("span",{className:"font-medium",children:(x=j.find(s=>s.id===parseInt(t.sport_id)))==null?void 0:x.name})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Type"}),e.jsx("span",{className:"font-medium",children:t.type||"--"})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Sub-type"}),e.jsx("span",{className:"font-medium",children:t.sub_type||"--"})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Price"}),e.jsxs("span",{className:"font-medium",children:["$",t.price]})]})]}),e.jsx("button",{onClick:()=>K(t.id),className:"ml-4 text-red-500 hover:text-red-700",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.33333 15C8.55435 15 8.76631 14.9122 8.92259 14.7559C9.07887 14.5996 9.16667 14.3877 9.16667 14.1667V9.16667C9.16667 8.94565 9.07887 8.73369 8.92259 8.57741C8.76631 8.42113 8.55435 8.33333 8.33333 8.33333C8.11232 8.33333 7.90036 8.42113 7.74408 8.57741C7.5878 8.73369 7.5 8.94565 7.5 9.16667V14.1667C7.5 14.3877 7.5878 14.5996 7.74408 14.7559C7.90036 14.9122 8.11232 15 8.33333 15ZM16.6667 4.16667H13.3333V3.33333C13.3333 2.8913 13.1577 2.46738 12.8452 2.15482C12.5326 1.84226 12.1087 1.66667 11.6667 1.66667H8.33333C7.8913 1.66667 7.46738 1.84226 7.15482 2.15482C6.84226 2.46738 6.66667 2.8913 6.66667 3.33333V4.16667H3.33333C3.11232 4.16667 2.90036 4.25446 2.74408 4.41074C2.5878 4.56702 2.5 4.77899 2.5 5C2.5 5.22101 2.5878 5.43298 2.74408 5.58926C2.90036 5.74554 3.11232 5.83333 3.33333 5.83333H4.16667V16.6667C4.16667 17.1087 4.34226 17.5326 4.65482 17.8452C4.96738 18.1577 5.3913 18.3333 5.83333 18.3333H14.1667C14.6087 18.3333 15.0326 18.1577 15.3452 17.8452C15.6577 17.5326 15.8333 17.1087 15.8333 16.6667V5.83333H16.6667C16.8877 5.83333 17.0996 5.74554 17.2559 5.58926C17.4122 5.43298 17.5 5.22101 17.5 5C17.5 4.77899 17.4122 4.56702 17.2559 4.41074C17.0996 4.25446 16.8877 4.16667 16.6667 4.16667ZM8.33333 3.33333H11.6667V4.16667H8.33333V3.33333ZM14.1667 16.6667H5.83333V5.83333H14.1667V16.6667ZM11.6667 15C11.8877 15 12.0996 14.9122 12.2559 14.7559C12.4122 14.5996 12.5 14.3877 12.5 14.1667V9.16667C12.5 8.94565 12.4122 8.73369 12.2559 8.57741C12.0996 8.42113 11.8877 8.33333 11.6667 8.33333C11.4457 8.33333 11.2337 8.42113 11.0774 8.57741C10.9211 8.73369 10.8333 8.94565 10.8333 9.16667V14.1667C10.8333 14.3877 10.9211 14.5996 11.0774 14.7559C11.2337 14.9122 11.4457 15 11.6667 15Z",fill:"#868C98"})})})]},l)})})}):e.jsx("div",{className:"mt-6 rounded-xl border border-gray-200 p-4 text-center shadow-sm",children:e.jsx("p",{className:"text-gray-500",children:"No sports added yet. Add your first sport below."})}),e.jsxs("div",{className:"rounded-xl border border-gray-200 p-4 shadow-sm",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold",children:"Add New Sport"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsxs("select",{name:"sport_id",value:h.sport_id,onChange:$,className:`w-full rounded-lg border ${p.sport_id?"border-red-500":"border-gray-300"} p-2`,children:[e.jsx("option",{value:"",children:"Select Sport"}),j.map(t=>e.jsx("option",{value:t.id,children:t.name},t.id))]}),p.sport_id&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:p.sport_id})]}),E&&e.jsxs(e.Fragment,{children:[E.sport_types.some(t=>t.type&&t.type.trim()!=="")?e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("label",{className:"text-sm text-gray-700",children:"Type"}),e.jsx(se,{className:"cursor-help text-gray-400 hover:text-gray-600","data-tooltip-id":"sport-type-tooltip",size:16}),e.jsx(te,{id:"sport-type-tooltip",className:"z-50 max-w-xs rounded-xl bg-white p-2 text-sm text-black",style:{backgroundColor:"#fff",border:"1px solid #e4e4e7",color:"#000",borderRadius:"12px",padding:"12px",maxWidth:"300px"},children:"Unless you intend to charge different rates for different sport types or subtypes, select 'All' for both. If you play a sport across multiple types/subtypes, ensure all are added individually to appear in relevant searches."})]}),e.jsxs("select",{name:"type",value:h.type,onChange:$,className:`w-full rounded-lg border ${p.type?"border-red-500":"border-gray-300"} p-2`,children:[e.jsx("option",{value:"",children:"Select Type"}),E.sport_types.length>=2&&e.jsx("option",{value:"All",children:"All"}),E.sport_types.map((t,l)=>t.type&&t.type.trim()!==""&&e.jsx("option",{value:t.type,children:t.type},l))]}),p.type&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:p.type})]}):null,h.type&&E.sport_types.some(t=>t.type===h.type&&t.subtype&&t.subtype.length>0)&&e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("label",{className:"text-sm text-gray-700",children:"Sub-type"}),e.jsx(se,{className:"cursor-help text-gray-400 hover:text-gray-600","data-tooltip-id":"sport-subtype-tooltip",size:16}),e.jsx(te,{id:"sport-subtype-tooltip",className:"z-50 max-w-xs rounded-lg bg-white p-2 text-sm text-black",style:{backgroundColor:"#fff",border:"1px solid #e4e4e7",color:"#000",borderRadius:"12px",padding:"12px",maxWidth:"300px"},children:"Unless you intend to charge different rates for different sport types or subtypes, select 'All' for both. If you play a sport across multiple types/subtypes, ensure all are added individually to appear in relevant searches."})]}),e.jsxs("select",{name:"sub_type",value:h.sub_type,onChange:$,className:`w-full rounded-lg border ${p.sub_type?"border-red-500":"border-gray-300"} p-2`,children:[e.jsx("option",{value:"",children:"Select Sub-type"}),h.type==="All"?e.jsx("option",{value:"All",children:"All"}):e.jsxs(e.Fragment,{children:[((G=E.sport_types.find(t=>t.type===h.type))==null?void 0:G.subtype.length)>=2&&e.jsx("option",{value:"All",children:"All"}),(M=E.sport_types.find(t=>t.type===h.type))==null?void 0:M.subtype.filter(t=>t&&t.trim()!=="").map((t,l)=>e.jsx("option",{value:t,children:t},l))]})]}),p.sub_type&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:p.sub_type})]}),e.jsxs("div",{children:[e.jsx("input",{type:"number",name:"price",value:h.price,onChange:$,placeholder:"Enter price",className:`w-full rounded-lg border ${p.price?"border-red-500":"border-gray-300"} p-2`}),p.price&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:p.price})]}),e.jsx(ae,{type:"button",onClick:B,loading:u,className:"w-full rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80 disabled:opacity-50",children:u?"Adding Sport...":"Add Sport"})]})]})]})]})]})},Ns=()=>{const{dispatch:h}=D.useContext(X),[_]=re(),[p,P]=r.useState("profile"),g=le();r.useEffect(()=>{const u=_.get("tab");u&&P({"payment-methods":"payment-methods",profile:"profile",sports:"sports",membership:"membership",billing:"billing",invoices:"invoices"}[u]||"profile")},[_.get("tab")]);const T=[{label:"Profile details",value:"profile",icon:me},{label:"Sports",value:"sports",icon:ue},{label:"Bank Accounts",value:"payment-methods",icon:xe},{label:"Invoices",value:"invoices",icon:he}];return r.useEffect(()=>{h({type:"SETPATH",payload:{path:"profile"}})},[]),e.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:e.jsxs("div",{className:"mx-auto  max-w-6xl px-4",children:[e.jsx(ye,{onBack:()=>g("/coach/dashboard")}),e.jsxs("div",{className:"flex flex-col gap-8 md:flex-row",children:[e.jsx("div",{className:"max-h-fit w-full rounded-xl bg-white shadow-sm md:max-w-xs",children:e.jsx("nav",{className:"no-scrollbar flex w-full flex-row overflow-x-auto p-4 md:flex-col",children:T.map(u=>{const b=u.icon;return e.jsxs("button",{onClick:()=>{P(u.value),g(`/coach/profile?tab=${u.value}`)},className:`mr-2 flex min-w-fit items-center whitespace-nowrap rounded-lg px-4 py-3 transition-colors md:mb-2 md:mr-0 ${p===u.value?"bg-gray-100 text-gray-900":"text-gray-600 hover:bg-gray-50"}`,children:[e.jsx(b,{className:"mr-2 h-5 w-5 text-gray-500"}),e.jsx("span",{className:"text-sm",children:u.label})]},u.value)})})}),e.jsxs("div",{className:" w-full rounded-xl bg-white p-6 shadow-sm",children:[p==="profile"&&e.jsx(ke,{}),p==="sports"&&e.jsx(Ie,{}),p==="payment-methods"&&e.jsx(Ce,{}),p==="invoices"&&e.jsx(Te,{})]})]})]})})};export{Ns as default};

import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as $,r,L as xe,k as ve,f as we}from"./vendor-851db8c1.js";import{u as re}from"./react-hook-form-687afde5.js";import{o as le}from"./yup-2824f222.js";import{c as ne,a as ie,e as he}from"./yup-54691517.js";import{M as Y,T as fe,A as ge,G as H,e as Q,d as oe,t as R,b as x,n as Ne,o as Se,R as Ce,p as Pe,u as ke,q as _e,r as Ie,v as Ee,f as Ae,h as Te,x as Fe,F as Me,y as $e}from"./index-f915b394.js";import"./index-02625b16.js";import{I as De}from"./ImageCropModal-34566426.js";import{F as Le,a as Be}from"./index.esm-51ae62c8.js";import{S as ze}from"./index.esm-92169588.js";import{b as Ue}from"./index.esm-c561e951.js";import{u as be,a as Re,C as ue}from"./@stripe/react-stripe-js-64f0e61f.js";import{L as Oe}from"./index.esm-3a36c7d6.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import{B as Ze}from"./BackButton-11ba52b2.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-image-crop-1f5038af.js";let V=new Y,Ge=new fe;const qe=()=>{const f=ne({email:ie().email().required()}).required(),{dispatch:j}=$.useContext(ge),[v,g]=r.useState("");$.useState({});const[N,P]=r.useState("");r.useState(!1);const[m,u]=r.useState(!1),[d,C]=r.useState({}),[U,S]=r.useState(!0),[q,k]=r.useState(null),[E,p]=r.useState(""),{dispatch:b}=$.useContext(H),[y,A]=r.useState(!1),[h,D]=r.useState(null),[L,T]=r.useState(!1),[_,w]=r.useState(null),{register:B,handleSubmit:s,setError:l,setValue:o,formState:{errors:n}}=re({resolver:le(f)}),O=localStorage.getItem("user");async function z(){var i;S(!0);try{const t=await Ge.getList("profile",{filter:[`user_id,eq,${O}`],join:["user|user_id"]}),a=(i=t==null?void 0:t.list)==null?void 0:i[0];if(a){const c=a.user||{},I=a.id,M={...a,...c,profile_id:I,user_id:c.id};C(M),o("email",c==null?void 0:c.email),o("first_name",c==null?void 0:c.first_name),o("last_name",c==null?void 0:c.last_name),o("phone",c==null?void 0:c.phone),o("bio",c==null?void 0:c.bio),g(c==null?void 0:c.email),P(c==null?void 0:c.photo),o("gender",a==null?void 0:a.gender),o("address",a==null?void 0:a.address),o("city",a==null?void 0:a.city),o("state",a==null?void 0:a.state),o("zip_code",a==null?void 0:a.zip_code),o("ntrp",a==null?void 0:a.ntrp),j({type:"UPDATE_PROFILE",payload:M}),S(!1)}}catch(t){R(j,t.response.data.message?t.response.data.message:t.message)}}const Z=["email","first_name","last_name","phone","bio","photo","alternative_phone","age_group","family_role","password","verify","status"],W=["gender","address","city","state","zip_code","ntrp","date_of_birth","country","house_no"];console.log("User Profile Data:",{user_id:d==null?void 0:d.user_id,profile_id:d==null?void 0:d.profile_id,defaultValues:d});const G=async(i,t)=>{try{u(!0);const a={[i]:t},c=Z.includes(i),I=W.includes(i);if(c){V.setTable("user");const M=await V.callRestAPI({id:d==null?void 0:d.user_id,...a},"PUT");M.error?F(M):(x(b,"Profile Updated",4e3),k(null),p(""),z())}else if(I){V.setTable("profile");const M=await V.callRestAPI({id:d==null?void 0:d.profile_id,...a},"PUT");M.error?F(M):(x(b,"Profile Updated",4e3),k(null),p(""),z())}else{x(b,"Unknown field type: "+i,4e3,"error"),u(!1);return}u(!1)}catch(a){u(!1),l(i,{type:"manual",message:a!=null&&a.message&&a==null?void 0:a.message}),R(j,a!=null&&a.message&&a==null?void 0:a.message)}},F=i=>{if(i.validation){const t=Object.keys(i.validation);for(let a=0;a<t.length;a++){const c=t[a];l(c,{type:"manual",message:i.validation[c]})}}},J=i=>{try{if(i.size>2*1024*1024){x(b,"File size exceeds 2MB limit. Please choose a smaller file.",3e3,"error");return}w(i.type);const t=new FileReader;t.onload=()=>{D(t.result),T(!0)},t.readAsDataURL(i)}catch(t){x(b,t==null?void 0:t.message,3e3,"error"),console.log(t)}},X=async i=>{try{A(!0);const t=_==="image/png",a=new File([i],`cropped_profile.${t?"png":"jpg"}`,{type:t?"image/png":"image/jpeg"});let c=new FormData;c.append("file",a);let I=await V.uploadImage(c);G("photo",I==null?void 0:I.url)}catch(t){x(b,t==null?void 0:t.message,3e3,"error"),console.log(t)}finally{A(!1)}},ee=()=>{G("photo",null),C({...d,photo:null})};return $.useEffect(()=>{z()},[]),e.jsxs("div",{className:"",children:[U||y&&e.jsx(Q,{}),e.jsx("div",{className:"",children:e.jsxs("div",{className:"",children:[e.jsx("div",{className:"mb-6",children:e.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Profile details"})}),e.jsx(De,{isOpen:L,onClose:()=>T(!1),image:h,onCropComplete:X}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:N||"/default-avatar.png",alt:"Profile",className:"h-24 w-24 rounded-full object-cover"}),e.jsx("div",{children:e.jsxs("div",{className:"flex flex-col items-start justify-between gap-2",children:[e.jsx("p",{className:" font-medium text-gray-700",children:"Profile Picture"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:ee,disabled:!N,className:"rounded-xl border border-red-600 px-3 py-1.5  text-red-600 disabled:opacity-50",children:"Remove"}),e.jsxs("label",{className:"cursor-pointer rounded-xl border border-gray-300 px-3 py-1.5  text-gray-700",children:["Change Photo",e.jsx("input",{type:"file",accept:"image/*",onChange:i=>J(i.target.files[0]),className:"hidden"})]})]}),e.jsx("p",{className:"text-xs text-gray-500",children:"Min 400x400px, PNG or JPEG"})]})})]}),e.jsx("div",{className:"space-y-4",children:[{key:"first_name",label:"First Name"},{key:"last_name",label:"Last Name"},{key:"gender",label:"Gender",type:"select",options:["male","female","other"]},{key:"email",label:"Email",note:"Your email is not shared with other users."},{key:"phone",label:"Phone"},{key:"address",label:"Address"},{key:"city",label:"City"},{key:"state",label:"State"},{key:"zip_code",label:"Zip Code"},{key:"ntrp",label:"NTRP"},{key:"bio",label:"Bio",type:"textarea"}].map(i=>e.jsx("div",{children:q===i.key?e.jsxs("div",{children:[e.jsxs("label",{className:"flex justify-between",children:[e.jsx("span",{className:" font-medium text-gray-700",children:i.label}),e.jsx("button",{onClick:()=>k(null),className:" text-primaryBlue underline hover:text-primaryBlue",children:"Cancel"})]}),i.type==="select"?e.jsxs("select",{value:E,onChange:t=>p(t.target.value),className:"mt-1 w-full rounded-md border border-gray-300 p-2",children:[e.jsxs("option",{value:"",children:["Select ",i.label.toLowerCase()]}),i.options.map(t=>e.jsx("option",{value:t,children:t.charAt(0).toUpperCase()+t.slice(1)},t))]}):i.type==="textarea"?e.jsx("textarea",{value:E,onChange:t=>p(t.target.value),rows:4,className:"mt-1 w-full rounded-md border border-gray-300 p-2"}):e.jsx("input",{type:"text",value:E,onChange:t=>p(t.target.value),className:"mt-1  w-full rounded-xl border border-gray-300 p-2"}),i.note&&e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:i.note}),e.jsx("div",{className:"mt-2",children:e.jsx(oe,{loading:m,onClick:()=>G(i.key,E),className:"rounded-xl bg-primaryBlue px-4 py-2  font-medium text-white hover:bg-primaryBlue",children:"Save"})})]}):e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:" font-medium text-gray-500",children:i.label}),e.jsx("button",{onClick:()=>{k(i.key),p((d==null?void 0:d[i.key])||"")},className:" text-primaryBlue hover:text-indigo-800",children:"Edit"})]}),e.jsx("p",{className:"mt-1",children:(d==null?void 0:d[i.key])||"--"}),i.note&&e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:i.note})]})},i.key))})]})]})})]})};let He=new Y;function Ve({getData:f,onClose:j}){const[v,g]=r.useState(!1),N=be(),P=Re(),{dispatch:m}=r.useContext(H),u=ne({user:he(),token:ie()}),{register:d,setValue:C,handleSubmit:U,setError:S,formState:{errors:q}}=re({resolver:le(u)}),k=async E=>{g(!0),N.createToken(P.getElement(ue)).then(async p=>{if(console.log(p),p.error){x(m,p.error||"Something went wrong");return}const b={sourceToken:p.token.id};try{const y=await He.createCustomerStripeCard(b);if(!y.error)x(m,"Card added successfully");else if(y.validation){const A=Object.keys(y.validation);for(let h=0;h<A.length;h++){const D=A[h];x(m,y.validation[D],3e3)}}f(),j()}catch(y){console.error(y),x(m,y.message,5e3),R(m,y.code)}finally{g(!1)}})};return e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"mt-5",children:e.jsxs("form",{className:"",onSubmit:U(k),children:[e.jsx(ue,{className:"mb-3 rounded p-4 shadow-inner",options:{hidePostalCode:!0,style:{base:{backgroundColor:"",fontSize:"14px",lineHeight:"20px"}}}}),e.jsx(oe,{loading:v,type:"submit",className:"inline-block rounded-lg bg-primaryBlue px-3 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-blue-700 hover:shadow-lg focus:bg-blue-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-blue-800 active:shadow-lg",children:v?"Adding...":"Add card"})]})})})}let te=new Y;function Ye(){var B;const[f,j]=r.useState("bank_cards"),[v,g]=r.useState(!1),[N,P]=r.useState(!1),[m,u]=r.useState(""),[d,C]=r.useState(10),[U,S]=r.useState(!1),[q,k]=r.useState(!1),[E,p]=r.useState(!1),[b,y]=$.useState({});be();const A=ne({user:he(),token:ie()}),{dispatch:h}=$.useContext(H);re({resolver:le(A)});const D=[{id:"bank_cards",label:"Bank cards",icon:e.jsx(Pe,{})}],L=async s=>{P(!0);try{console.log("Saving card:",s),await new Promise(l=>setTimeout(l,1e3)),g(!1)}catch(l){console.error("Error saving card:",l)}finally{P(!1)}};async function T(s){var l,o;try{p(!0);const{data:n,limit:O,error:z,message:Z}=await te.getCustomerStripeCards(s);if(console.log(n),z&&x(h,Z,5e3),!n)return;m||u(((l=n==null?void 0:n.data[0])==null?void 0:l.id)??""),y(n),C(+O),S(m&&m!==((o=n.data[0])==null?void 0:o.id)),k(n.has_more)}catch(n){console.error("ERROR",n),x(h,n.message,5e3),R(dispatch,n.code)}finally{p(!1)}}const _=async s=>{p(!0);const{error:l,message:o}=await te.setStripeCustomerDefaultCard(s);if(x(h,o),l){console.error(l);return}T({}),p(!1)},w=async s=>{p(!0);const{isDeleted:l,error:o,message:n}=await te.deleteCustomerStripeCard(s);if(x(h,n),o){console.error(o);return}T({}),p(!1)};return $.useEffect(()=>{T({})},[]),e.jsxs("div",{className:"mx-auto max-w-2xl p-4 sm:p-6",children:[e.jsx("h2",{className:"mb-6 text-2xl font-semibold",children:"Payment methods"}),E&&e.jsx(Q,{}),e.jsx("div",{className:"mb-6 flex flex-wrap gap-4 border-b sm:flex-nowrap sm:gap-0 sm:space-x-4",children:D.map(s=>e.jsxs("button",{onClick:()=>j(s.id),className:`flex items-center space-x-2 px-1 pb-2 ${f===s.id?"border-b-2 border-primaryBlue text-primaryBlue":"text-gray-500 hover:text-gray-700"}`,children:[e.jsx("span",{className:"text-lg",children:s.icon}),e.jsx("span",{children:s.label})]},s.id))}),f==="bank_cards"&&e.jsxs("div",{className:"space-y-4",children:[(B=b==null?void 0:b.data)==null?void 0:B.map(s=>e.jsxs("div",{className:"flex flex-col justify-between gap-3 rounded-xl border p-4",children:[e.jsxs("div",{className:"flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between",children:[e.jsx("div",{className:"text-sm text-gray-700 sm:text-base",children:s.customer.email}),e.jsx("div",{className:"flex items-center justify-end space-x-4",children:e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("button",{onClick:()=>_(s.id),className:`text-sm sm:text-base ${s.id===s.customer.default_source?"text-green-600":"text-blue-600"} underline`,children:s.id===s.customer.default_source?"Default":"Set Default"}),e.jsx("button",{onClick:()=>w(s.id),className:"text-sm text-gray-500 underline sm:text-base",children:"Delete"})]})})]}),e.jsxs("div",{className:"flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("div",{className:"flex space-x-2",children:[s.brand==="Visa"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#1A1F71] text-white",children:e.jsx(Ue,{size:18})}),s.brand==="Mastercard"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#EB001B] text-white",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"2.11676in",height:"1.5in",viewBox:"0 0 152.407 108",children:e.jsxs("g",{children:[e.jsx("rect",{width:"152.407",height:"108",style:"fill: none"}),e.jsxs("g",{children:[e.jsx("rect",{x:"60.4117",y:"25.6968",width:"31.5",height:"56.6064",style:"fill: #ff5f00"}),e.jsx("path",{d:"M382.20839,306a35.9375,35.9375,0,0,1,13.7499-28.3032,36,36,0,1,0,0,56.6064A35.938,35.938,0,0,1,382.20839,306Z",transform:"translate(-319.79649 -252)",style:"fill: #eb001b"}),e.jsx("path",{d:"M454.20349,306a35.99867,35.99867,0,0,1-58.2452,28.3032,36.00518,36.00518,0,0,0,0-56.6064A35.99867,35.99867,0,0,1,454.20349,306Z",transform:"translate(-319.79649 -252)",style:"fill: #f79e1b"}),e.jsx("path",{d:"M450.76889,328.3077v-1.1589h.4673v-.2361h-1.1901v.2361h.4675v1.1589Zm2.3105,0v-1.3973h-.3648l-.41959.9611-.41971-.9611h-.365v1.3973h.2576v-1.054l.3935.9087h.2671l.39351-.911v1.0563Z",transform:"translate(-319.79649 -252)",style:"fill: #f79e1b"})]})]})})}),s.brand==="MasterCard"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded  text-white",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"2.11676in",height:"1.5in",viewBox:"0 0 152.407 108",children:e.jsxs("g",{children:[e.jsx("rect",{width:"152.407",height:"108",style:{fill:"none"}}),e.jsxs("g",{children:[e.jsx("rect",{x:"60.4117",y:"25.6968",width:"31.5",height:"56.6064",style:{fill:"#ff5f00"}}),e.jsx("path",{d:"M382.20839,306a35.9375,35.9375,0,0,1,13.7499-28.3032,36,36,0,1,0,0,56.6064A35.938,35.938,0,0,1,382.20839,306Z",transform:"translate(-319.79649 -252)",style:{fill:"#eb001b"}}),e.jsx("path",{d:"M454.20349,306a35.99867,35.99867,0,0,1-58.2452,28.3032,36.00518,36.00518,0,0,0,0-56.6064A35.99867,35.99867,0,0,1,454.20349,306Z",transform:"translate(-319.79649 -252)",style:{fill:"#f79e1b"}}),e.jsx("path",{d:"M450.76889,328.3077v-1.1589h.4673v-.2361h-1.1901v.2361h.4675v1.1589Zm2.3105,0v-1.3973h-.3648l-.41959.9611-.41971-.9611h-.365v1.3973h.2576v-1.054l.3935.9087h.2671l.39351-.911v1.0563Z",transform:"translate(-319.79649 -252)",style:{fill:"#f79e1b"}})]})]})})}),s.brand==="American Express"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#006FCF] text-white",children:e.jsx(ze,{size:18})}),s.brand==="Discover"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#FF6000] text-sm font-bold text-white",children:"DISC"}),s.brand==="Diners Club"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#0069AA] text-white",children:e.jsx(Ne,{size:18})}),s.brand==="JCB"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#0B4EA2] text-white",children:e.jsx(Se,{size:18})}),s.brand==="UnionPay"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#00447C] text-sm font-bold text-white",children:"UP"})]}),e.jsxs("p",{className:"text-sm text-black sm:text-base",children:[s.brand," • ",s.last4]})]}),e.jsx("div",{children:e.jsxs("p",{className:"text-sm text-gray-500",children:["Exp. ",s.exp_month,"/",s.exp_year.toString().slice(-2)]})})]})]},s.id)),e.jsxs("button",{onClick:()=>g(!0),className:"flex w-full items-center justify-center space-x-2 rounded-lg border border-blue-600 px-4 py-2 text-blue-600 hover:bg-blue-50 sm:w-auto",children:[e.jsx("span",{children:"+"}),e.jsx("span",{children:"Add card"})]})]}),e.jsx(Ce,{isOpen:v,onClose:()=>g(!1),title:"Add card",primaryButtonText:"Add card",onPrimaryAction:L,submitting:N,showFooter:!1,children:e.jsx(Ve,{onSubmit:L,getData:T,onClose:()=>g(!1)})})]})}let ae=new Y,Je=new fe;const K=f=>new Date(f*1e3).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});function Ke(){const[f,j]=r.useState({}),[v,g]=r.useState([]),[N,P]=r.useState(10),[m,u]=r.useState(1),[d,C]=r.useState(1),[U,S]=r.useState(0),[q,k]=r.useState(!1),[E,p]=r.useState(!1),[b,y]=r.useState(!1),[A,h]=r.useState(!1),[D,L]=r.useState([]),[T,_]=r.useState(!1),[w,B]=r.useState({}),[s,l]=r.useState({}),[o,n]=r.useState(null),{club:O}=ke();console.log("club",O);const{dispatch:z}=r.useContext(H),{dispatch:Z,state:W}=$.useContext(ge);async function G(t,a,c={}){y(!0);try{console.log(W);const I=o||parseInt(localStorage.getItem("user"));c.user_id=I;const M=await ae.getCustomerStripeSubscriptions({page:t,limit:a},c),{list:ce,total:ye,limit:je,num_pages:de,page:se}=M,me={};ce.forEach(pe=>{pe.status==="active"&&(me[pe.subId]=!0)}),j(me),g(ce),P(+je),u(+de),C(+se),S(+ye),k(+se>1),p(+se+1<=+de)}catch(I){console.error(I),R(Z,I.code)}finally{y(!1)}}const F=parseInt(localStorage.getItem("user"));async function J(){try{const t=o||F,a=await ae.getCustomerStripeSubscription(t);l(a.customer)}catch(t){console.error(t),R(Z,t.code)}}const X=async t=>{_(!0);try{const a=await ae.cancelStripeSubscription(t);if(a.error){console.error(a.message),x(z,a.message,7500,"error");return}x(z,a.message,1e4,"success"),G(1,N),h(!1),B({})}catch(a){console.error(a),x(z,a.message,7500,"error"),R(Z,a.code)}finally{_(!1)}},ee=async()=>{try{const t=await Je.getList("user",{filter:[`guardian,eq,${F}`,"role,cs,user"]});L(t.list)}catch(t){console.error("Error fetching family members:",t)}};r.useEffect(()=>{G(1,N),ee()},[F]),r.useEffect(()=>{J()},[F]),r.useEffect(()=>{o!==null&&(G(1,N),J())},[o]);const i=t=>{n(t)};return console.log("currentTableData",v),e.jsxs("div",{className:"mx-auto max-w-3xl p-3 sm:p-6",children:[b&&e.jsx(Q,{}),e.jsxs("div",{className:"mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between",children:[e.jsx("h1",{className:"text-2xl font-semibold",children:"Membership"}),e.jsx(xe,{to:"/user/membership/buy",className:"w-full rounded-lg bg-blue-600 px-4 py-2 text-center text-white transition-colors hover:bg-blue-700 sm:w-auto",children:"Buy new plan"})]}),e.jsx("div",{className:"mb-4 border-b border-gray-200"}),e.jsx("div",{className:"relative mb-4 flex justify-end",children:e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"relative",children:e.jsxs("select",{value:o||F,onChange:t=>{const a=parseInt(t.target.value);a===F?n(null):i(a)},className:"w-full appearance-none rounded-lg border border-gray-300 bg-white px-3 py-2 pr-8 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",children:[e.jsx("option",{value:F,children:"Myself"}),D.map(t=>e.jsxs("option",{value:t.id,children:[t.first_name," ",t.last_name," (",t.family_role||"Family Member",")"]},t.id))]})}),e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"Select the user whose membership you want to manage"})]})}),!b&&v.length===0&&e.jsxs("div",{className:"flex flex-col items-center justify-center rounded-xl bg-gray-50 px-4 py-12 text-center",children:[e.jsx("div",{className:"mb-4 rounded-full bg-blue-100 p-3",children:e.jsx("svg",{className:"h-6 w-6 text-blue-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),e.jsx("h3",{className:"mb-2 text-lg font-medium text-gray-900",children:"No Active Memberships"}),e.jsx("p",{className:"mb-6 text-sm text-gray-600",children:"You currently don't have any active membership plans."}),e.jsx(xe,{to:"/user/membership/buy",className:"rounded-lg bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700",children:"Browse Plans"})]}),v.map(t=>e.jsxs("div",{className:"mb-4 rounded-xl bg-gray-50",children:[e.jsxs("button",{onClick:()=>{j(a=>({...a,[t.subId]:!a[t.subId]}))},className:"flex w-full cursor-pointer flex-col gap-2 p-4 hover:bg-gray-100 sm:flex-row sm:items-center sm:justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[f[t.subId]?e.jsx(_e,{size:20}):e.jsx(Ie,{size:20}),e.jsxs("span",{className:"text-sm font-medium sm:text-base",children:[K(t.currentPeriodStart)," -"," ",K(t.currentPeriodEnd)]})]}),e.jsxs("div",{className:"flex items-center justify-between gap-3 pl-7 sm:pl-0",children:[e.jsx("span",{className:"text-sm text-gray-600 sm:text-base",children:t.planName}),e.jsx("span",{className:`rounded-full px-3 py-1 text-sm capitalize ${t.status==="active"?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:t.status})]})]}),f[t.subId]&&e.jsx("div",{className:"p-4",children:e.jsxs("div",{className:"space-y-6 rounded-xl border bg-white p-4",children:[e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row sm:items-center",children:[e.jsx("span",{className:"text-gray-600",children:"Plan price"}),e.jsxs("div",{className:"text-left sm:text-right",children:[e.jsx("div",{className:"font-semibold",children:Ee(t.planAmount)}),e.jsx("div",{className:"text-sm text-gray-500",children:"Billed annually"})]})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Purchased on"}),e.jsx("span",{className:"text-sm sm:text-base",children:K(t.createdAt)})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Valid until"}),e.jsx("span",{className:"text-sm sm:text-base",children:K(t.currentPeriodEnd)})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Subscription ID"}),e.jsx("span",{className:"break-all text-sm sm:text-base",children:t.subId})]}),t.status==="active"&&e.jsx("div",{className:"flex justify-center sm:justify-start",children:e.jsx("button",{onClick:()=>{h(!0),B(t)},className:"w-full rounded-xl bg-red-500 px-5 py-2 text-white transition-colors hover:bg-red-600 sm:w-auto",children:"Cancel plan"})})]})})]},t.subId)),e.jsxs("div",{className:`fixed inset-0 z-50 flex items-center justify-center p-4 ${A?"":"hidden"}`,children:[e.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),e.jsxs("div",{className:"relative z-50 w-full max-w-lg rounded-3xl bg-white p-4 sm:p-6",children:[e.jsxs("div",{className:"flex flex-col gap-4 sm:flex-row",children:[e.jsx("div",{children:e.jsxs("svg",{width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{width:"40",height:"40",rx:"10",fill:"#FDEDF0"}),e.jsx("path",{d:"M20 29C15.0293 29 11 24.9707 11 20C11 15.0293 15.0293 11 20 11C24.9707 11 29 15.0293 29 20C29 24.9707 24.9707 29 20 29ZM19.1 22.7V24.5H20.9V22.7H19.1ZM19.1 15.5V20.9H20.9V15.5H19.1Z",fill:"#DF1C41"})]})}),e.jsxs("div",{children:[e.jsx("h2",{className:"mb-4 text-xl font-medium",children:"Are you sure?"}),e.jsx("p",{className:"mb-6 text-gray-600",children:"Are you sure you want to cancel membership?"})]})]}),e.jsxs("div",{className:"flex flex-col-reverse gap-3 border-t pt-4 sm:flex-row sm:justify-end",children:[e.jsx("button",{onClick:()=>{h(!1),B({})},className:"w-full rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50 sm:w-auto",children:"Go back"}),e.jsx(oe,{onClick:()=>X(w.subId),className:"w-full rounded-lg bg-red-600 px-4 py-2 text-white hover:bg-red-700 sm:w-auto",loading:T,children:"Yes, cancel"})]})]})]})]})}let Qe=new Y;function We(){const{dispatch:f}=$.useContext(H),[j,v]=r.useState({}),[g,N]=r.useState([]);r.useState(0),r.useState(!1),r.useState(!1);const[P,m]=r.useState(""),[u,d]=r.useState(""),[C,U]=r.useState(""),[S,q]=r.useState("desc"),[k,E]=r.useState(!1),[p,b]=r.useState(null),y=r.useRef({}),A=s=>{v(l=>({...l,[s]:!l[s]}))},h=async(s={})=>{E(!0);try{let l=new URLSearchParams;s.sort&&l.append("sort",s.sort),s.invoice_type&&l.append("invoice_type",s.invoice_type),s.search&&l.append("search",s.search);const o=await Qe.callRawAPI(`/v3/api/custom/courtmatchup/user/billing/invoices${l.toString()?`?${l.toString()}`:""}`,{},"GET");if(o.error){x(f,o.message,5e3);return}N(o.invoices||[])}catch(l){console.error("ERROR",l),x(f,l.message,5e3),R(dispatch,l.message)}finally{E(!1)}},D=s=>{m(s.target.value),h({sort:S})},L=()=>!u&&!C?g:g.filter(s=>{try{const l=new Date(s.date);if(isNaN(l.getTime()))return!1;const o=u?new Date(u):null,n=C?new Date(C):null;return o&&n?l>=o&&l<=n:o?l>=o:n?l<=n:!0}catch{return console.error("Invalid date:",s.date),!1}}),T=()=>{const s=S==="asc"?"desc":"asc";q(s),h({sort:s})};r.useEffect(()=>{h({sort:S})},[]);const _=s=>new Date(s).toLocaleDateString(),w=s=>Number(s).toLocaleString("en-US",{style:"currency",currency:"usd"}),B=s=>{b(s);const l=window.open("","_blank");if(!l){x(f,"Please allow pop-ups to print receipts",5e3);return}if(!y.current[s]){x(f,"Receipt content not found",5e3);return}const n=g.find(O=>O.id===s);l.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Receipt #${n.receipt_id}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 800px;
              margin: 0 auto;
              padding: 20px;
            }
            .receipt-header {
              text-align: center;
              margin-bottom: 30px;
              border-bottom: 1px solid #eee;
              padding-bottom: 20px;
            }
            .receipt-title {
              font-size: 24px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            .receipt-id {
              font-size: 16px;
              color: #666;
            }
            .receipt-date {
              font-size: 14px;
              color: #666;
              margin-top: 5px;
            }
            .receipt-body {
              margin-bottom: 30px;
            }
            .receipt-row {
              display: flex;
              justify-content: space-between;
              margin-bottom: 10px;
              padding: 5px 0;
              border-bottom: 1px solid #f5f5f5;
            }
            .receipt-label {
              font-weight: 500;
              color: #666;
            }
            .receipt-total {
              margin-top: 20px;
              font-size: 18px;
              font-weight: bold;
              border-top: 2px solid #eee;
              padding-top: 10px;
            }
            .receipt-footer {
              margin-top: 40px;
              text-align: center;
              font-size: 14px;
              color: #999;
            }
            @media print {
              body {
                padding: 0;
                margin: 0;
              }
              .no-print {
                display: none;
              }
            }
          </style>
        </head>
        <body>
          <div class="receipt-header">
            <div class="receipt-title">Payment Receipt</div>
            <div class="receipt-id">Invoice #${n.receipt_id}</div>
            <div class="receipt-date">Date: ${_(n.date)}</div>
          </div>

          <div class="receipt-body">
            <div class="receipt-row">
              <span class="receipt-label">Type:</span>
              <span>${n.type}</span>
            </div>
            <div class="receipt-row">
              <span class="receipt-label">Amount:</span>
              <span>${w(n.amount)}</span>
            </div>
            ${n.coach_fee>0?`
            <div class="receipt-row">
              <span class="receipt-label">Coach Fee:</span>
              <span>${w(n.coach_fee)}</span>
            </div>
            `:""}
            ${n.service_fee>0?`
            <div class="receipt-row">
              <span class="receipt-label">Service Fee:</span>
              <span>${w(n.service_fee)}</span>
            </div>
            `:""}
            ${n.club_fee>0?`
            <div class="receipt-row">
              <span class="receipt-label">Club Fee:</span>
              <span>${w(n.club_fee)}</span>
            </div>
            `:""}
            <div class="receipt-row">
              <span class="receipt-label">Valid Until:</span>
              <span>${_(n.valid_until)}</span>
            </div>
            <div class="receipt-row">
              <span class="receipt-label">Payment Method:</span>
              <span>${n.payment_method}</span>
            </div>
            <div class="receipt-row">
              <span class="receipt-label">Status:</span>
              <span class="capitalize">${n.status}</span>
            </div>

            <div class="receipt-total">
              <div class="receipt-row">
                <span class="receipt-label">Total:</span>
                <span>${w(n.total_amount)}</span>
              </div>
            </div>
          </div>

          <div class="receipt-footer">
            Thank you for your payment.
          </div>

          <script>
            window.onload = function() {
              window.print();
              setTimeout(function() {
                window.close();
              }, 500);
            };
          <\/script>
        </body>
      </html>
    `),l.document.close(),setTimeout(()=>{b(null)},1e3)};return e.jsxs("div",{className:"mx-auto max-w-3xl p-4 sm:p-6",children:[k&&e.jsx(Q,{}),e.jsxs("div",{className:"mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between",children:[e.jsx("h1",{className:"text-2xl font-semibold",children:"Billing"}),e.jsxs("select",{className:"w-full rounded-lg border-gray-200 px-4 py-2 capitalize sm:w-auto",onChange:s=>h({invoice_type:s.target.value,sort:S}),children:[e.jsx("option",{value:"",children:"All bills"}),e.jsx("option",{value:"subscription",children:"Subscription"}),e.jsx("option",{value:"lesson",children:"Lesson"}),e.jsx("option",{value:"clinic",children:"Clinic"}),e.jsx("option",{value:"club_court",children:"Club Court"})]})]}),e.jsx("div",{className:"mb-5 border-b border-gray-200"}),e.jsxs("div",{className:"mb-6 flex flex-col gap-3 sm:flex-row",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx(Ae,{className:"absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400"}),e.jsx("input",{type:"text",value:P,onChange:D,placeholder:"Search by plan name or invoice number",className:"w-full rounded-lg border border-gray-200 py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{className:"flex w-full gap-2 sm:w-auto",children:[e.jsx("input",{type:"date",value:u,onChange:s=>d(s.target.value),placeholder:"Start date",className:"w-full rounded-lg border border-gray-200 px-3 py-2 text-sm sm:w-auto"}),e.jsx("input",{type:"date",value:C,onChange:s=>U(s.target.value),placeholder:"End date",className:"w-full rounded-lg border border-gray-200 px-3 py-2 text-sm sm:w-auto"}),(u||C)&&e.jsx("button",{onClick:()=>{d(""),U("")},className:"rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-500 hover:bg-gray-50 hover:text-gray-700",title:"Clear dates",children:"✕"})]}),e.jsxs("button",{onClick:T,className:"flex w-full items-center justify-center gap-2 rounded-lg border border-gray-200 px-4 py-2 hover:bg-gray-50 sm:w-auto",children:[S==="asc"?"Oldest first":"Latest first",e.jsx(Oe,{className:`transform ${S==="desc"?"rotate-180":""}`})]})]}),e.jsx("div",{className:"space-y-4",children:L().length===0?e.jsx("div",{className:"py-8 text-center text-gray-500",children:"No billing records found"}):L().map(s=>e.jsxs("div",{className:"rounded-lg border border-gray-100 bg-gray-50 p-3 shadow-sm",children:[e.jsxs("button",{onClick:()=>A(s.id),className:"flex w-full flex-col gap-2 sm:flex-row sm:items-center sm:justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Te,{className:`transform transition-transform ${j[s.id]?"":"-rotate-90"}`}),e.jsx("span",{className:"text-sm sm:text-base",children:s.type})]}),e.jsxs("div",{className:"flex items-center justify-between gap-4 pl-6 sm:pl-0",children:[e.jsx("span",{className:"text-sm text-gray-600",children:_(s.create_at)}),e.jsx("span",{className:"font-medium",children:w(s.total_amount)})]})]}),j[s.id]&&e.jsx("div",{className:"mt-2 rounded-lg bg-white p-4",children:e.jsxs("div",{className:"space-y-4",ref:l=>y.current[s.id]=l,children:[e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Amount"}),e.jsx("span",{children:w(s.amount)})]}),s.coach_fee>0&&e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Coach fee"}),e.jsx("span",{children:w(s.coach_fee)})]}),s.service_fee>0&&e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Service fee"}),e.jsx("span",{children:w(s.service_fee)})]}),s.club_fee>0&&e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Club fee"}),e.jsx("span",{children:w(s.club_fee)})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Invoice ID"}),e.jsxs("span",{children:["#",s.receipt_id]})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Date"}),e.jsx("span",{children:_(s.date)})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Valid until"}),e.jsx("span",{children:_(s.valid_until)})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Payment method"}),e.jsx("span",{children:s.payment_method})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Status"}),e.jsx("span",{className:"capitalize",children:s.status})]}),e.jsxs("button",{onClick:l=>{l.stopPropagation(),B(s.id)},className:"mt-4 flex w-full items-center justify-center gap-2 rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50",children:[e.jsx(Fe,{className:"text-lg"}),"Print Receipt"]})]})})]},s.id))})]})}const Zs=()=>{const{dispatch:f}=$.useContext(H),[j]=ve(),[v,g]=r.useState("profile"),N=we();r.useEffect(()=>{const m=j.get("tab");m&&g({"payment-methods":"payment-methods",profile:"profile",membership:"membership",billing:"billing"}[m]||"profile")},[j.get("tab")]);const P=[{label:"Profile details",value:"profile",icon:Me},{label:"Payment methods",value:"payment-methods",icon:Le},{label:"Membership",value:"membership",icon:Be},{label:"Billing",value:"billing",icon:$e}];return r.useEffect(()=>{f({type:"SETPATH",payload:{path:"profile"}})},[]),e.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:e.jsxs("div",{className:"mx-auto  max-w-6xl px-4",children:[e.jsx(Ze,{onBack:()=>N("/user/dashboard")}),e.jsxs("div",{className:"flex flex-col gap-8 md:flex-row",children:[e.jsx("div",{className:"max-h-fit w-full rounded-xl bg-white shadow-sm md:max-w-xs",children:e.jsx("nav",{className:"no-scrollbar flex w-full flex-row overflow-x-auto p-4 md:flex-col",children:P.map(m=>{const u=m.icon;return e.jsxs("button",{onClick:()=>{g(m.value),N(`/user/profile?tab=${m.value}`)},className:`mr-2 flex min-w-fit items-center whitespace-nowrap rounded-lg px-4 py-3 transition-colors md:mb-2 md:mr-0 ${v===m.value?"bg-gray-100 text-gray-900":"text-gray-600 hover:bg-gray-50"}`,children:[e.jsx(u,{className:"mr-2 h-5 w-5 text-gray-500"}),e.jsx("span",{className:"text-sm",children:m.label})]},m.value)})})}),e.jsxs("div",{className:" w-full rounded-xl bg-white p-6 shadow-sm",children:[v==="profile"&&e.jsx(qe,{}),v==="payment-methods"&&e.jsx(Ye,{}),v==="membership"&&e.jsx(Ke,{}),v==="billing"&&e.jsx(We,{})]})]})]})})};export{Zs as default};

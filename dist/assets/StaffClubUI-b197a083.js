import{j as o}from"./@nivo/heatmap-ba1ecfff.js";import{u as l,aO as d,e as S,aC as g,M as x}from"./index-f915b394.js";import{C}from"./ClubUI-a3669001.js";import{r as i}from"./vendor-851db8c1.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./SportList-49113b44.js";import"./PlusIcon-7e8d14d7.js";import"./PencilIcon-35185602.js";import"./TrashIcon-aaaccaf2.js";import"./InformationCircleIcon-d35f3488.js";import"./SplashScreenPagePreview-a5d606e1.js";import"./BottomDrawer-7b010fbb.js";import"./ImageCropModal-34566426.js";import"./react-image-crop-1f5038af.js";import"./index.esm-09a3a6b8.js";import"./index.esm-b72032a7.js";import"./AuthLayout-33fa006e.js";import"./MembershipCard-4b55dc0a.js";import"./SportTypeSelection-ee0cc3da.js";import"./SelectionOptionsCard-0d5c6ddd.js";import"./SelectionOption-01b973e9.js";import"./HistoryComponent-a235a60c.js";new x;function xt(){const[m,h]=i.useState(null),{club:t,sports:p,courts:e,fetchClubData:a,pricing:c}=l(),[r,n]=i.useState(null),[u,s]=i.useState(!1);return i.useEffect(()=>{(async()=>{s(!0);const f=await d(t==null?void 0:t.id);n(f),s(!1)})()},[t==null?void 0:t.id]),o.jsxs("div",{children:[u&&o.jsx(S,{}),r&&r.staff.club_ui?o.jsx(C,{selectedClub:{name:""},profileSettings:m,fetchSettings:a,club:t,courts:e,sports:p,pricing:c}):o.jsx(g,{message:"You don't have permission to access the club UI"})]})}export{xt as default};

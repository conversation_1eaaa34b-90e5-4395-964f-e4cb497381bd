import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as a,f as ne}from"./vendor-851db8c1.js";import{w as le,M as ce,G as de,A as me,B as pe,t as ue}from"./index-f915b394.js";import{M as R}from"./index-be4468eb.js";import{c as xe,a as d}from"./yup-54691517.js";import{u as he}from"./react-hook-form-687afde5.js";import{o as ge}from"./yup-2824f222.js";import{A as fe}from"./index.esm-9c6194ba.js";import{a as je}from"./index.esm-c561e951.js";import be from"./AddAdminEmailPage-df0a19d3.js";import ye from"./EditAdminEmailPage-87fb4d30.js";import{A as ve}from"./AddButton-df0c3574.js";import{P as we}from"./index-eb1bc208.js";import Ne from"./Skeleton-1e8bf077.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@hookform/resolvers-67648cca.js";import"./AddButton.module-98aac587.js";import"./react-loading-skeleton-3d87d1f5.js";let L=new ce;const m=[{header:"ID",accessor:"id"},{header:"Email Type",accessor:"slug"},{header:"Subject",accessor:"subject"},{header:"Tags",accessor:"tag"},{header:"Action",accessor:""}],Se=()=>{const{dispatch:M}=a.useContext(de),{dispatch:I}=a.useContext(me),[S,O]=a.useState([]),[l,A]=a.useState(10),[q,z]=a.useState(0),[Ae,B]=a.useState(0),[p,G]=a.useState(0),[Y,H]=a.useState(!1),[K,_]=a.useState(!1),[C,E]=a.useState(!1),[F,P]=a.useState(!1),[c,u]=a.useState([]),[J,x]=a.useState([]),[Q,U]=a.useState("eq"),[h,g]=a.useState(!0),[f,j]=a.useState(!1),[k,b]=a.useState(!1),[V,W]=a.useState();ne();const y=a.useRef(null),X=xe({id:d(),email:d(),role:d(),status:d()}),{register:Ce,handleSubmit:Z,formState:{errors:Ee}}=he({resolver:ge(X)});function ee(){o(p-1,l)}function te(){o(p+1,l)}const se=(t,i,s)=>{const r=i==="eq"&&isNaN(s)?`${s}`:s,n=`${t},${i},${r}`;x(v=>[...v.filter(w=>!w.includes(t)),n])},ae=()=>{o(0,l,{},J)},ie=t=>{o(0,l,{},t)};async function o(t,i,s={},r=[]){g(!(k||f));try{L.setTable("email");const n=await L.callRestAPI({payload:{...s},page:t,limit:i,filter:r},"PAGINATE");n&&g(!1);const{list:v,total:T,limit:w,num_pages:$,page:N}=n;O(v),A(w),z($),G(N),B(T),H(N>1),_(N+1<=$)}catch(n){g(!1),console.log("ERROR",n),ue(I,n.message)}}const re=t=>{const i=m.filter(s=>s.accessor).map(s=>{const r=getNonNullValue(t[s.accessor]);return r?`${s.accessor},cs,${r}`:null}).filter(Boolean);o(0,l,{},i)};a.useEffect(()=>{M({type:"SETPATH",payload:{path:"email"}});const i=setTimeout(async()=>{await o(1,l)},700);return()=>{clearTimeout(i)}},[]);const D=t=>{y.current&&!y.current.contains(t.target)&&E(!1)};a.useEffect(()=>(document.addEventListener("mousedown",D),()=>{document.removeEventListener("mousedown",D)}),[]);const oe=()=>{u([]),x([]),o(1,l)};return e.jsxs("div",{className:"px-8",children:[e.jsxs("div",{className:"flex items-center justify-between py-3",children:[e.jsx("form",{className:"relative rounded bg-white",onSubmit:Z(re),children:e.jsx("div",{className:"flex items-center gap-4 text-nowrap text-gray-700",children:e.jsxs("div",{className:"relative",ref:y,children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-1",onClick:()=>E(!C),children:[e.jsx(pe,{}),e.jsx("span",{children:"Filters"}),c.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white",children:c.length})]}),C&&e.jsx("div",{className:"top-fill filter-form-holder absolute left-0 z-10 mt-4 w-[500px] min-w-[90%] rounded-md border border-gray-200 bg-white shadow-lg",children:e.jsxs("div",{className:"p-4",children:[c==null?void 0:c.map((t,i)=>e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("div",{className:" mb-3 w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",children:t}),e.jsxs("select",{className:"mb-3 w-[40%] rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:s=>{U(s.target.value)},children:[e.jsx("option",{value:"eq",selected:!0,children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),e.jsx("input",{type:"text",placeholder:"Enter value...",className:" mb-3 w-1/3 rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:s=>se(t,Q,s.target.value)}),e.jsx("div",{className:"mt-[-10px] w-1/12",children:e.jsx(je,{className:" cursor-pointer text-xl",onClick:()=>{u(s=>s.filter(r=>r!==t)),x(s=>{const r=s.filter(n=>!n.includes(t));return ie(r),r})}})})]},i)),e.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out ",onClick:()=>{P(!F)},children:[e.jsx(fe,{}),"Add filter"]}),F&&e.jsx("div",{className:"absolute top-11 z-10 bg-white px-5 py-3 text-gray-600 shadow-md",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:m.slice(0,-1).map(t=>e.jsx("li",{className:`${c.includes(t.accessor)?"cursor-not-allowed text-gray-400":"cursor-pointer"}`,onClick:()=>{c.includes(t.header)||u(i=>[...i,t.accessor]),P(!1)},children:t.header},t.header))})}),c.length>0&&e.jsx("div",{onClick:oe,className:"inline-block cursor-pointer  rounded px-6  py-2.5 font-medium leading-tight text-gray-600  transition duration-150 ease-in-out",children:"Clear all filter"})]}),e.jsx("button",{type:"button",onClick:ae,className:"mt-4 inline-block cursor-pointer rounded bg-blue-500 px-6 py-2.5 font-medium leading-tight text-white transition duration-150 ease-in-out",children:"Apply Filters"})]})})]})})}),e.jsx(ve,{onClick:()=>b(!0)})]}),h?e.jsx(Ne,{}):e.jsxs("div",{className:"overflow-x-auto border-b border-gray-200 shadow ",children:[e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx("tr",{children:m.map((t,i)=>e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[t.header,e.jsx("span",{children:t.isSorted?t.isSortedDesc?" ▼":" ▲":""})]},i))})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:S.map((t,i)=>e.jsx("tr",{children:m.map((s,r)=>s.accessor==""?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsxs("button",{className:"text-[#4F46E5]",onClick:()=>{W(t.id),j(!0)},children:[" ","Edit"]})},r):s.mapping&&s.accessor==="status"?e.jsx("td",{className:"inline-block whitespace-nowrap px-6 py-5 text-sm",children:t[s.accessor]===1?e.jsx("span",{className:"rounded-md bg-[#D1FAE5] px-3 py-1 text-[#065F46]",children:s.mapping[t[s.accessor]]}):e.jsx("span",{className:"rounded-md bg-[#F4F4F4] px-3 py-1 text-[#393939]",children:s.mapping[t[s.accessor]]})},r):s.mapping?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s.mapping[t[s.accessor]]},r):e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t[s.accessor]},r))},i))})]}),h&&e.jsx(e.Fragment,{children:e.jsx("p",{className:" px-10 py-3 text-xl capitalize ",children:"Loading..."})}),!h&&S.length===0&&e.jsx(e.Fragment,{children:e.jsx("p",{className:" px-10 py-3 text-xl capitalize ",children:"You Don't have any Data"})})]}),e.jsx(we,{currentPage:p,pageCount:q,pageSize:l,canPreviousPage:Y,canNextPage:K,updatePageSize:t=>{A(t),o(1,t)},previousPage:ee,nextPage:te}),e.jsx(R,{isModalActive:k,closeModalFn:()=>b(!1),children:e.jsx(be,{setSidebar:b,getData:o})}),f&&e.jsx(R,{isModalActive:f,closeModalFn:()=>j(!1),children:e.jsx(ye,{activeId:V,setSidebar:j})})]})},yt=le(Se,"email","You don't have permission to access email management");export{yt as default};

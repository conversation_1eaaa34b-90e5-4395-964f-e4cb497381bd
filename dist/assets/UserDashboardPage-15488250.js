import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as r,f as V,L as k}from"./vendor-851db8c1.js";import{c as O,v as F,M as L,T as P,A as E,G as I,u as z,e as K,_ as Q,a3 as _,Z as S,F as J}from"./index-f915b394.js";import"./react-qr-code-4a7125ac.js";import{B as Y}from"./BottomDrawer-7b010fbb.js";import{b as W}from"./index.esm-b72032a7.js";import{R as X,T as q,P as ee,F as se}from"./ReservationStatus-5ced670f.js";import{g as te,f as H,h as M,j as ae}from"./date-fns-07266b7d.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";function re({isOpen:s,onClose:d,coach:o,coaches:n}){const[l,u]=r.useState(""),[x,m]=r.useState(!0),[j,C]=r.useState(o);return r.useEffect(()=>{C(o)},[o]),e.jsx(Y,{isOpen:s,onClose:d,title:"Coach Profiles",children:e.jsxs("div",{className:"mx-auto flex w-full max-w-6xl flex-col gap-4 md:flex-row",children:[e.jsxs("div",{className:"max-h-fit w-full space-y-6 rounded-xl bg-white p-4 shadow-5",children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Search by name",value:l,onChange:a=>u(a.target.value),className:"w-full rounded-lg border border-gray-300 py-2 pl-10 pr-4 focus:border-blue-500 focus:outline-none"}),e.jsx(O,{className:"absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400"}),e.jsx("button",{onClick:()=>m(!x),className:"absolute right-2 top-1/2 -translate-y-1/2 transform rounded-md border border-gray-300 px-2 py-1 text-sm hover:bg-gray-50",children:e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{children:"A-Z"}),e.jsx(W,{className:`text-xs transition-transform ${x?"":"rotate-180"}`})]})})]}),e.jsxs("div",{className:"max-h-[400px] space-y-4 overflow-y-auto",children:[n.length>0&&n.filter(a=>{var h,p;return`${(h=a.user)==null?void 0:h.first_name} ${(p=a.user)==null?void 0:p.last_name}`.toLowerCase().includes(l.toLowerCase())}).sort((a,f)=>{var g,t,w,N;const h=`${(g=a.user)==null?void 0:g.first_name} ${(t=a.user)==null?void 0:t.last_name}`.toLowerCase(),p=`${(w=f.user)==null?void 0:w.first_name} ${(N=f.user)==null?void 0:N.last_name}`.toLowerCase();return x?h.localeCompare(p):p.localeCompare(h)}).map(a=>{var f,h,p,g,t;return e.jsxs("div",{className:`flex cursor-pointer items-center justify-between rounded-xl border p-3 ${(j==null?void 0:j.id)===a.id?"border-[1.5px] border-primaryBlue bg-blue-50":"border-[1.5px] border-gray-100 hover:bg-gray-50"}`,onClick:()=>C(a),children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("img",{src:a.photo||((f=a.user)==null?void 0:f.photo)||"/default-avatar.png",alt:`${(h=a.user)==null?void 0:h.first_name} ${(p=a.user)==null?void 0:p.last_name}`,className:"h-10 w-10 rounded-full object-cover"}),e.jsx("div",{className:"flex flex-col",children:e.jsxs("span",{className:"font-medium capitalize",children:[(g=a.user)==null?void 0:g.first_name," ",(t=a.user)==null?void 0:t.last_name]})})]}),e.jsxs("span",{className:"text-gray-600",children:[F(a.hourly_rate),"/h"]})]},a.id)}),n.length===0&&e.jsx("p",{className:"text-center text-sm text-gray-500",children:"No coaches found"})]})]}),e.jsx("div",{className:"w-full",children:e.jsx(ie,{selectedCoach:j})})]})})}function ie({selectedCoach:s}){var o,n,l,u,x,m;if(!s)return null;const d=V();return e.jsxs("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsx("h2",{className:"text-xl font-semibold",children:"Coach profile"})}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:s.photo||((o=s.user)==null?void 0:o.photo)||"/default-avatar.png",alt:`${(n=s.user)==null?void 0:n.first_name} ${(l=s.user)==null?void 0:l.last_name}`,className:"h-12 w-12 rounded-lg object-cover"}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-medium capitalize",children:[(u=s.user)==null?void 0:u.first_name," ",(x=s.user)==null?void 0:x.last_name]}),e.jsxs("p",{className:"text-sm text-gray-600",children:[F(s.hourly_rate),"/h"]})]})]}),s.bio&&e.jsx("div",{className:"space-y-2",children:e.jsx("p",{className:"text-gray-600",children:s.bio})})]}),e.jsxs("button",{onClick:()=>d(`/user/lessons?coach=${s.id}`),className:"mt-5 rounded-lg bg-blue-900 px-6 py-3 text-sm capitalize text-white hover:bg-blue-800",children:["Select ",(m=s==null?void 0:s.user)==null?void 0:m.first_name," and continue."]})]})}let B=new L,le=new P;const R=s=>{const d=new Date(s),o=H(d,"EEEE"),n=H(d,"(MMM dd)");return`${o} ${n}`},ze=()=>{V(),r.useContext(E);const{dispatch:s}=r.useContext(I);r.useContext(E);const[d,o]=r.useState([]),[n,l]=r.useState(!1),[u,x]=r.useState([]),[m,j]=r.useState(!0),[C,a]=r.useState(!1),[f,h]=r.useState(null);r.useState(null);const[p,g]=r.useState([]),{club:t,sports:w}=z(),[N,A]=r.useState(null);r.useEffect(()=>{s({type:"SETPATH",payload:{path:"dashboard"}})},[]),localStorage.getItem("user");async function D(){try{l(!0);const i=m?"/v3/api/custom/courtmatchup/user/buddy/my-requests":"/v3/api/custom/courtmatchup/user/buddy/all-requests";console.log("Fetching requests from:",i);const c=await B.callRawAPI(i,{},"GET");console.log("Response:",c),c&&c.my_requests?x(c.my_requests):c&&c.list?x(c.list):x([])}catch(i){console.error("Error fetching requests:",i),x([])}finally{l(!1)}}async function T(){try{l(!0);const i=await B.callRawAPI("/v3/api/custom/courtmatchup/user/reservations",{},"GET"),c=M(new Date),$=i.list.filter(v=>{const y=M(new Date(v.booking_date));return ae(y,c)||y.getTime()===c.getTime()}).sort((v,y)=>{const U=new Date(`${v.booking_date}T${v.start_time}`),G=new Date(`${y.booking_date}T${y.start_time}`);return U-G});o($)}catch(i){console.error(i)}finally{l(!1)}}async function Z(){const i=await le.getList("coach",{join:["user|user_id"]}),c=i.list[0];g(i.list),console.log("Newly added coach:",c),A(c)}r.useEffect(()=>{(async()=>(l(!0),await T(),await Z(),l(!1)))()},[]),r.useEffect(()=>{console.log("Toggle state changed:",m),D()},[m]);const b=i=>{console.log("Toggle clicked, new value:",!m),j(!m)};return e.jsxs("div",{className:"mx-auto max-w-7xl space-y-6 p-3 md:p-6",children:[n&&e.jsx(K,{}),t!=null&&t.home_image?e.jsx("div",{className:"",children:e.jsxs("div",{className:"flex flex-col gap-4 md:flex-row md:gap-10",children:[e.jsx("div",{className:"relative mb-4 flex-1 overflow-hidden rounded-xl shadow-md transition-all duration-300 hover:shadow-lg md:mb-6",children:e.jsx("div",{className:"relative h-[200px] w-full overflow-hidden md:h-[300px]",children:e.jsx("img",{src:t==null?void 0:t.home_image,alt:"Club Home Image",className:"h-full w-full object-cover transition-transform duration-700 hover:scale-105",loading:"lazy"})})}),e.jsxs("div",{className:"flex-1",children:[(t==null?void 0:t.title)&&e.jsx("h1",{className:"mb-2 text-2xl font-bold tracking-tight md:text-3xl",children:t==null?void 0:t.title}),(t==null?void 0:t.description)&&e.jsx("p",{className:"max-w-2xl text-base md:text-lg",children:t==null?void 0:t.description})]})]})}):e.jsxs("div",{className:"to-navy-900 mb-6 rounded-xl p-4 md:p-6",children:[(t==null?void 0:t.title)&&e.jsx("h1",{className:"mb-2 text-2xl font-bold tracking-tight text-white md:text-3xl",children:t==null?void 0:t.title}),(t==null?void 0:t.description)&&e.jsx("p",{className:"max-w-2xl text-base text-gray-200 md:text-lg",children:t==null?void 0:t.description})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[e.jsxs("div",{className:"h-fit rounded-2xl bg-white p-6 shadow-sm",children:[e.jsxs("div",{className:"mb-6 flex items-center gap-2",children:[e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M4.75 3C3.7835 3 3 3.7835 3 4.75V19.25C3 20.2165 3.7835 21 4.75 21H19.25C20.2165 21 21 20.2165 21 19.25V4.75C21 3.7835 20.2165 3 19.25 3H4.75ZM8 11C7.44772 11 7 11.4477 7 12C7 12.5523 7.44772 13 8 13C8.55228 13 9 12.5523 9 12C9 11.4477 8.55228 11 8 11ZM8 15C7.44772 15 7 15.4477 7 16C7 16.5523 7.44772 17 8 17C8.55228 17 9 16.5523 9 16C9 15.4477 8.55228 15 8 15ZM11 16C11 15.4477 11.4477 15 12 15C12.5523 15 13 15.4477 13 16C13 16.5523 12.5523 17 12 17C11.4477 17 11 16.5523 11 16ZM12 11C11.4477 11 11 11.4477 11 12C11 12.5523 11.4477 13 12 13C12.5523 13 13 12.5523 13 12C13 11.4477 12.5523 11 12 11ZM15 12C15 11.4477 15.4477 11 16 11C16.5523 11 17 11.4477 17 12C17 12.5523 16.5523 13 16 13C15.4477 13 15 12.5523 15 12ZM4.75 4.5C4.61193 4.5 4.5 4.61193 4.5 4.75V7H19.5V4.75C19.5 4.61193 19.3881 4.5 19.25 4.5H4.75Z",fill:"#176448"})}),e.jsx("h2",{className:"text-lg font-medium",children:"Upcoming reservations"})]}),e.jsx("div",{className:"max-h-[600px] overflow-y-auto ",children:d.length>0?e.jsx("div",{className:"space-y-4",children:d.map(i=>e.jsx(oe,{reservation:i,sports:w}))}):e.jsxs("div",{className:"flex flex-col items-center justify-center py-8",children:[e.jsxs("svg",{width:"60",height:"60",viewBox:"0 0 60 60",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"59",height:"59",rx:"29.5",fill:"url(#paint0_linear_158_11396)"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"59",height:"59",rx:"29.5",stroke:"url(#paint1_linear_158_11396)"}),e.jsxs("g",{filter:"url(#filter0_d_158_11396)",children:[e.jsx("rect",{x:"8",y:"8",width:"44",height:"44",rx:"22",fill:"white"}),e.jsx("rect",{x:"8.5",y:"8.5",width:"43",height:"43",rx:"21.5",stroke:"#E2E4E9"}),e.jsx("path",{d:"M20 30C20 27.2819 21.0844 24.8171 22.8443 23.0146C25.0536 24.5496 26.5 27.1059 26.5 30C26.5 32.8941 25.0536 35.4504 22.8443 36.9854C21.0844 35.1829 20 32.7181 20 30Z",fill:"#868C98"}),e.jsx("path",{d:"M28 30C28 26.7284 26.4289 23.8237 24 21.9993C25.6713 20.7439 27.7488 20 30 20C32.2512 20 34.3287 20.7439 36 21.9993C33.5711 23.8237 32 26.7284 32 30C32 33.2716 33.5711 36.1763 36 38.0007C34.3287 39.2561 32.2512 40 30 40C27.7488 40 25.6713 39.2561 24 38.0007C26.4289 36.1763 28 33.2716 28 30Z",fill:"#868C98"}),e.jsx("path",{d:"M37.1557 23.0146C38.9156 24.8171 40 27.2819 40 30C40 32.7181 38.9156 35.1829 37.1557 36.9854C34.9464 35.4504 33.5 32.8941 33.5 30C33.5 27.1059 34.9464 24.5496 37.1557 23.0146Z",fill:"#868C98"})]}),e.jsxs("defs",{children:[e.jsxs("filter",{id:"filter0_d_158_11396",x:"4",y:"6",width:"52",height:"52",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB",children:[e.jsx("feFlood",{"flood-opacity":"0",result:"BackgroundImageFix"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"2"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"}),e.jsx("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_158_11396"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_158_11396",result:"shape"})]}),e.jsxs("linearGradient",{id:"paint0_linear_158_11396",x1:"30",y1:"0",x2:"30",y2:"60",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7","stop-opacity":"0.48"}),e.jsx("stop",{offset:"1","stop-color":"#F7F8F8","stop-opacity":"0"}),e.jsx("stop",{offset:"1","stop-color":"#E4E5E7","stop-opacity":"0"})]}),e.jsxs("linearGradient",{id:"paint1_linear_158_11396",x1:"30",y1:"0",x2:"30",y2:"60",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7"}),e.jsx("stop",{offset:"0.765625","stop-color":"#E4E5E7","stop-opacity":"0"})]})]})]}),e.jsx("p",{className:"mb-4 text-gray-600",children:"You have no upcoming reservations"}),e.jsx(k,{to:"/user/reserve-court",className:"font-medium text-blue-600 hover:text-blue-700",children:"Reserve a court"})]})})]}),e.jsxs("div",{className:"max-h-fit rounded-lg bg-white p-6 shadow-sm",children:[e.jsx("div",{className:"mb-6 flex items-center justify-between",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M12 2C9.51472 2 7.5 4.01472 7.5 6.5C7.5 8.98528 9.51472 11 12 11C14.4853 11 16.5 8.98528 16.5 6.5C16.5 4.01472 14.4853 2 12 2Z",fill:"#176448"}),e.jsx("path",{d:"M16.5081 13.8263C16.1908 14.2141 16.0005 14.7098 16.0005 15.25V16H15.2505C14.0078 16 13.0005 17.0074 13.0005 18.25C13.0005 19.4926 14.0078 20.5 15.2505 20.5H16.0005V21H5.59881C4.60008 21 3.69057 20.1119 3.9402 19.0012C4.7686 15.3152 8.21185 12.5 12.0004 12.5C13.6638 12.5 15.2115 12.9805 16.5081 13.8263Z",fill:"#176448"}),e.jsx("path",{d:"M19 15.25C19 14.8358 18.6642 14.5 18.25 14.5C17.8358 14.5 17.5 14.8358 17.5 15.25V17.5H15.25C14.8358 17.5 14.5 17.8358 14.5 18.25C14.5 18.6642 14.8358 19 15.25 19H17.5V21.25C17.5 21.6642 17.8358 22 18.25 22C18.6642 22 19 21.6642 19 21.25V19H21.25C21.6642 19 22 18.6642 22 18.25C22 17.8358 21.6642 17.5 21.25 17.5H19V15.25Z",fill:"#176448"})]}),e.jsx("h2",{className:"text-lg font-medium",children:"Open requests"})]})}),e.jsxs("div",{className:"mb-4 flex items-center gap-3",children:[e.jsxs("div",{className:"relative inline-flex h-6 w-11 cursor-pointer items-center rounded-full bg-gray-200",onClick:b,children:[e.jsx("input",{type:"checkbox",checked:m,onChange:b,className:"peer sr-only"}),e.jsx("div",{className:"peer h-6 w-11 cursor-pointer rounded-full bg-gray-200 after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-navy-700 peer-checked:after:translate-x-full peer-checked:after:border-white"})]}),e.jsx("span",{className:"cursor-pointer select-none text-base text-gray-900",onClick:b,children:"My requests only"})]}),u.length>0?e.jsx("div",{className:"max-h-[500px] space-y-4 overflow-y-auto",children:u.map(i=>e.jsx(ne,{request:i,sports:w},i.buddy_id))}):e.jsxs("div",{className:"flex flex-col items-center justify-center py-8",children:[e.jsxs("svg",{width:"60",height:"60",viewBox:"0 0 60 60",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"59",height:"59",rx:"29.5",fill:"url(#paint0_linear_187_35666)"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"59",height:"59",rx:"29.5",stroke:"url(#paint1_linear_187_35666)"}),e.jsxs("g",{filter:"url(#filter0_d_187_35666)",children:[e.jsx("rect",{x:"8",y:"8",width:"44",height:"44",rx:"22",fill:"white"}),e.jsx("rect",{x:"8.5",y:"8.5",width:"43",height:"43",rx:"21.5",stroke:"#E2E4E9"}),e.jsx("path",{d:"M30 20C27.5147 20 25.5 22.0147 25.5 24.5C25.5 26.9853 27.5147 29 30 29C32.4853 29 34.5 26.9853 34.5 24.5C34.5 22.0147 32.4853 20 30 20Z",fill:"#868C98"}),e.jsx("path",{d:"M34.5081 31.8263C34.1908 32.2141 34.0005 32.7098 34.0005 33.25V34H33.2505C32.0078 34 31.0005 35.0074 31.0005 36.25C31.0005 37.4926 32.0078 38.5 33.2505 38.5H34.0005V39H23.5988C22.6001 39 21.6906 38.1119 21.9402 37.0012C22.7686 33.3152 26.2118 30.5 30.0004 30.5C31.6638 30.5 33.2115 30.9805 34.5081 31.8263Z",fill:"#868C98"}),e.jsx("path",{d:"M37 33.25C37 32.8358 36.6642 32.5 36.25 32.5C35.8358 32.5 35.5 32.8358 35.5 33.25V35.5H33.25C32.8358 35.5 32.5 35.8358 32.5 36.25C32.5 36.6642 32.8358 37 33.25 37H35.5V39.25C35.5 39.6642 35.8358 40 36.25 40C36.6642 40 37 39.6642 37 39.25V37H39.25C39.6642 37 40 36.6642 40 36.25C40 35.8358 39.6642 35.5 39.25 35.5H37V33.25Z",fill:"#868C98"})]}),e.jsxs("defs",{children:[e.jsxs("filter",{id:"filter0_d_187_35666",x:"4",y:"6",width:"52",height:"52",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB",children:[e.jsx("feFlood",{"flood-opacity":"0",result:"BackgroundImageFix"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"2"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"}),e.jsx("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_187_35666"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_187_35666",result:"shape"})]}),e.jsxs("linearGradient",{id:"paint0_linear_187_35666",x1:"30",y1:"0",x2:"30",y2:"60",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7","stop-opacity":"0.48"}),e.jsx("stop",{offset:"1","stop-color":"#F7F8F8","stop-opacity":"0"}),e.jsx("stop",{offset:"1","stop-color":"#E4E5E7","stop-opacity":"0"})]}),e.jsxs("linearGradient",{id:"paint1_linear_187_35666",x1:"30",y1:"0",x2:"30",y2:"60",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7"}),e.jsx("stop",{offset:"0.765625","stop-color":"#E4E5E7","stop-opacity":"0"})]})]})]}),e.jsx("p",{className:"mb-4 text-gray-600",children:"There are no open requests"}),e.jsx(k,{to:"/user/custom-request",className:"font-medium text-blue-600 hover:text-blue-700",children:"Make request"})]})]})]}),e.jsx(re,{isOpen:C,onClose:()=>a(!1),coach:f,coaches:p})]})},oe=({reservation:s,sports:d})=>{d.find(l=>l.id===s.sport_id);const o=JSON.parse(s.player_ids),n=Q(s==null?void 0:s.reservation_updated_at);return e.jsx("div",{children:e.jsxs("div",{className:"rounded-2xl bg-gray-50 p-3 md:p-4",children:[e.jsx("div",{className:`inline-block rounded-full px-2 py-1 text-xs ${s.booking_type==="Lesson"&&"bg-[#C2D6FF]"||s.booking_type==="Coach"&&"bg-[#C2D6FF]"||s.booking_type==="Clinic"&&"bg-[#FFDAC2]"||s.booking_type==="Court"&&"bg-[#FFB3B3]"||"bg-green-200"} mb-3`,children:s.booking_type}),e.jsxs("div",{className:"mb-2 flex flex-wrap items-center gap-1 text-sm text-gray-600 md:gap-2 md:text-base",children:[e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"flex-shrink-0",children:e.jsx("path",{d:"M11 2.5999H13.4C13.5591 2.5999 13.7117 2.66312 13.8243 2.77564C13.9368 2.88816 14 3.04077 14 3.1999V12.7999C14 12.959 13.9368 13.1116 13.8243 13.2242C13.7117 13.3367 13.5591 13.3999 13.4 13.3999H2.6C2.44087 13.3999 2.28826 13.3367 2.17574 13.2242C2.06321 13.1116 2 12.959 2 12.7999V3.1999C2 3.04077 2.06321 2.88816 2.17574 2.77564C2.28826 2.66312 2.44087 2.5999 2.6 2.5999H5V1.3999H6.2V2.5999H9.8V1.3999H11V2.5999ZM9.8 3.7999H6.2V4.9999H5V3.7999H3.2V6.1999H12.8V3.7999H11V4.9999H9.8V3.7999ZM12.8 7.3999H3.2V12.1999H12.8V7.3999Z",fill:"#868C98"})}),e.jsxs("span",{className:"break-words",children:[R(s.booking_date)," •"," ",_(s.start_time)," -"," ",_(s.end_time)]})]}),s.activity&&e.jsx("div",{className:"mb-3 break-words font-medium text-gray-900",children:s.activity}),e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-2",children:[e.jsx("div",{className:"flex-shrink-0",children:s.booking_status===S.PENDING&&e.jsxs("div",{className:"flex items-center gap-1 md:gap-2",children:[e.jsx(X,{}),e.jsx(q,{timeLeft:n})]})||s.booking_status===S.SUCCESS&&e.jsx(ee,{})||s.booking_status===S.FAIL&&e.jsx(se,{})}),e.jsxs("div",{className:"flex max-w-fit items-center gap-1 rounded-full border bg-white px-2 py-0.5 text-gray-600 md:gap-2 md:px-3",children:[e.jsx(J,{className:"flex-shrink-0"}),e.jsx("span",{children:o.length})]})]})]},s.reservation_id)})},ne=({request:s,sports:d})=>{var o,n;return e.jsxs("div",{className:"rounded-lg bg-[#FAFBFC] p-3 md:p-4",children:[e.jsxs("div",{className:"mb-2 flex flex-wrap items-center text-xs text-gray-500 md:text-sm",children:[e.jsxs("span",{className:"mr-1",children:["Added ",te(new Date(s==null?void 0:s.create_at))," ago • By"]}),e.jsxs("div",{className:"inline-flex items-center",children:[e.jsx("img",{src:s.owner_photo||"/default-avatar.png",alt:"profile",className:"mr-1 h-5 w-5 rounded-full object-cover md:h-6 md:w-6"}),e.jsxs("span",{className:"text-xs text-gray-700 md:text-sm",children:[s.owner_first_name," ",s.owner_last_name]})]})]}),e.jsxs("div",{className:"mb-2 break-words text-sm font-medium md:text-base",children:[R(s.date)," • ",_(s.start_time)," ","- ",_(s.end_time)]}),e.jsxs("div",{className:"flex flex-col gap-2 md:flex-row md:items-center md:justify-between",children:[e.jsxs("div",{className:"flex flex-wrap items-center gap-1 text-xs text-gray-600 md:gap-2 md:text-sm",children:[e.jsx("span",{className:"break-words",children:((o=d.find(l=>l.id===s.sport_id))==null?void 0:o.name)||"--"}),s.sub_type&&e.jsxs("span",{children:["• ",s.sub_type]}),s.type&&e.jsxs("span",{children:["• ",s.type]})]}),e.jsxs("div",{className:"flex flex-wrap items-center gap-2 text-xs md:gap-3 md:text-sm",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{className:"text-gray-600",children:"NTRP:"}),e.jsxs("span",{children:[s.ntrp,s.max_ntrp?`-${s.max_ntrp}`:""]})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 flex-shrink-0 text-gray-600 md:h-5 md:w-5",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z",clipRule:"evenodd"})}),e.jsxs("span",{children:[((n=s.player_details)==null?void 0:n.length)||0,"/",s.num_players]})]})]})]})]})};export{ze as default};

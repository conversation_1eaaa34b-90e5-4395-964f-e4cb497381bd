import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r}from"./vendor-851db8c1.js";import{w as U,M as W,G as X,d as v,b as p,v as u}from"./index-f915b394.js";import{P as Z}from"./index-eb1bc208.js";import{j as ee}from"./index.esm-09a3a6b8.js";import{D as te}from"./DataTable-a2248415.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let h=new W;const se=()=>{const{dispatch:c}=r.useContext(X),[F,f]=r.useState(!0),[P,_]=r.useState([]),[C,d]=r.useState(!1),[a,w]=r.useState(null),[T,x]=r.useState(!1),[E,A]=r.useState(1),[l,g]=r.useState(10),[k,M]=r.useState(0),[D,L]=r.useState(!1),[O,$]=r.useState(!1),[ae,I]=r.useState(0),[y,J]=r.useState(1),[b,re]=r.useState(""),n=async(s=1,t=10,o=[])=>{f(!0);try{h.setTable("clubs");const i=b?[...o,{key:"name",value:b,operator:"contains"}]:o,Q=await h.callRestAPI({page:s,limit:t,filter:i},"PAGINATE"),{list:H,total:K,limit:Y,num_pages:S,page:m}=Q;_(H),g(Y),M(S),J(m),A(m),I(K),L(m>1),$(m+1<=S)}catch(i){console.error("Error fetching clubs:",i),p(c,"Error fetching clubs",5e3,"error")}finally{f(!1)}},z=async s=>{s.preventDefault();try{x(!0);const t=new FormData(s.target),o=t.get("feeType"),i={fee_type:o,...o==="monthly"?{monthly_fee_amount:parseFloat(t.get("monthlyFeeAmount"))}:{tech_fee_percentage:parseFloat(t.get("techFeePercentage")),tech_fee_fixed:parseFloat(t.get("techFeeFixed"))}};await h.callRawAPI(`/v3/api/custom/courtmatchup/admin/profile-edit/${a.user_id}`,{fee_settings:[i]},"POST"),p(c,"Fee settings updated successfully",5e3,"success"),d(!1),n()}catch(t){console.error("Error updating fee settings:",t),p(c,"Error updating fee settings",5e3,"error")}finally{x(!1)}};function V(){n(y-1,l)}function G(){n(y+1,l)}r.useEffect(()=>{c({type:"SETPATH",payload:{path:"fee-settings"}}),n()},[]);const R=s=>{w(s),d(!0)},j=s=>{try{if(!s.fee_settings)return null;const t=JSON.parse(s.fee_settings);return Array.isArray(t)?t[0]:null}catch(t){return console.error("Error parsing fee settings:",t),null}},q=(s,t)=>!t||t.fee_type!=="tech"?0:s*t.tech_fee_percentage/100+t.tech_fee_fixed,N=[{header:"Club Name",accessor:"name"},{header:"Fee Type",accessor:"fee_type",renderCustomCell:s=>{const t=j(s);return e.jsx("span",{className:"font-medium capitalize",children:t?t.fee_type==="monthly"?"Monthly Fee":"Technology Fee":e.jsx("span",{className:"text-gray-400",children:"Not configured"})})}},{header:"Fee Details",accessor:"fee_details",renderCustomCell:s=>{const t=j(s);return t?t.fee_type==="monthly"?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-gray-600",children:"Monthly Amount:"}),e.jsx("span",{className:"font-medium",children:u(t.monthly_fee_amount)})]}):e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-gray-600",children:"Tech Fee:"}),e.jsxs("span",{className:"font-medium",children:[t.tech_fee_percentage,"% +"," ",u(t.tech_fee_fixed)]})]}),e.jsxs("div",{className:"text-sm text-gray-500",children:["Example: ",u(q(50,t))," fee on $50 transaction"]})]}):e.jsx("span",{className:"text-gray-400",children:"--"})}},{header:"Actions",accessor:"actions",renderCustomCell:s=>e.jsx(v,{onClick:()=>R(s),className:"flex flex-row items-center gap-2 rounded-md border px-2 py-1.5 text-sm hover:bg-gray-50",children:e.jsx("span",{children:"Edit fees"})})}],B=N.reduce((s,t)=>(t.renderCustomCell&&(s[t.accessor]=t.renderCustomCell),s),{});return e.jsx("div",{className:"min-h-screen bg-gray-50",children:e.jsxs("div",{className:"mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Club Fee Settings"}),e.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"Manage fee structures for each club"})]})}),e.jsx("div",{className:"mt-6",children:e.jsxs("div",{className:"relative max-w-md",children:[e.jsx("input",{type:"text",placeholder:"Search by club name...",className:"w-full rounded-lg border border-gray-300 py-2 pl-10 pr-4 text-sm text-gray-700 placeholder:text-gray-500 focus:border-[#176448] focus:outline-none focus:ring-1 focus:ring-[#176448]",onChange:s=>{const t=s.target.value.trim();t?n(1,l,[`name,cs,${t}`]):n(1,l)}}),e.jsx(ee,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"})]})})]}),e.jsx("div",{className:"rounded-xl shadow-sm",children:e.jsx(te,{columns:N,data:P,loading:F,renderCustomCell:B,emptyMessage:"No clubs found",loadingMessage:"Loading clubs..."})}),e.jsx("div",{className:"mt-4",children:e.jsx(Z,{currentPage:E,pageCount:k,pageSize:l,canPreviousPage:D,canNextPage:O,updatePageSize:s=>{g(s),n(1,s)},previousPage:V,nextPage:G,gotoPage:s=>n(s,l)})}),e.jsx("div",{className:`fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50 ${!C&&"hidden"}`,children:e.jsxs("div",{className:"w-full max-w-md rounded-2xl bg-white p-6",children:[e.jsxs("h2",{className:"mb-4 text-xl font-semibold",children:["Edit Fee Settings for ",a==null?void 0:a.name]}),e.jsxs("form",{onSubmit:z,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:"Fee Type"}),e.jsxs("select",{name:"feeType",defaultValue:a!=null&&a.feeSettings?JSON.parse(a.feeSettings).feeType:"tech",className:"w-full rounded-md border border-gray-300 px-3 py-2",onChange:s=>{const t=s.target.form,o=t.querySelector(".monthly-fields"),i=t.querySelector(".tech-fields");s.target.value==="monthly"?(o.classList.remove("hidden"),i.classList.add("hidden")):(o.classList.add("hidden"),i.classList.remove("hidden"))},children:[e.jsx("option",{value:"tech",children:"Technology Fee"}),e.jsx("option",{value:"monthly",children:"Monthly Fee"})]})]}),e.jsxs("div",{className:"monthly-fields hidden",children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:"Monthly Fee Amount ($)"}),e.jsx("input",{type:"number",name:"monthlyFeeAmount",defaultValue:a!=null&&a.feeSettings?JSON.parse(a.feeSettings).monthlyFeeAmount:"",step:"0.01",min:"0",className:"w-full rounded-md border border-gray-300 px-3 py-2"})]}),e.jsxs("div",{className:"tech-fields",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:"Percentage Fee (%)"}),e.jsx("input",{type:"number",name:"techFeePercentage",defaultValue:a!=null&&a.feeSettings?JSON.parse(a.feeSettings).techFeePercentage:"1",step:"0.01",min:"0",className:"w-full rounded-md border border-gray-300 px-3 py-2"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:"Fixed Fee ($)"}),e.jsx("input",{type:"number",name:"techFeeFixed",defaultValue:a!=null&&a.feeSettings?JSON.parse(a.feeSettings).techFeeFixed:"0.49",step:"0.01",min:"0",className:"w-full rounded-md border border-gray-300 px-3 py-2"})]})]}),e.jsxs("div",{className:"mt-6 flex justify-end space-x-3",children:[e.jsx("button",{type:"button",onClick:()=>d(!1),className:"rounded-xl border border-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-50",children:"Cancel"}),e.jsx(v,{type:"submit",loading:T,className:"rounded-xl bg-[#176448] px-4 py-2 text-white hover:bg-[#176448]/80",children:"Save Changes"})]})]})]})})]})})},Ve=U(se,"fee_settings","You don't have permission to access fee settings");export{Ve as default};

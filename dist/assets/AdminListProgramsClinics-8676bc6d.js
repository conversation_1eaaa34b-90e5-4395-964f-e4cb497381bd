import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as i}from"./vendor-851db8c1.js";import{w as S,M as y,e as j}from"./index-f915b394.js";import{S as v}from"./react-select-c8303602.js";import{L as w}from"./ListClinics-83392fdb.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./yup-54691517.js";import"./yup-2824f222.js";import"./@hookform/resolvers-67648cca.js";import"./index-eb1bc208.js";import"./Skeleton-1e8bf077.js";import"./react-loading-skeleton-3d87d1f5.js";import"./HistoryComponent-a235a60c.js";import"./DataTable-a2248415.js";let m=new y;function C(){const[o,c]=i.useState(null),[n,s]=i.useState(!1),[p,d]=i.useState([]),[L,u]=i.useState(null),[f,x]=i.useState([]);async function h(){s(!0);try{m.setTable("clubs");const t=await m.callRestAPI({},"GETALL");d(t.list)}catch(t){console.error("Error fetching data:",t)}finally{s(!1)}}const g=async t=>{s(!0);try{u({id:t.value,name:t.label}),await b(t.value)}catch(a){console.error("Error fetching data:",a)}finally{s(!1)}},b=async t=>{var a,l;s(!0);try{const r=await m.callRawAPI(`/v3/api/custom/courtmatchup/admin/profile/${t}`,{},"GET");c((a=r==null?void 0:r.model)==null?void 0:a.club),x((l=r==null?void 0:r.model)==null?void 0:l.sports)}catch(r){console.log(r)}finally{s(!1)}};return i.useEffect(()=>{h()},[]),e.jsxs("div",{className:"h-full bg-white p-4 sm:p-6 lg:p-8",children:[n&&e.jsx(j,{}),e.jsxs("div",{className:"mb-4 max-w-xl",children:[e.jsx("label",{className:"mb-2 block text-base font-medium text-gray-900",children:"Select club"}),e.jsx(v,{className:"w-full rounded-lg border border-gray-200 p-2.5 text-sm",options:p.map(t=>({value:t.user_id,label:t.name,club_id:t==null?void 0:t.id})),isMulti:!1,onChange:g})]}),o!=null&&o.id?e.jsx(w,{club:o,sports:f}):e.jsx("div",{className:"flex h-[calc(100vh-200px)] items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{className:"mb-2 text-xl font-medium text-gray-900",children:"No Club Selected"}),e.jsx("p",{className:"text-gray-600",children:"Please select a club from the dropdown above to view and manage its details."})]})})]})}const ft=S(C,"clinics","You don't have permission to access clinic management");export{ft as default};

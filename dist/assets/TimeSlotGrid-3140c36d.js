import{j as c}from"./@nivo/heatmap-ba1ecfff.js";import{r as a,b as B}from"./vendor-851db8c1.js";const G=({days:L,isSelected:M,handleTimeSelect:f,handleDeleteTime:N,containerRef:O,renderTimeSlotContent:y,disableTimeSlot:i})=>{const p=a.useRef(null),[l,E]=a.useState(!1),[P,R]=a.useState(null),[X,D]=a.useState(null),[o,h]=a.useState({start:null,current:null}),S=a.useRef(new Map),u=a.useRef(null),g=a.useRef(new Set),T=t=>{const[e,r]=t.split(":"),n=parseInt(e),s=n>=12?"PM":"AM";return`${n%12||12}:${r} ${s}`},w=(()=>{const t=[];for(let e=8;e<=22;e++)for(let r=0;r<60;r+=30){const n=`${e.toString().padStart(2,"0")}:${r.toString().padStart(2,"0")}:00`;t.push({display:T(n),value:n})}return t})(),j=a.useCallback((t,e)=>{if(!t||!e.start||!e.current)return!1;const r=p.current.getBoundingClientRect(),n=t.getBoundingClientRect(),s={left:n.left-r.left,right:n.right-r.left,top:n.top-r.top,bottom:n.bottom-r.top},m=Math.min(e.start.x,e.current.x),v=Math.max(e.start.x,e.current.x),F=Math.min(e.start.y,e.current.y),b=Math.max(e.start.y,e.current.y);return!(s.right<m||s.left>v||s.bottom<F||s.top>b)},[]),C=a.useCallback(t=>{u.current&&cancelAnimationFrame(u.current),u.current=requestAnimationFrame(()=>{const e=new Set;S.current.forEach((r,n)=>{if(j(r,t)){const[s,m]=n.split("|"),v=w.find(b=>b.value===m);(i&&v?i(v,s):!1)||(e.add(n),g.current.has(n)||f(m,s))}}),g.current=e})},[j,f,i,w]),$=(t,e)=>{if(!p.current)return{x:0,y:0};const r=p.current.getBoundingClientRect(),n=window.pageXOffset||document.documentElement.scrollLeft,s=window.pageYOffset||document.documentElement.scrollTop;return{x:t-r.left-n,y:e-r.top-s}},k=(t,e,r)=>{r.preventDefault(),E(!0),R(t),D(e);const n=$(r.clientX,r.clientY);h({start:n,current:n}),g.current.clear()},A=(t,e)=>{M(t,e)?N(t,e):f(t,e)},d=a.useCallback(t=>{if(l){t.preventDefault();const e=$(t.clientX,t.clientY),r={...o,current:e};h(r),C(r)}},[l,o,C]),x=a.useCallback(()=>{E(!1),R(null),D(null),h({start:null,current:null}),g.current.clear(),u.current&&(cancelAnimationFrame(u.current),u.current=null),window.removeEventListener("mousemove",d),window.removeEventListener("mouseup",x)},[d]);B.useEffect(()=>{const t=()=>{l&&x()};if(l)return window.addEventListener("mousemove",d),window.addEventListener("mouseup",t),()=>{window.removeEventListener("mousemove",d),window.removeEventListener("mouseup",t)}},[l,d,x]);const I=(t,e)=>{l&&f(t,e)};return c.jsxs("div",{className:"relative w-full overflow-hidden",ref:p,children:[l&&o.start&&o.current&&c.jsx("div",{style:{position:"absolute",left:Math.min(o.start.x,o.current.x),top:Math.min(o.start.y,o.current.y),width:Math.abs(o.current.x-o.start.x),height:Math.abs(o.current.y-o.start.y),backgroundColor:"rgba(59, 130, 246, 0.2)",border:"2px solid rgb(59, 130, 246)",pointerEvents:"none",zIndex:1e3}}),c.jsx("div",{className:"w-full overflow-x-auto pb-4",children:c.jsx("div",{className:"grid min-w-[900px] grid-cols-7 gap-5",children:L.map(t=>c.jsxs("div",{className:"rounded-md bg-white p-2 text-center",children:[c.jsx("div",{className:"mb-2 rounded-md bg-[#F6F8FA] px-3 py-2 font-medium",children:t}),c.jsx("div",{className:"space-y-2",children:w.map(e=>{const r=M(e.value,t),n=i?i(e,t):!1;return c.jsxs("div",{className:"relative",onMouseEnter:()=>!n&&I(e.value,t),children:[c.jsx("button",{ref:s=>{s&&S.current.set(`${t}|${e.value}`,s)},className:`w-full rounded-md border-2 border-gray-100 px-3 py-2 text-sm font-medium ${n?"cursor-not-allowed border-gray-200 bg-gray-100 text-gray-400":r?"border-2 border-primaryBlue bg-[#EBF1FF] text-primaryBlue":"border-gray-300 text-gray-500 hover:border-gray-400"}`,disabled:n,onMouseDown:s=>!n&&k(e.value,t,s),onClick:s=>{!l&&!n&&A(e.value,t)},children:e.display}),y&&y(e,t)]},`${t}-${e.value}`)})})]},t))})})]})},q=G;export{q as T};

import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as r,f as oe,j as ve,r as y}from"./vendor-851db8c1.js";import{M as ae,A as ce,G as de,b as R,t as re,B as we,c as Ne,g as O}from"./index-f915b394.js";import{o as me}from"./yup-2824f222.js";import{u as ue}from"./react-hook-form-687afde5.js";import{c as pe,a as d,b as Se}from"./yup-54691517.js";import{P as Ce}from"./index-eb1bc208.js";import{A as ke}from"./AddButton-df0c3574.js";import{A as Pe,a as _e}from"./index.esm-9c6194ba.js";import{a as Ee}from"./index.esm-c561e951.js";import{S as Fe}from"./index-02625b16.js";import{M as he}from"./index-be4468eb.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@hookform/resolvers-67648cca.js";import"./AddButton.module-98aac587.js";let ge=new ae;const Ae=({activeId:x,setSidebar:S})=>{var T,n;const B=pe({name:d().required(),status:Se().required()}).required(),{dispatch:C}=r.useContext(ce),{dispatch:u}=r.useContext(de);oe(),ve();const[k,w]=y.useState(0),[P,_]=y.useState(!1),{register:b,handleSubmit:h,setError:m,setValue:N,formState:{errors:E}}=ue({resolver:me(B)}),F=[{key:"0",value:"Inactive"},{key:"1",value:"Active"}],A=async o=>{_(!0);try{const i=await ge.updateStripePrice(x,o);if(!i.error)R(u,"Edited",4e3);else if(i.validation){const j=Object.keys(i.validation);for(let g=0;g<j.length;g++){const f=j[g];m(f,{type:"manual",message:i.validation[f]})}}}catch(i){console.log("Error",i),R(u,i.message,4e3),re(C,i.message)}_(!1)};async function H(){try{const o=await ge.getStripePrice(x);if(!o.error){const i=o.model.object;N("name",i.nickname),N("status",o.model.status),w(o.model.id)}}catch(o){console.log("Error",o),re(C,o.message)}}return r.useEffect(()=>{u({type:"SETPATH",payload:{path:"prices"}}),H()},[x]),e.jsxs("div",{className:"rounded   mx-auto",children:[e.jsxs("div",{className:"flex items-center p-3 gap-4 border-b border-b-[#E0E0E0] justify-between",children:[e.jsx("div",{className:"flex items-center gap-3",children:e.jsx("span",{className:"text-lg font-semibold",children:"Edit Plan"})}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"flex items-center py-2 px-3 border border-[#C6C6C6] rounded-md shadow-sm hover:bg-[#F4F4F4]",onClick:()=>S(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center py-2 px-3 text-white bg-[#4F46E5] rounded-md shadow-sm",onClick:async()=>{await h(A)(),S(!1)},disabled:P,children:P?"Saving...":"Save"})]})]}),e.jsxs("form",{className:"w-full text-left max-w-lg p-4",onSubmit:h(A),children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"name",children:"Name"}),e.jsx("input",{type:"text",...b("name"),className:`"appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(T=E.name)!=null&&T.message?"border-red-500":""}`}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(n=E.name)==null?void 0:n.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Status"}),e.jsx("select",{className:"appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",...b("status"),children:F.map(o=>e.jsx("option",{value:o.key,children:o.value},o.key))})]})]})]})},Te=({setSidebar:x})=>{var f,Z,J,V,Q,X,U,$,L,z,q,G,ee,D;const[S,B]=y.useState("one_time"),[C,u]=y.useState([]),[k,w]=y.useState(!1),P=pe({product_id:d().required(),name:d().required(),amount:d().required(),type:d().required(),interval:d().when("type",{is:"recurring",then:s=>s.required(),otherwise:s=>s.notRequired()}),interval_count:d(),usage_type:d().when("type",{is:"recurring",then:s=>s.required(),otherwise:s=>s.notRequired()}),usage_limit:d(),trial_days:d()}).required(),{dispatch:_}=r.useContext(ce),{dispatch:b}=r.useContext(de),h=oe(),{register:m,handleSubmit:N,setError:E,setValue:F,trigger:A,resetField:H,getValues:T,formState:{errors:n}}=ue({resolver:me(P)}),o=[{key:0,value:"",display:"Nothing Selected"},{key:1,value:"one_time",display:"One Time"},{key:2,value:"recurring",display:"Recurring"}],i=[{key:0,value:"",display:"Nothing Selected"},{key:1,value:"licenced",display:"Upfront"},{key:2,value:"metered",display:"Metered"}],j=[{key:0,value:"",display:"Nothing Selected"},{key:1,value:"day",display:"Day"},{key:2,value:"week",display:"Week"},{key:3,value:"month",display:"Month"},{key:4,value:"year",display:"Year"},{key:5,value:"lifetime",display:"Lifetime"}],g=async s=>{let v=new ae;console.log(s),w(!0);try{const p=await v.addStripePrice(s);if(!p.error)R(b,"Price Added"),h("/admin/prices");else if(p.validation){const ie=Object.keys(p.validation);for(let K=0;K<ie.length;K++){const I=ie[K];console.log(I),E(I,{type:"manual",message:p.validation[I]})}}}catch(p){console.log("Error",p),R(b,p.message),re(_,p.message)}w(!1)};return r.useEffect(()=>{b({type:"SETPATH",payload:{path:"prices"}}),(async()=>{let s=new ae;const{list:v,error:p}=await s.getStripeProducts({limit:"all"});if(p){R(_,"Something went wrong while fetching products list");return}u(v)})()},[]),e.jsxs("div",{className:" mx-auto  rounded",children:[e.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:["(/* ",e.jsx("svg",{onClick:()=>x(!1),xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M14.3322 5.83203L19.8751 11.3749C20.2656 11.7654 20.2656 12.3986 19.8751 12.7891L14.3322 18.332M19.3322 12.082H3.83218",stroke:"#A8A8A8","stroke-width":"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})," */)",e.jsx("span",{className:"text-lg font-semibold",children:"Add Price"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 shadow-sm hover:bg-[#f4f4f4]",onClick:()=>x(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center rounded-md bg-[#4F46E5] px-3 py-2 text-white shadow-sm",onClick:async()=>{await N(g)(),x(!1)},disabled:k,children:k?"Saving...":"Save"})]})]}),e.jsxs("form",{className:" w-full p-4 text-left",onSubmit:N(g),children:[e.jsxs("div",{className:"mb-4 ",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"product_id",children:"Product"}),e.jsxs("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...m("product_id"),children:[e.jsx("option",{value:"",children:"Nothing selected"},"prod_default"),C.map((s,v)=>e.jsx("option",{value:s.id,children:s.name},`prod_${s.id}`))]}),e.jsx("p",{className:"text-xs italic text-red-500",children:(f=n.product_id)==null?void 0:f.message})]}),e.jsxs("div",{className:"mb-4 ",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"name",children:"Name"}),e.jsx("input",{type:"text",placeholder:"Name",...m("name"),className:`"shadow focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 focus:outline-none ${(Z=n.name)!=null&&Z.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(J=n.name)==null?void 0:J.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"amount",children:"Amount"}),e.jsx("input",{type:"number",min:.1,step:"any",placeholder:"Amount",...m("amount"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(V=n.amount)!=null&&V.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(Q=n.amount)==null?void 0:Q.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"type",children:"Type"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...m("type"),onChange:s=>{const v=s.target.value;B(v),F("type",v),A("type")},children:o.map(s=>e.jsx("option",{value:s.value.toLowerCase(),children:s.display},`interval_${s.key}`))}),e.jsx("p",{className:"text-xs italic text-red-500",children:(X=n.type)==null?void 0:X.message})]}),S==="recurring"?e.jsxs("div",{className:"ml-6",children:[e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"interval",children:"Interval"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...m("interval"),placeholder:"Select",children:j.map(s=>e.jsx("option",{value:s.value.toLowerCase(),children:s.display},`interval_${s.key}`))}),e.jsx("p",{className:"text-xs italic text-red-500",children:(U=n.interval)==null?void 0:U.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"interval_count",children:"Interval Count"}),e.jsx("input",{type:"number",step:"1",placeholder:"Interval Count",...m("interval_count"),...F("interval_count",1),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${($=n.interval_count)!=null&&$.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(L=n.interval_count)==null?void 0:L.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"usage_type",children:"Usage Type"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...m("usage_type"),placeholder:"Select",children:i.map(s=>e.jsx("option",{value:s.value.toLowerCase(),children:s.display},`interval_${s.key}`))}),e.jsx("p",{className:"text-xs italic text-red-500",children:(z=n.usage_type)==null?void 0:z.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"trial_days",children:"Trial Days"}),e.jsx("input",{type:"number",step:"1",placeholder:"0",...m("trial_days"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(q=n.trial_days)!=null&&q.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(G=n.trial_days)==null?void 0:G.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"trial_days",children:"Usage Limit"}),e.jsx("input",{type:"number",step:"1",placeholder:"1000",...m("usage_limit"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(ee=n.usage_limit)!=null&&ee.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(D=n.usage_limit)==null?void 0:D.message})]})]}):""]})]})};let $e=new ae;const le=[{header:"Stripe Id",accessor:"stripe_id"},{header:"Product",accessor:"product_name"},{header:"Nickname",accessor:"name"},{header:"Type",accessor:"type",mapping:{one_time:"One Time",recurring:"Recurring",lifetime:"Lifetime"}},{header:"Price",accessor:"amount"},{header:"Trial",accessor:"trial_days"},{header:"Status",accessor:"status",mapping:{0:"Inactive",1:"Active"}},{header:"Action",accessor:""}],Nt=()=>{const{dispatch:x}=r.useContext(de),{dispatch:S}=r.useContext(ce);r.useState("");const[B,C]=r.useState([]),[u,k]=r.useState(10),[w,P]=r.useState(0),[_,b]=r.useState(0),[h,m]=r.useState(0),[N,E]=r.useState(!1),[F,A]=r.useState(!1),[H,T]=r.useState(!1),[n,o]=r.useState(!1),[i,j]=r.useState([]),[g,f]=r.useState([]),[Z,J]=r.useState(""),[V,Q]=r.useState("eq"),[X,U]=r.useState(!1),[$,L]=y.useState(!1),[z,q]=y.useState(!1),[G,ee]=y.useState();oe();const D=r.useRef(null),s=pe({stripe_id:d(),name:d(),status:d(),product_name:d(),amount:d(),type:d()}),{register:v,handleSubmit:p,formState:{errors:ie}}=ue({resolver:me(s)});function K(t){navigator.clipboard.writeText(t),R(x,"Copied to clipboard")}const I=(t,l,a)=>{const c=l==="eq"&&isNaN(a)?`"${a}"`:a,W=`${t},${l},${c}`;f(Y=>[...Y.filter(se=>!se.includes(t)),W]),J(a)};function fe(t){(async function(){k(t),await M(1,t)})()}function ye(){(async function(){await M(h-1>1?h-1:1,u)})()}function be(){(async function(){await M(h+1<=w?h+1:1,u)})()}async function M(t,l,a){U(!0);try{const c=await $e.getStripePrices({page:t,limit:l},`filter=${a.toString()}`),{list:W,total:Y,limit:te,num_pages:se,page:ne}=c;C(W),k(+te),P(+se),m(+ne),b(+Y),E(+ne>1),A(+ne+1<=+se)}catch(c){console.log("ERROR",c),re(S,c.message)}U(!1)}const je=t=>{const l=O(t.stripe_id),a=O(t.product_name),c=O(t.name),W=O(t.amount),Y=O(t.type),te=O(t.status);M(1,u,{stripe_id:l,product_name:a,name:c,amount:W,type:Y,status:te})};r.useEffect(()=>{x({type:"SETPATH",payload:{path:"prices"}});const l=setTimeout(async()=>{await M(1,u,g)},700);return()=>{clearTimeout(l)}},[Z,g,V]),y.useEffect(()=>{$||M(1,u,g)},[$,G,z]);const xe=t=>{D.current&&!D.current.contains(t.target)&&T(!1)};return r.useEffect(()=>(document.addEventListener("mousedown",xe),()=>{document.removeEventListener("mousedown",xe)}),[]),e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex items-center justify-between py-3",children:[e.jsx("form",{className:"relative rounded bg-white",onSubmit:p(je),children:e.jsxs("div",{className:"flex items-center gap-4 text-gray-700 text-nowrap",children:[e.jsxs("div",{className:"relative",ref:D,children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-1",onClick:()=>T(!H),children:[e.jsx(we,{}),e.jsx("span",{children:"Filters"}),i.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white",children:i.length})]}),H&&e.jsx("div",{className:"absolute z-10 mt-4 w-[500px] min-w-[90%] top-fill left-0 filter-form-holder bg-white border border-gray-200 rounded-md shadow-lg",children:e.jsxs("div",{className:"p-4",children:[i==null?void 0:i.map((t,l)=>e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("div",{className:" mb-3 w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",children:t}),e.jsxs("select",{className:"mb-3 w-[40%] rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:a=>{Q(a.target.value)},children:[e.jsx("option",{value:"eq",selected:!0,children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),e.jsx("input",{type:"text",placeholder:"Enter value...",className:" mb-3 w-1/3 rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:a=>I(t,V,a.target.value)}),e.jsx("div",{className:"w-1/12 mt-[-10px]",children:e.jsx(Ee,{className:" cursor-pointer text-xl",onClick:()=>{j(a=>a.filter(c=>c!==t)),f(a=>a.filter(c=>!c.includes(t)))}})})]},l)),e.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out ",onClick:()=>{o(!n)},children:[e.jsx(Pe,{}),"Add filter"]}),n&&e.jsx("div",{className:"absolute top-11 z-10 bg-white px-5 py-3 text-gray-600 shadow-md",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:le.slice(0,-1).map(t=>e.jsx("li",{className:`${i.includes(t.header)?"cursor-not-allowed text-gray-400":"cursor-pointer"}`,onClick:()=>{i.includes(t.header)||j(l=>[...l,t.header]),o(!1)},children:t.header},t.header))})}),i.length>0&&e.jsx("div",{onClick:()=>{j([]),f([])},className:"inline-block cursor-pointer  rounded px-6  py-2.5 font-medium leading-tight text-gray-600  transition duration-150 ease-in-out",children:"Clear all filter"})]})]})})]}),e.jsxs("div",{className:" flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-2 py-1 focus-within:border-gray-400",children:[e.jsx(Ne,{className:"text-xl text-gray-200"}),e.jsx("input",{type:"text",placeholder:"search...",className:"border-none p-0 placeholder:text-left  focus:outline-none",style:{boxShadow:"0 0 transparent"},onInput:t=>{var l;return I("name","cs",(l=t.target)==null?void 0:l.value)}}),e.jsx(_e,{className:"text-lg text-gray-200"})]})]})}),e.jsx(ke,{onClick:()=>q(!0)})]}),X?e.jsx(Fe,{}):e.jsx("div",{className:"overflow-x-auto border-b border-gray-200 shadow ",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200 rounded-lg",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx("tr",{children:le.map((t,l)=>e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[t.header,e.jsx("span",{children:t.isSorted?t.isSortedDesc?" ▼":" ▲":""})]},l))})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:B.map((t,l)=>e.jsx("tr",{children:le.map((a,c)=>a.accessor==""?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsxs("button",{className:"mx-1 inline-block cursor-pointer p-1 text-sm font-medium text-[#4F46E5] transition duration-150 ease-in-out hover:underline",onClick:()=>{ee(t.id),L(!0)},children:[" ","Edit"]})},c):a.accessor==="stripe_id"?e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"w-full flex items-center justify-between",children:[e.jsxs("span",{children:[t[a.accessor]," "]}),e.jsx("span",{className:"cursor-pointer",onClick:()=>K(t[a.accessor]),children:e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:[e.jsx("g",{clipPath:"url(#clip0_948_1315)",children:e.jsx("path",{d:"M6.66663 6.6665V4.33317C6.66663 3.39975 6.66663 2.93304 6.84828 2.57652C7.00807 2.26292 7.26304 2.00795 7.57664 1.84816C7.93316 1.6665 8.39987 1.6665 9.33329 1.6665H15.6666C16.6 1.6665 17.0668 1.6665 17.4233 1.84816C17.7369 2.00795 17.9918 2.26292 18.1516 2.57652C18.3333 2.93304 18.3333 3.39975 18.3333 4.33317V10.6665C18.3333 11.5999 18.3333 12.0666 18.1516 12.4232C17.9918 12.7368 17.7369 12.9917 17.4233 13.1515C17.0668 13.3332 16.6 13.3332 15.6666 13.3332H13.3333M4.33329 18.3332H10.6666C11.6 18.3332 12.0668 18.3332 12.4233 18.1515C12.7369 17.9917 12.9918 17.7368 13.1516 17.4232C13.3333 17.0666 13.3333 16.5999 13.3333 15.6665V9.33317C13.3333 8.39975 13.3333 7.93304 13.1516 7.57652C12.9918 7.26292 12.7369 7.00795 12.4233 6.84816C12.0668 6.6665 11.6 6.6665 10.6666 6.6665H4.33329C3.39987 6.6665 2.93316 6.6665 2.57664 6.84816C2.26304 7.00795 2.00807 7.26292 1.84828 7.57652C1.66663 7.93304 1.66663 8.39975 1.66663 9.33317V15.6665C1.66663 16.5999 1.66663 17.0666 1.84828 17.4232C2.00807 17.7368 2.26304 17.9917 2.57664 18.1515C2.93316 18.3332 3.39987 18.3332 4.33329 18.3332Z",stroke:"#8D8D8D","stroke-width":"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("defs",{children:e.jsx("clipPath",{id:"clip0_948_1315",children:e.jsx("rect",{width:"20",height:"20",fill:"white"})})})]})})]})},c):a.mapping&&a.accessor==="status"?e.jsx("td",{className:"px-6 py-5 whitespace-nowrap inline-block text-sm",children:t[a.accessor]===1?e.jsx("span",{className:"rounded-md bg-[#D1FAE5] px-3 py-1 text-[#065F46]",children:a.mapping[t[a.accessor]]}):e.jsx("span",{className:"rounded-md bg-[#F4F4F4] px-3 py-1 text-[#393939]",children:a.mapping[t[a.accessor]]})},c):a.mapping?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:a.mapping[t[a.accessor]]},c):e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t[a.accessor]},c))},l))})]})}),e.jsx(Ce,{currentPage:h,pageCount:w,pageSize:u,canPreviousPage:N,canNextPage:F,updatePageSize:fe,previousPage:ye,nextPage:be}),e.jsx(he,{isModalActive:$,closeModalFn:()=>L(!1),children:e.jsx(Ae,{activeId:G,setSidebar:L})}),e.jsx(he,{isModalActive:z,closeModalFn:()=>q(!1),children:e.jsx(Te,{setSidebar:q})})]})};export{Nt as default};

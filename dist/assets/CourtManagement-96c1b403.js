import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as s,b as Ce}from"./vendor-851db8c1.js";import{d as et,M as we,ak as ut,R as $e,G as Be,e as Pe,al as ht,b as R,K as tt,a3 as ze,H as xe,J as oe,E as ye}from"./index-f915b394.js";import{u as ft}from"./react-hook-form-687afde5.js";import{S as Je}from"./react-select-c8303602.js";import{B as Re}from"./BottomDrawer-7b010fbb.js";import{H as xt}from"./HistoryComponent-a235a60c.js";import{P as pt}from"./PencilIcon-35185602.js";import{T as gt}from"./TrashIcon-7d213648.js";import{T as yt}from"./TimeSlotGrid-3140c36d.js";import{b as <PERSON>}from"./@headlessui/react-a5400090.js";function wt({title:i,titleId:r,...l},n){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":r},l),i?s.createElement("title",{id:r},i):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z"}))}const vt=s.forwardRef(wt),jt=vt;function He({isOpen:i,onClose:r,onConfirm:l,eventCount:n,affectedReservations:m=[],isSubmitting:g,type:_="hours"}){if(!i)return null;const T=N=>{if(!N)return"";const[H,z]=N.split(":"),k=parseInt(H),v=k>=12?"PM":"AM";return`${k%12||12}:${z} ${v}`},O=N=>N?new Date(N).toLocaleDateString("en-US",{weekday:"short",year:"numeric",month:"short",day:"numeric"}):"";return e.jsxs("div",{className:"fixed inset-0 z-[99999] flex items-center justify-center",children:[e.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),e.jsxs("div",{className:"relative z-50 w-full max-w-2xl rounded-3xl bg-white p-6",children:[e.jsx("h2",{className:"mb-4 text-xl font-medium",children:"Confirm Changes"}),e.jsx("p",{className:"mb-4 text-gray-600",children:`Changing ${_==="court"?"this court":`these ${_}`} will delete ${n} existing reservation${n!==1?"s":""}.`}),m.length>0&&e.jsxs("div",{className:"mb-6 max-h-64 overflow-y-auto rounded-lg border border-gray-200",children:[e.jsx("div",{className:"bg-gray-50 px-4 py-2",children:e.jsx("h3",{className:"text-sm font-medium text-gray-700",children:"Affected Reservations:"})}),e.jsx("div",{className:"divide-y divide-gray-200",children:m.map((N,H)=>e.jsx("div",{className:"px-4 py-3 text-sm",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-gray-900",children:N.sport_name}),e.jsx("p",{className:"text-gray-600",children:N.first_name&&N.last_name?`${N.first_name} ${N.last_name}`:N.email})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("p",{className:"text-gray-900",children:O(N.date)}),e.jsxs("p",{className:"text-gray-600",children:[T(N.start_time)," -"," ",T(N.end_time)]})]})]})},H))})]}),e.jsx("p",{className:"mb-6 text-gray-600",children:"Are you sure you want to continue?"}),e.jsxs("div",{className:"flex justify-end gap-3",children:[e.jsx("button",{onClick:r,className:"rounded-lg border border-gray-200 px-6 py-2",disabled:g,children:"Cancel"}),e.jsx(et,{onClick:l,className:"rounded-lg bg-primaryBlue px-6 py-2 text-white",loading:g,children:"Confirm"})]})]})]})}const st=Ce.forwardRef(({onSubmit:i=()=>{},initialData:r={},club:l={},isOpen:n=!1,onClose:m=()=>{},title:g="Edit club settings",onPrimaryAction:_=()=>{},submitting:T=!1},O)=>{const N=new we,{handleSubmit:H}=ft(),z=l!=null&&l.times?JSON.parse(l.times):[],[k,v]=s.useState(()=>z.length>0?(console.log("Using parsed times:",z),z):[{from:"",until:""}]),[V,U]=s.useState(!1),[Y,h]=s.useState(0),[f,u]=s.useState([]),[A,F]=s.useState(!1),[S,K]=s.useState(!1),[o,b]=s.useState(null);Ce.useEffect(()=>{console.log("Current time slots:",k)},[k]);const j=(d,C,t)=>{v(a=>{const x=[...a];return x[d]={...x[d],[C]:t?t.value:""},x})},p=async d=>{try{K(!0);const C=await N.callRawAPI("/v3/api/custom/courtmatchup/club/courts/affected-reservations",{times:d.times,days_off:d.days_off},"POST");return C&&!C.error&&C.total_affected>0?(h(C.total_affected),u(C.affected_reservations||[]),b(d),U(!0),!0):!1}catch(C){return console.error("Error checking for affected events:",C),!1}finally{K(!1)}},L=async()=>{F(!0);try{const d={...o,delete_affected_events:!0};await i(d),U(!1),b(null)}catch(d){console.error("Error saving changes:",d)}finally{F(!1)}},W=()=>{U(!1),b(null),h(0),u([])};Ce.useImperativeHandle(O,()=>({submit:async()=>new Promise(d=>{H(async C=>{const t=Object.entries($.daysOff).filter(([M,Q])=>Q).map(([M])=>M),a=$.dailyBreaks?[{start:$.breakStartTime,end:$.breakEndTime}]:[],x={times:k,daily_breaks:a,days_off:t};await p(x)||await i(x),d(x)})()})}));const[$,Z]=s.useState({times:k,daysOff:{Monday:!1,Tuesday:!1,Wednesday:!1,Thursday:!1,Friday:!1,Saturday:!1,Sunday:!1,...l!=null&&l.days_off?JSON.parse(l.days_off).reduce((d,C)=>({...d,[C]:!0}),{}):{}},dailyBreaks:!1,breakStartTime:"09:00:00",breakEndTime:"10:00:00",...r});Ce.useEffect(()=>{if(l!=null&&l.daily_breaks)try{const d=JSON.parse(l.daily_breaks);d&&d.length>0&&Z(C=>({...C,dailyBreaks:!0,breakStartTime:d[0].start||C.breakStartTime,breakEndTime:d[0].end||C.breakEndTime}))}catch(d){console.error("Error parsing daily breaks:",d)}},[l]);const J=d=>{Z(C=>({...C,daysOff:{...C.daysOff,[d]:!C.daysOff[d]}}))},I=ut().map(d=>({...d,value:d.value+":00"}));console.log("Time options:",I);const X=()=>{v(d=>[...d,{from:"",until:""}])},te=d=>{v(C=>C.filter((t,a)=>a!==d))};return e.jsxs(e.Fragment,{children:[e.jsx($e,{isOpen:n,onClose:m,title:g,onPrimaryAction:_,submitting:T||S,children:e.jsxs("div",{className:"flex flex-col gap-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"mb-4 text-sm font-medium text-gray-500",children:"General opening hours"}),k.map((d,C)=>e.jsxs("div",{className:"mb-5 flex items-center gap-4",children:[e.jsxs("div",{className:"flex flex-1 gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsx(Je,{classNamePrefix:"select",options:I,components:{DropdownIndicator:()=>null,IndicatorSeparator:()=>null},value:(()=>{const t=I.find(a=>a.value===d.from);return console.log(`Finding option for from=${d.from}:`,t),t||null})(),onChange:t=>j(C,"from",t),placeholder:"Select time",isClearable:!0})}),e.jsx("div",{className:"flex-1",children:e.jsx(Je,{classNamePrefix:"select",components:{DropdownIndicator:()=>null,IndicatorSeparator:()=>null},options:d.from?I.filter(t=>t.value>d.from):I,value:(()=>{const t=I.find(a=>a.value===d.until);return console.log(`Finding option for until=${d.until}:`,t),t||null})(),onChange:t=>j(C,"until",t),placeholder:"Select time",isDisabled:!d.from,isClearable:!0})})]}),k.length>1&&e.jsx("button",{onClick:()=>te(C),className:" text-red-500 hover:text-red-700",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M4.79167 4.79102V16.8743C4.79167 17.3346 5.16476 17.7077 5.625 17.7077H14.375C14.8352 17.7077 15.2083 17.3346 15.2083 16.8743V4.79102M4.79167 4.79102H15.2083M4.79167 4.79102H3.125M15.2083 4.79102H16.875M11.6667 8.95768V13.541M8.33333 8.95768V13.541M7.5 4.79102C7.5 3.4103 8.61929 2.29102 10 2.29102C11.3807 2.29102 12.5 3.4103 12.5 4.79102",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})})]},C))]}),e.jsx("button",{onClick:X,className:"mb-5 text-primaryBlue underline hover:text-primaryBlue/80",children:"+Add another time slot"}),e.jsxs("div",{children:[e.jsx("h3",{className:"mb-4 text-sm font-medium text-gray-500",children:"Days off"}),e.jsx("div",{className:"grid grid-cols-2 gap-4",children:Object.keys($.daysOff).map(d=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:$.daysOff[d],onChange:()=>J(d),className:"h-4 w-4 rounded border-gray-300"}),e.jsx("span",{className:"text-sm text-gray-600",children:d})]},d))})]})]})}),e.jsx(He,{isOpen:V,onClose:W,onConfirm:L,eventCount:Y,affectedReservations:f,isSubmitting:A,type:"hours"})]})});st.displayName="ClubSettingsEditForm";const bt=st;function Nt({initialData:i={},onSubmit:r,setExceptionName:l,exceptionName:n}){return e.jsx("div",{className:"flex flex-col gap-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"mb-4 text-sm font-medium text-gray-500",children:"Exception details"}),e.jsxs("div",{className:"mt-1",children:[e.jsx("label",{className:"mb-2 block text-sm text-gray-600",children:"Name"}),e.jsx("input",{type:"text",value:n,onChange:m=>l(m.target.value),placeholder:"Enter exception name",className:"w-full rounded-xl border border-gray-200 px-3 py-2 text-sm shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue"})]})]})})}const Ct=new we;function Ge({onSubmit:i,initialData:r,onClose:l,isOpen:n,isEdit:m,club:g,sports:_=[],courts:T,title:O,primaryButtonText:N,submitting:H,showFooter:z=!1,onPrimaryAction:k}){var Q,ne,Se;const[v,V]=s.useState({name:(r==null?void 0:r.name)||"",sport_id:Number(r==null?void 0:r.sport_id)||null,type:(r==null?void 0:r.type)||"",sub_type:(r==null?void 0:r.sub_type)||null}),[U,Y]=s.useState(!1),[h,f]=s.useState(!1),[u,A]=s.useState(!1),[F,S]=s.useState(null),[K,o]=s.useState(null),[b,j]=s.useState(!1),[p,L]=s.useState(0),[W,$]=s.useState(null),{dispatch:Z}=s.useContext(Be),J=()=>{V({name:"",sport_id:null,type:"",sub_type:null})};s.useEffect(()=>{r?V({name:r.name||"",sport_id:Number(r.sport_id)||null,type:r.type||"",sub_type:r.sub_type||null}):m||J()},[r,m]);const I=s.useCallback(y=>{const{name:G,value:le}=y.target;V(ce=>{const ue=G==="sport_id"?Number(le):le;if(ce[G]===ue)return ce;const ae={...ce};return ae[G]=ue,G==="sport_id"&&(ae.type="",ae.sub_type=null),G==="type"&&(ae.sub_type=null),ae})},[]),X=async()=>{A(!1),f(!0);try{const y={court_id:r.id,...v,sport_id:F,type:"",sub_type:null,sport_change_option:K};if(await d(y)){f(!1);return}await i(y),m||J()}catch(y){console.error(y)}finally{f(!1)}},te=()=>{S(null),o(null),A(!1)},d=async y=>{try{await Ct.callRawAPI("/v3/api/custom/courtmatchup/club/check-affected-events",{court_id:y.court_id,sport_id:y.sport_id,type:y.type,sub_type:y.sub_type},"POST");const G={affected_events_count:Math.floor(Math.random()*5)};return G.affected_events_count>0?(L(G.affected_events_count),$(y),j(!0),!0):!1}catch(G){return console.error("Error checking for affected events:",G),!1}},C=async()=>{f(!0);try{await i(W),j(!1),m||J()}catch(y){console.error("Error saving changes:",y)}finally{f(!1)}},t=async y=>{if(y.preventDefault(),x.length>0&&!v.type){R(Z,"Please select a court type",3e3,"error");return}if(D.length>0&&!v.sub_type){R(Z,"Please select a surface type",3e3,"error");return}if(m&&(r!=null&&r.sport_id)&&v.sport_id!==r.sport_id){S(v.sport_id),A(!0);return}f(!0);try{const G=m?{court_id:r.id,...v}:v;if(m&&await d(G)){f(!1);return}await i(G),m||J()}catch(G){console.error(G)}finally{f(!1)}},a=_.find(y=>y.id===v.sport_id),x=((Q=a==null?void 0:a.sport_types)==null?void 0:Q.filter(y=>y.type))||[],D=((Se=(ne=a==null?void 0:a.sport_types)==null?void 0:ne.find(y=>y.type===v.type))==null?void 0:Se.subtype)||[],M=()=>{var pe,_e;const[y,G]=s.useState(""),le=de=>{G(de.target.value)},ce=()=>{o(Number(y)),X()},ue=((pe=_.find(de=>de.id===(r==null?void 0:r.sport_id)))==null?void 0:pe.name)||"previous sport",ae=((_e=_.find(de=>de.id===F))==null?void 0:_e.name)||"new sport";return e.jsxs("div",{className:"fixed inset-0 z-[9999] flex items-center justify-center",children:[e.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),e.jsxs("div",{className:"relative z-50 w-full max-w-md rounded-lg bg-white p-6",children:[e.jsx("h3",{className:"mb-4 text-lg font-medium",children:"Change Sport"}),e.jsxs("p",{className:"mb-4 text-gray-600",children:["You are changing the sport for this space from"," ",e.jsx("strong",{children:ue})," to ",e.jsx("strong",{children:ae}),". However, any events for this space will still be listed under the previous sport, including in the daily scheduler."]}),e.jsx("p",{className:"mb-4 text-gray-600",children:"Would you like to:"}),e.jsxs("div",{className:"mb-6 space-y-3",children:[e.jsxs("label",{className:"flex items-start gap-2",children:[e.jsx("input",{type:"radio",name:"sportChangeOption",value:"1",checked:y==="1",onChange:le,className:"mt-1 h-4 w-4"}),e.jsxs("span",{children:["A: Keep these events (events will remain under ",ue,")"]})]}),e.jsxs("label",{className:"flex items-start gap-2",children:[e.jsx("input",{type:"radio",name:"sportChangeOption",value:"2",checked:y==="2",onChange:le,className:"mt-1 h-4 w-4"}),e.jsx("span",{children:"B: Delete these events"})]}),e.jsxs("label",{className:"flex items-start gap-2",children:[e.jsx("input",{type:"radio",name:"sportChangeOption",value:"3",checked:y==="3",onChange:le,className:"mt-1 h-4 w-4"}),e.jsxs("span",{children:["C: Change the sport listed for these events to ",ae]})]})]}),e.jsxs("div",{className:"flex justify-end gap-3",children:[e.jsx("button",{onClick:te,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{onClick:ce,disabled:!y,className:`rounded-lg px-4 py-2 text-white ${y?"bg-primaryBlue hover:bg-blue-700":"cursor-not-allowed bg-gray-400"}`,children:"Confirm"})]})]})]})};return e.jsxs("div",{children:[U&&e.jsx(Pe,{}),e.jsx($e,{isOpen:n!==void 0?n:!0,onClose:l,title:O||(m?"Edit court":"Add new court"),showFooter:z,primaryButtonText:N||(m?"Save changes":"Add court"),onPrimaryAction:k,submitting:H||h,children:e.jsx(ht,{isLoading:U,children:e.jsxs("form",{onSubmit:t,className:"flex h-full flex-col",children:[e.jsxs("div",{className:"h-full flex-1 space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Court Name"}),e.jsx("input",{type:"text",name:"name",value:v.name,onChange:I,className:"mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Enter court name",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Sport"}),e.jsx("div",{className:"mt-2 space-x-4",children:_.filter(y=>y.status===1).map(y=>e.jsxs("label",{className:"inline-flex items-center",children:[e.jsx("input",{type:"radio",name:"sport_id",value:y.id,checked:v.sport_id===y.id,onChange:I,className:"form-radio"}),e.jsx("span",{className:"ml-2",children:y.name})]},y.id))})]}),x.length>0&&e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700",children:["Type ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("div",{className:"mt-2 space-x-4",children:x.map(y=>e.jsxs("label",{className:"inline-flex items-center",children:[e.jsx("input",{type:"radio",name:"type",value:y.type,checked:v.type===y.type,onChange:I,className:"form-radio",required:!0}),e.jsx("span",{className:"ml-2",children:y.type})]},y.type))})]}),v.type&&D.length>0&&e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700",children:["Sub Type ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("div",{className:"mt-2 space-x-4",children:D.map(y=>e.jsxs("label",{className:"inline-flex items-center",children:[e.jsx("input",{type:"radio",name:"sub_type",value:y,checked:v.sub_type===y,onChange:I,className:"form-radio",required:!0}),e.jsx("span",{className:"ml-2",children:y})]},y))})]})]}),e.jsxs("div",{className:"flex flex-shrink-0 justify-end gap-4 border-t border-gray-200 px-4 py-4",children:[e.jsx("button",{type:"button",className:"flex-1 rounded-xl border border-gray-200 px-3 py-2 text-sm font-semibold text-gray-900 hover:bg-gray-50",onClick:l,children:"Cancel"}),e.jsx(et,{type:"submit",loading:h,className:"flex-1 rounded-xl bg-primaryBlue px-3 py-2 text-sm font-semibold text-white hover:bg-blue-700",children:m?"Save changes":"Add court"})]})]})})}),u&&e.jsx(M,{}),e.jsx(He,{isOpen:b,onClose:()=>j(!1),onConfirm:C,eventCount:p,isSubmitting:h,type:"court"})]})}function Ye({showTimesAvailableModal:i,setShowTimesAvailableModal:r,selectedCourt:l,setSelectedTimes:n,isSubmitting:m,selectedTimes:g,onSave:_,minTime:T=0,maxTime:O=23,allowMultipleSlots:N=!0,title:H="Times available"}){const[z,k]=s.useState(null),[v,V]=s.useState(!1),[U,Y]=s.useState(null),[h,f]=s.useState(null),[u,A]=s.useState({start:null,current:null}),F=s.useRef(new Map),S=s.useRef(null),K=s.useRef(new Set),o=["monday","tuesday","wednesday","thursday","friday","saturday","sunday"],j=(()=>{const t=[];for(let a=0;a<=23;a++)t.push(`${a.toString().padStart(2,"0")}:00:00`),t.push(`${a.toString().padStart(2,"0")}:30:00`);return t})(),p=(t,a)=>{n(x=>x.some(M=>M.time===t&&M.day===a)?x.filter(M=>!(M.time===t&&M.day===a)):[...x,{time:t,day:a}])},L=(t,a)=>{k(null)},W=(t,a)=>{n(x=>x.filter(D=>!(D.time===t&&D.day===a))),k(null)},$=()=>{const t={};return g.forEach(a=>{const x=a.day.toLowerCase();t[x]||(t[x]=[]),t[x].push(a.time)}),Object.entries(t).map(([a,x])=>({day:a,timeslots:[...new Set(x)].sort()}))},Z=t=>{const[a,x]=t.split(":"),D=parseInt(a),M=D>=12?"PM":"AM";let Q=D%12;return Q===0&&(Q=12),`${Q}:${x} ${M}`},J=s.useCallback((t,a)=>{if(!t||!a.start||!a.current)return!1;const x=t.getBoundingClientRect(),D=Math.min(a.start.x,a.current.x),M=Math.max(a.start.x,a.current.x),Q=Math.min(a.start.y,a.current.y),ne=Math.max(a.start.y,a.current.y);return!(x.right<D||x.left>M||x.bottom<Q||x.top>ne)},[]),I=s.useCallback(t=>{S.current&&cancelAnimationFrame(S.current),S.current=requestAnimationFrame(()=>{F.current.forEach((a,x)=>{if(J(a,t)){const[D,M]=x.split("|");g.some(ne=>ne.time===M&&ne.day===D)||p(M,D)}})})},[J,g]),X=(t,a,x)=>{V(!0),Y(t),f(a);const D={start:{x:x.clientX,y:x.clientY},current:{x:x.clientX,y:x.clientY}};A(D),K.current.clear(),p(t,a)},te=s.useCallback(t=>{if(v){t.preventDefault();const a={...u,current:{x:t.clientX,y:t.clientY}};A(a),I(a)}},[v,u,I]),d=s.useCallback(()=>{V(!1),Y(null),f(null),A({start:null,current:null}),K.current.clear(),S.current&&cancelAnimationFrame(S.current)},[]),C=(t,a)=>{v&&(g.some(D=>D.time===t&&D.day===a)||p(t,a))};return s.useEffect(()=>{if(v)return window.addEventListener("mousemove",te),window.addEventListener("mouseup",d),()=>{window.removeEventListener("mousemove",te),window.removeEventListener("mouseup",d)}},[v,te,d]),e.jsx(Re,{isOpen:i,onClose:()=>r(!1),title:H,onDiscard:()=>{n([]),r(!1)},discardLabel:"Discard",showActions:!0,isSubmitting:m,saveLabel:"Save changes",leftElement:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{className:"rounded-lg bg-[#F6F8FA] p-2",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M3.33268 12.5L9.41008 6.42259C9.73552 6.09715 10.2632 6.09715 10.5886 6.42259L16.666 12.5",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})}),e.jsx("button",{className:"rounded-lg bg-[#F6F8FA] p-2",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M16.6673 7.5L10.5899 13.5774C10.2645 13.9028 9.73685 13.9028 9.41141 13.5774L3.33398 7.5",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})})]}),e.jsx("p",{className:"text-xl",children:l==null?void 0:l.name})]}),onSave:()=>{const t=$();_==null||_(t),r(!1)},children:e.jsxs("div",{className:"w-full select-none",children:[v&&u.start&&u.current&&e.jsx("div",{style:{position:"fixed",left:Math.min(u.start.x,u.current.x),top:Math.min(u.start.y,u.current.y),width:Math.abs(u.current.x-u.start.x),height:Math.abs(u.current.y-u.start.y),backgroundColor:"rgba(59, 130, 246, 0.2)",border:"2px solid rgb(59, 130, 246)",pointerEvents:"none",zIndex:1e3}}),e.jsx("div",{className:"grid grid-cols-7 gap-5",children:o.map(t=>e.jsxs("div",{className:"rounded-md bg-white p-2 text-center",children:[e.jsx("div",{className:"mb-2 rounded-md bg-[#F6F8FA] px-3 py-2 font-medium capitalize",children:t}),e.jsx("div",{className:"space-y-2",children:j.map(a=>{const x=g.some(M=>M.time===a&&M.day===t),D=`${t}-${a}`;return e.jsxs("div",{className:"relative",children:[e.jsx("button",{ref:M=>{M&&F.current.set(`${t}|${a}`,M)},className:`w-full rounded-md border-2 border-gray-100 px-3 py-2 text-sm font-medium text-gray-500 ${x?"border-2 border-primaryBlue bg-[#EBF1FF] text-primaryBlue":"border-gray-300 hover:border-gray-400"}`,onMouseDown:M=>X(a,t,M),onMouseEnter:()=>C(a,t),onMouseUp:d,onClick:()=>{x?W(a,t):p(a,t)},children:Z(a)}),z===D&&e.jsx("div",{className:"absolute right-0 z-10 mt-1 w-36 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5",children:e.jsxs("div",{className:"py-1",children:[e.jsxs("button",{onClick:()=>L(),className:"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[e.jsx("svg",{className:"mr-3 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"})}),"Edit"]}),e.jsxs("button",{onClick:()=>W(a,t),className:"flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-100",children:[e.jsx("svg",{className:"mr-3 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})}),"Delete"]})]})})]},`${t}-${a}`)})})]},t))})]})})}function St({showExceptionTimeSelector:i,setShowExceptionTimeSelector:r,selectedException:l,setSelectedExceptionTimes:n,selectedExceptionTimes:m,exceptions:g,onSelectException:_,onSave:T,isSubmitting:O}){s.useContext(Be);const N=new we,[H,z]=s.useState(null),[k,v]=s.useState(!1),[V,U]=s.useState(null),[Y,h]=s.useState(null),f=s.useRef(null),[u,A]=s.useState(!1),[F,S]=s.useState((l==null?void 0:l.name)||""),K=s.useRef(null),[o,b]=s.useState(!1),[j,p]=s.useState(0),[L,W]=s.useState([]),[$,Z]=s.useState(!1),[J,I]=s.useState(null);s.useEffect(()=>{S((l==null?void 0:l.name)||"")},[l]),s.useEffect(()=>{u&&K.current&&K.current.focus()},[u]);const[X,te]=s.useState(()=>(g==null?void 0:g.findIndex(c=>c.name===(l==null?void 0:l.name)))||0),d=()=>{if(X>0){const c=X-1;te(c),_(g[c])}},C=()=>{if(X<g.length-1){const c=X+1;te(c),_(g[c])}},t=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],x=(()=>{const c=[];for(let w=8;w<=22;w++){const q=`${w.toString().padStart(2,"0")}:00:00`;c.push(q)}return c})(),D=(c,w)=>{const q=m.some(B=>B.time===c&&B.day===w);n(q?B=>B.filter(me=>!(me.time===c&&me.day===w)):B=>[...B,{time:c,day:w}])},M=(c,w)=>{n(q=>q.filter(B=>!(B.time===c&&B.day===w))),z(null)},Q=()=>{const c=t.map(w=>{const q=m.filter(B=>B.day===w).map(B=>B.time).sort();return q.length>0?{day:w.toLowerCase(),timeslots:q}:null}).filter(Boolean);return{name:l==null?void 0:l.name,days:c}},ne=c=>{const w=parseInt(c.split(":")[0]);return w<12?`${w}:00 AM`:`${w===12?12:w-12}:00 PM`},Se=c=>{S(c.target.value)},y=()=>{F.trim()&&(l.name=F.trim(),A(!1))},G=c=>{c.key==="Enter"?y():c.key==="Escape"&&(S((l==null?void 0:l.name)||""),A(!1))},le=async c=>{try{Z(!0);const w=await N.callRawAPI("/v3/api/custom/courtmatchup/club/courts/affected-reservations",{exceptions:c},"POST");return w&&!w.error&&w.total_affected>0?(p(w.total_affected),W(w.affected_reservations||[]),I(c),b(!0),!0):!1}catch(w){return console.error("Error checking for affected events:",w),!1}finally{Z(!1)}},ce=async()=>{try{await T({exceptions:J,delete_affected_events:!0}),b(!1),I(null),r(!1),n([])}catch(c){console.error("Error saving changes:",c)}},ue=()=>{b(!1),I(null),p(0),W([])},ae=e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{className:"rounded-lg bg-[#F6F8FA] p-2",onClick:d,disabled:X===0,children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M3.33268 12.5L9.41008 6.42259C9.73552 6.09715 10.2632 6.09715 10.5886 6.42259L16.666 12.5",stroke:X===0?"#D1D5DB":"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("button",{className:"rounded-lg bg-[#F6F8FA] p-2",onClick:C,disabled:X===(g==null?void 0:g.length)-1,children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M16.6673 7.5L10.5899 13.5774C10.2645 13.9028 9.73685 13.9028 9.41141 13.5774L3.33398 7.5",stroke:X===(g==null?void 0:g.length)-1?"#D1D5DB":"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})})]}),e.jsx("div",{className:"flex items-center gap-2",children:u?e.jsx("input",{ref:K,type:"text",value:F,onChange:Se,onBlur:y,onKeyDown:G,className:"w-48 rounded-lg border border-primaryBlue bg-white px-3 py-1 text-xl outline-none",placeholder:"Enter exception name"}):e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("p",{className:"text-xl",children:F}),e.jsx("button",{onClick:()=>A(!0),className:"rounded-lg p-1 hover:bg-gray-100",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M11.7167 7.51667L12.4833 8.28333L4.93333 15.8333H4.16667V15.0667L11.7167 7.51667ZM14.7167 2.5C14.5083 2.5 14.2917 2.58333 14.1333 2.74167L12.6083 4.26667L15.7333 7.39167L17.2583 5.86667C17.5833 5.54167 17.5833 5.01667 17.2583 4.69167L15.3083 2.74167C15.1417 2.575 14.9333 2.5 14.7167 2.5ZM11.7167 5.15833L2.5 14.375V17.5H5.625L14.8417 8.28333L11.7167 5.15833Z",fill:"#868C98"})})})]})})]});s.useEffect(()=>{var c;if(l){const w=((c=l.days)==null?void 0:c.flatMap(q=>q.timeslots.map(B=>({day:q.day.charAt(0).toUpperCase()+q.day.slice(1),time:B}))))||[];n(w)}},[l]);const pe=(c,w)=>{if(!V||!Y)return!1;const[q,B]=V.split("-"),[me,he]=Y.split("-"),ve=t.indexOf(w),ke=t.indexOf(q),fe=t.indexOf(me),je=Math.min(ke,fe),be=Math.max(ke,fe);if(ve<je||ve>be)return!1;const Ee=x.indexOf(c),Ne=x.indexOf(B),ee=x.indexOf(he),ie=Math.min(Ne,ee),re=Math.max(Ne,ee);return Ee>=ie&&Ee<=re},_e=(c,w)=>{f.current&&clearTimeout(f.current),U(`${w}-${c}`),h(`${w}-${c}`),f.current=setTimeout(()=>{v(!0)},150)},de=(c,w)=>{k&&h(`${w}-${c}`)},Ie=()=>{if(f.current&&(clearTimeout(f.current),f.current=null),k){const[c,w]=V.split("-"),[q,B]=Y.split("-"),me=t.indexOf(c),he=t.indexOf(q),ve=Math.min(me,he),ke=Math.max(me,he),fe=x.indexOf(w),je=x.indexOf(B),be=Math.min(fe,je),Ee=Math.max(fe,je),Ne=[];for(let ee=ve;ee<=ke;ee++){const ie=t[ee];for(let re=be;re<=Ee;re++){const Me=x[re];m.some(Te=>Te.time===Me&&Te.day===ie)||Ne.push({time:Me,day:ie})}}n(ee=>[...ee,...Ne]),v(!1),U(null),h(null)}};return s.useEffect(()=>{const c=()=>{k&&Ie()};return window.addEventListener("mouseup",c),()=>window.removeEventListener("mouseup",c)},[k,V,Y]),e.jsx(Re,{isOpen:i,onClose:()=>r(!1),title:"Exception times",onDiscard:()=>{n([]),r(!1)},discardLabel:"Discard",showActions:!0,saveLabel:"Save changes",leftElement:ae,onSave:async()=>{const c=Q(),w=g.map(B=>B.name===l.name?c:B);await le(w)||(await T(w),r(!1),n([]))},isSubmitting:O||$,children:e.jsxs("div",{className:"w-full",children:[e.jsx("div",{className:"grid grid-cols-7 gap-5",children:t.map(c=>e.jsxs("div",{className:"rounded-md bg-white p-2 text-center",children:[e.jsx("div",{className:"mb-2 rounded-md bg-[#F6F8FA] px-3 py-2 font-medium",children:c}),e.jsx("div",{className:"space-y-2",children:x.map(w=>{const q=m==null?void 0:m.some(he=>he.time===w&&he.day===c),B=k&&pe(w,c),me=`${c}-${w}`;return e.jsxs("div",{className:"relative",children:[e.jsx("button",{className:`w-full rounded-md border-2 border-gray-100 px-3 py-2 text-sm font-medium text-gray-500 ${q?"border-2 border-primaryBlue bg-[#EBF1FF] text-primaryBlue":B?"border-primaryBlue bg-[#EBF1FF] bg-opacity-50":"border-gray-300 hover:border-gray-400"}`,onMouseDown:()=>_e(w,c),onMouseEnter:()=>de(w,c),onClick:()=>{f.current&&(clearTimeout(f.current),f.current=null),k||D(w,c),v(!1),U(null),h(null)},children:ne(w)}),H===me&&e.jsx("div",{className:"absolute right-0 z-10 mt-1 w-36 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5",children:e.jsx("div",{className:"py-1",children:e.jsxs("button",{onClick:()=>M(w,c),className:"flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-100",children:[e.jsx("svg",{className:"mr-3 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})}),"Delete"]})})})]},`${c}-${w}`)})})]},c))}),e.jsx(He,{isOpen:o,onClose:ue,onConfirm:ce,eventCount:j,affectedReservations:L,isSubmitting:O,type:"exception"})]})})}const _t=({exceptions:i,setExceptions:r,selectedException:l,setSelectedException:n,showExceptionTimeSelector:m,setShowExceptionTimeSelector:g,selectedExceptionTimes:_,setSelectedExceptionTimes:T,showDeleteExceptionModal:O,setShowDeleteExceptionModal:N,selectedExceptionIndex:H,setSelectedExceptionIndex:z,deletingException:k,deleteException:v,setShowExceptionEditModal:V,showHowItWorksModal:U,setShowHowItWorksModal:Y,timesSelectorIsSubmitting:h,setTimesSelectorIsSubmitting:f,userRole:u,profileSettings:A,globalDispatch:F,handleSearchException:S})=>{const K=new we;return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mt-8 max-w-4xl",children:[e.jsxs("div",{className:"mb-6 flex items-center justify-between",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Exceptions"}),e.jsx("div",{className:"flex items-center gap-4",children:e.jsx("button",{className:"rounded-xl bg-primaryBlue px-4 py-2 text-sm font-medium text-white hover:bg-blue-700",onClick:()=>{n(null),V(!0)},children:"+ Add new"})})]}),e.jsxs("div",{className:"mb-6 flex justify-between",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.25 2.5C12.976 2.5 16 5.524 16 9.25C16 12.976 12.976 16 9.25 16C5.524 16 2.5 12.976 2.5 9.25C2.5 5.524 5.524 2.5 9.25 2.5ZM9.25 14.5C12.1502 14.5 14.5 12.1502 14.5 9.25C14.5 6.349 12.1502 4 9.25 4C6.349 4 4 6.349 4 9.25C4 12.1502 6.349 14.5 9.25 14.5ZM15.6137 14.5532L17.7355 16.6742L16.6742 17.7355L14.5532 15.6137L15.6137 14.5532Z",fill:"#525866"})})}),e.jsx("input",{type:"text",placeholder:"search",onChange:o=>S(o.target.value),className:"w-full rounded-md border border-gray-300 py-2 pl-10 pr-4 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"})]}),e.jsxs("button",{className:"flex items-center gap-2 rounded-md border border-gray-300 px-3 py-2 text-sm text-gray-600",onClick:()=>Y(!0),children:[e.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M7.16732 7.33398H8.00065L8.00065 10.834M14.1673 8.00065C14.1673 11.4064 11.4064 14.1673 8.00065 14.1673C4.5949 14.1673 1.83398 11.4064 1.83398 8.00065C1.83398 4.5949 4.5949 1.83398 8.00065 1.83398C11.4064 1.83398 14.1673 4.5949 14.1673 8.00065Z",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7.54102 5.33333C7.54102 5.58646 7.74622 5.79167 7.99935 5.79167C8.25248 5.79167 8.45768 5.58646 8.45768 5.33333C8.45768 5.0802 8.25248 4.875 7.99935 4.875C7.74622 4.875 7.54102 5.0802 7.54102 5.33333Z",fill:"#868C98",stroke:"#868C98",strokeWidth:"0.25"})]}),"How it works"]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-2 shadow",children:[e.jsx("div",{className:"px-6 py-3",children:e.jsxs("div",{className:"grid grid-cols-2",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Exception name"}),e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Days applicable"})]})}),e.jsx("ul",{className:"pb-5",children:Array.isArray(i)&&i.length>0?i.map((o,b)=>{var j;return e.jsx("li",{className:"mx-2 my-3 rounded-xl bg-gray-100 px-6 py-3",children:e.jsxs("div",{className:"grid grid-cols-2 items-center",children:[e.jsx("span",{className:"text-sm text-gray-900",children:o.name}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:(j=o.days)==null?void 0:j.map(p=>p.day.slice(0,3).toUpperCase()).join(", ")}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"text-gray-400 hover:text-gray-500",onClick:()=>{var L;n(o);const p=((L=o.days)==null?void 0:L.flatMap(W=>W.timeslots.map($=>({day:W.day.charAt(0).toUpperCase()+W.day.slice(1),time:$}))))||[];T(p),g(!0)},children:e.jsx(pt,{className:"h-5 w-5"})}),e.jsx("button",{onClick:()=>{z(b),N(!0)},className:"text-gray-400 hover:text-gray-500",children:e.jsx(gt,{className:"h-5 w-5"})})]})]})]})},b)}):e.jsx("div",{className:"flex w-full items-center justify-center text-center text-sm text-gray-500",children:"No exceptions found"})})]})]}),e.jsx(St,{showExceptionTimeSelector:m,setShowExceptionTimeSelector:g,selectedException:l,setSelectedExceptionTimes:T,selectedExceptionTimes:_,exceptions:i,onSelectException:o=>{var j;n(o);const b=((j=o.days)==null?void 0:j.flatMap(p=>p.timeslots.map(L=>({day:p.day.charAt(0).toUpperCase()+p.day.slice(1),time:L}))))||[];T(b)},isSubmitting:h,onSave:async o=>{var b;f(!0);try{let j,p;Array.isArray(o)?(j={exceptions:o},p=o):(j=o,p=o.exceptions),u==="club"?await K.callRawAPI(`/v3/api/custom/courtmatchup/${u}/profile-edit`,j,"POST"):await K.callRawAPI(`/v3/api/custom/courtmatchup/admin/profile-edit/${(b=A==null?void 0:A.user)==null?void 0:b.id}`,j,"POST"),r(p),R(F,"Times updated successfully",3e3,"success")}catch(j){console.error(j),R(F,"Error updating times",3e3,"error")}finally{f(!1)}}}),e.jsx(tt,{onClose:()=>N(!1),isOpen:O,onDelete:()=>v(H),message:"Are you sure you want to delete this exception?",loading:k})]})},Ke=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],Fe=(i,r)=>{if(!r||r.length===0)return!0;const[l,n]=i.split(":"),m=parseInt(l)*60+parseInt(n);return r.some(g=>{const[_,T]=g.from.split(":"),[O,N]=g.until.split(":"),H=parseInt(_)*60+parseInt(T),z=parseInt(O)*60+parseInt(N);return m>=H&&m<z})},De=(i,r)=>!r||r.length===0?!1:r.includes(i),qe=(i,r)=>{if(!r||r.length===0)return[];const l=i.toLowerCase();return r.reduce((n,m)=>{const g=m.days.find(_=>_.day===l);return g&&n.push({name:m.name,timeslots:g.timeslots}),n},[])},Xe=(i,r)=>{if(!r||r.length===0)return!1;const l=i.includes(":00",5)?i:`${i}:00`;return r.some(n=>n.timeslots.includes(l))},Qe=(i,r)=>{if(!r||r.length===0)return null;const l=i.includes(":00",5)?i:`${i}:00`,n=r.find(m=>m.timeslots.includes(l));return n?n.name:null},kt=({isOpen:i,onClose:r,court:l,club:n,globalDispatch:m,onSave:g,edit_api:_})=>{const[T,O]=s.useState([]),[N,H]=s.useState(null),[z,k]=s.useState(!1),[v,V]=s.useState({times:[],daysOff:[],exceptions:[]}),U=new we;s.useEffect(()=>{i&&l&&Y()},[i,l]),s.useEffect(()=>{if(n){const o=n.times?JSON.parse(n.times):[],b=n.days_off?JSON.parse(n.days_off):[],j=n.exceptions?JSON.parse(n.exceptions):[];V({times:o,daysOff:b,exceptions:j})}},[n]);const Y=()=>{try{let o=[];l.availability&&(o=JSON.parse(l.availability));const b=Ke.map(j=>({day:j.toLowerCase(),timeslots:[]}));o&&Array.isArray(o)&&o.length>0&&o.forEach(j=>{const p=b.findIndex(L=>L.day===j.day.toLowerCase());p!==-1&&(b[p].timeslots=j.timeslots)}),O(b),H(o||[])}catch(o){console.error("Error parsing court availability:",o),R(m,"Error loading court availability",3e3,"error")}},h=()=>{if(!T||!N)return!1;const o=f(),b=Array.isArray(N)?N.filter(p=>p.timeslots&&p.timeslots.length>0):[];if(o.length===0&&b.length>0||o.length>0&&b.length===0||o.length!==b.length)return!0;const j={};b.forEach(p=>{j[p.day.toLowerCase()]=[...p.timeslots].sort()});for(const p of o){const L=p.day.toLowerCase(),W=j[L];if(!W||p.timeslots.length!==W.length)return!0;const $=[...p.timeslots].sort();for(let Z=0;Z<$.length;Z++){const J=$[Z].replace(/:00$/,""),I=W[Z].replace(/:00$/,"");if(J!==I)return!0}}return!1},f=()=>T?T.filter(o=>o.timeslots.length>0):[],u=(o,b)=>{if(De(b,v.daysOff)){R(m,`${b} is a club day off`,3e3,"error");return}if(!Fe(o,v.times)){R(m,"This time is outside club hours",3e3,"error");return}const j=qe(b,v.exceptions);if(Xe(o,j)){const p=Qe(o,j);R(m,`This time is marked as "${p||"Exception"}"`,3e3,"warning")}O(p=>{const L=p.map($=>{if($.day===b.toLowerCase()){const Z=o.replace(":00","");if(!$.timeslots.some(I=>I===o||I===Z))return{...$,timeslots:[...$.timeslots,o].sort()}}return $});return L.some($=>$.day===b.toLowerCase())||L.push({day:b.toLowerCase(),timeslots:[o]}),L})},A=(o,b)=>{O(j=>j.map(p=>{if(p.day===b.toLowerCase()){const L=p.timeslots.filter(W=>W!==o&&W!==o.replace(":00",""));return L.length===0?null:{...p,timeslots:L}}return p}).filter(Boolean))},F=(o,b)=>{const j=T==null?void 0:T.find(L=>L.day===b.toLowerCase());if(!j)return!1;const p=o.replace(":00","");return j.timeslots.some(L=>L===o||L.replace(":00","")===p)},S=async()=>{try{k(!0);const o=f();await U.callRawAPI(_,{court_id:l.id,availability:o},"POST"),R(m,"Court availability updated successfully",3e3,"success"),H(o),g&&g(),r()}catch(o){console.error("Error saving court availability:",o),R(m,"Failed to update court availability",3e3,"error")}finally{k(!1)}},K=()=>{Y(),r()};return l?e.jsx(Re,{isOpen:i,onClose:r,title:`Court Availability - ${l.name}`,onDiscard:K,discardLabel:"Discard",showActions:h(),saveLabel:"Save changes",onSave:S,isSubmitting:z,children:e.jsxs("div",{className:"p-4",children:[e.jsx("div",{className:"mb-4",children:e.jsx("p",{className:"text-sm text-gray-600",children:"Set the available time slots for this court. Users will only be able to book during these times."})}),e.jsx("div",{className:"overflow-x-auto pb-4",children:e.jsx(yt,{days:Ke,isSelected:F,handleTimeSelect:u,handleDeleteTime:A,renderTimeSlotContent:(o,b)=>{if(De(b,v.daysOff))return e.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-red-50 text-xs font-medium text-red-500",children:"Club Closed"});if(!Fe(o.value,v.times))return e.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-red-50 text-xs font-medium text-red-500",children:"Club Closed"});const j=qe(b,v.exceptions);if(Xe(o.value,j)){const p=Qe(o.value,j);return e.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-orange-50 text-xs font-medium text-orange-500",children:e.jsx("span",{className:"truncate",children:p||"Exception"})})}return null},disableTimeSlot:(o,b)=>!!(De(b,v.daysOff)||!Fe(o.value,v.times))})})]})}):null},Et=({club:i,courts:r,exceptions:l,setShowEditModal:n,searchQuery:m,setSearchQuery:g,filteredCourts:_,activeDropdown:T,setActiveDropdown:O,dropdownPosition:N,setDropdownPosition:H,setSelectedCourtForEdit:z,setShowEditCourtModal:k,setSelectedCourtForDelete:v,setShowDeleteCourtModal:V,setShowAddCourtModal:U,sports:Y,globalDispatch:h,edit_api:f,fetchSettings:u})=>{var d,C;const[A,F]=s.useState((i==null?void 0:i.allow_user_court_selection)===1),[S,K]=s.useState(!1),[o,b]=s.useState([]),[j,p]=s.useState(!1),[L,W]=s.useState(!1),[$,Z]=s.useState(null),J=new we,I=localStorage.getItem("user");s.useEffect(()=>{if(r&&r.length>0){const t=r.map(a=>({id:a.id,name:a.name,sport_id:a.sport_id,type:a.type||"",sub_type:a.sub_type||"",allow_reservation:a.allow_reservation!==!1,allow_lesson:a.allow_lesson!==!1,allow_clinic:a.allow_clinic!==!1,allow_buddy:a.allow_buddy!==!1}));F((i==null?void 0:i.allow_user_court_selection)===1),b(t)}},[r]);const X=async()=>{const t=!A;K(!0);try{await J.callRawAPI(f,{allow_user_court_selection:t?1:0},"POST"),await xe(J,{user_id:I,activity_type:oe.court_management,action_type:oe.UPDATE,data:{allow_user_court_selection:t?1:0},club_id:i==null?void 0:i.id,description:`${t?"Enabled":"Disabled"} user court selection`}),F(t),R(h,`User court selection ${t?"enabled":"disabled"} successfully`,3e3,"success"),u&&u()}catch(a){console.error("Error updating user court selection setting:",a),R(h,"Error updating setting",3e3,"error")}finally{K(!1)}},te=async()=>{p(!0);try{const t=o.map(a=>({id:a.id,allow_reservation:a.allow_reservation,allow_lesson:a.allow_lesson,allow_clinic:a.allow_clinic,allow_buddy:a.allow_buddy}));await J.callRawAPI(f,{court_settings:t},"POST"),await xe(J,{user_id:I,activity_type:oe.court_management,action_type:oe.UPDATE,data:t,club_id:i==null?void 0:i.id,description:"Updated court usage settings"}),R(h,"Court settings saved successfully",3e3,"success"),u&&u()}catch(t){console.error("Error saving court settings:",t),R(h,"Error saving court settings",3e3,"error")}finally{p(!1)}};return e.jsxs("div",{className:"mt-8 grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow",children:[e.jsxs("div",{className:"mb-6 flex items-center justify-between rounded-lg bg-[#F6F8FA] px-6 py-3",children:[e.jsx("h2",{className:"text-lg font-medium",children:"Club settings"}),e.jsx("button",{className:"text-sm font-medium text-gray-600",onClick:()=>n(!0),children:"Edit"})]}),e.jsxs("div",{className:"flex flex-col divide-y divide-gray-200 p-6",children:[e.jsxs("div",{className:"py-3",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"GENERAL OPENING HOURS"}),e.jsx("p",{className:"mt-1",children:i!=null&&i.times&&JSON.parse(i==null?void 0:i.times).length>0?(d=JSON.parse(i==null?void 0:i.times))==null?void 0:d.map(t=>e.jsxs("div",{children:[ze(t.from)," -"," ",ze(t.until)]},t.from)):"N/A"})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"DAYS OFF"}),e.jsx("p",{className:"mt-1",children:i!=null&&i.days_off?(C=JSON.parse(i==null?void 0:i.days_off))==null?void 0:C.join(", "):"N/A"})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"TOTAL COURTS"}),e.jsx("p",{className:"mt-1",children:r!=null&&r.length?r==null?void 0:r.length:"N/A"})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"SCHEDULED EXCEPTIONS"}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsx("p",{className:"mt-1",children:l==null?void 0:l.length})})]}),e.jsxs("div",{className:"py-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"ALLOW USERS TO SELECT COURT"}),e.jsx("button",{onClick:X,disabled:S,className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${A?"bg-primaryBlue":"bg-gray-200"} ${S?"cursor-not-allowed opacity-50":""}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${A?"translate-x-6":"translate-x-1"}`})})]}),e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"When enabled, users can select their preferred court during reservation."})]})]})]}),e.jsxs("div",{className:"h-fit rounded-lg bg-white p-6 shadow",children:[e.jsxs("div",{className:"mb-6 flex items-center justify-between",children:[e.jsx("h2",{className:"text-lg font-medium",children:"Courts"}),e.jsx("button",{className:"rounded-xl bg-primaryBlue px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 disabled:bg-gray-400",onClick:te,disabled:j,children:j?"Saving...":"Save Court Settings"})]}),e.jsx("div",{className:"mb-4 flex gap-4",children:e.jsxs("div",{className:"relative w-96",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.25 2.5C12.976 2.5 16 5.524 16 9.25C16 12.976 12.976 16 9.25 16C5.524 16 2.5 12.976 2.5 9.25C2.5 5.524 5.524 2.5 9.25 2.5ZM9.25 14.5C12.1502 14.5 14.5 12.1502 14.5 9.25C14.5 6.349 12.1502 4 9.25 4C6.349 4 4 6.349 4 9.25C4 12.1502 6.349 14.5 9.25 14.5ZM15.6137 14.5532L17.7355 16.6742L16.6742 17.7355L14.5532 15.6137L15.6137 14.5532Z",fill:"#525866"})})}),e.jsx("input",{type:"text",placeholder:"Search by name, sport, type, sub-type...",className:"w-full rounded-lg border border-gray-300 px-4 py-2 pl-10 text-sm focus:border-blue-500 focus:outline-none",value:m,onChange:t=>g(t.target.value)})]})}),e.jsx("div",{className:"overflow-x-auto overflow-y-hidden",children:e.jsxs("table",{className:"min-w-full border-separate border-spacing-y-2",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"text-left text-sm text-gray-500",children:[e.jsx("th",{className:"px-4 pb-4",children:"Name"}),e.jsx("th",{className:"px-4 pb-4",children:"Sport"}),e.jsx("th",{className:"px-4 pb-4",children:"Type"}),e.jsx("th",{className:"px-4 pb-4",children:"Sub-type"}),e.jsx("th",{className:"px-4 pb-4"})]})}),(_==null?void 0:_.length)>0?_==null?void 0:_.map(t=>{var a;return e.jsx("tbody",{children:e.jsxs("tr",{className:"overflow-hidden",children:[e.jsx("td",{className:"rounded-l-xl bg-gray-100 px-4 py-3",children:t.name?t.name:"N/A"}),e.jsx("td",{className:"bg-gray-100 px-4 py-3",children:t.sport_id?(a=Y.find(x=>x.id==t.sport_id))==null?void 0:a.name:"N/A"}),e.jsx("td",{className:"bg-gray-100 px-4 py-3",children:t.type||"--"}),e.jsx("td",{className:"bg-gray-100 px-4 py-3",children:t.sub_type||"--"}),e.jsx("td",{className:"rounded-r-xl bg-gray-100 px-4 py-3",children:e.jsxs("div",{className:"relative",children:[e.jsx("button",{onClick:x=>{const D=x.currentTarget.getBoundingClientRect(),M=x.currentTarget.closest("table").getBoundingClientRect(),Q=D.bottom>M.bottom-100;H(Q?"top":"bottom"),O(T===t.id?null:t.id)},className:"text-gray-400 hover:text-gray-500",children:e.jsx(jt,{className:"h-5 w-5"})}),T===t.id&&e.jsx("div",{className:`absolute right-0 z-10 w-36 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 ${N==="top"?"bottom-full mb-1":"top-full mt-1"}`,children:e.jsxs("div",{className:"py-1",role:"menu",children:[e.jsxs("button",{className:"flex w-full items-center gap-1 px-4 py-2 text-sm text-gray-600 hover:bg-gray-100",onClick:()=>{z(t),k(!0),O(null)},children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M11.041 5.20756L13.5768 2.67181C13.9022 2.34638 14.4298 2.34637 14.7553 2.67181L17.3268 5.2433C17.6522 5.56874 17.6522 6.09638 17.3268 6.42181L14.791 8.95756M11.041 5.20756L2.53509 13.7135C2.37881 13.8698 2.29102 14.0817 2.29102 14.3027V17.7076H5.69584C5.91685 17.7076 6.12881 17.6198 6.28509 17.4635L14.791 8.95756M11.041 5.20756L14.791 8.95756",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),"Edit"]}),e.jsxs("button",{className:"flex w-full items-center gap-1 px-4 py-2 text-sm text-gray-600 hover:bg-gray-100",onClick:()=>{Z(t),W(!0),O(null)},children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M6.66667 1.66667V4.16667M13.3333 1.66667V4.16667M2.5 6.66667H17.5M4.16667 3.33333H15.8333C16.7538 3.33333 17.5 4.07953 17.5 5V16.6667C17.5 17.5871 16.7538 18.3333 15.8333 18.3333H4.16667C3.24619 18.3333 2.5 17.5871 2.5 16.6667V5C2.5 4.07953 3.24619 3.33333 4.16667 3.33333Z",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),"Availability"]}),e.jsxs("button",{className:"flex w-full items-center gap-1 px-4 py-2 text-sm text-gray-600 hover:bg-gray-100",onClick:()=>{v(t.id),V(!0),O(null)},children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M4.79167 4.79102V16.8743C4.79167 17.3346 5.16476 17.7077 5.625 17.7077H14.375C14.8352 17.7077 15.2083 17.3346 15.2083 16.8743V4.79102M4.79167 4.79102H15.2083M4.79167 4.79102H3.125M15.2083 4.79102H16.875M11.6667 8.95768V13.541M8.33333 8.95768V13.541M7.5 4.79102C7.5 3.4103 8.61929 2.29102 10 2.29102C11.3807 2.29102 12.5 3.4103 12.5 4.79102",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),"Delete"]})]})})]})})]})},t.id)}):e.jsx("div",{className:"flex w-full items-center justify-center text-center text-sm text-gray-500",children:"No courts found"})]})}),e.jsx("button",{className:"mt-6 rounded-md px-4 py-2 text-sm font-medium text-black underline",onClick:()=>U(!0),children:"+ Add court"})]}),e.jsx(kt,{isOpen:L,onClose:()=>{W(!1),Z(null)},court:$,club:i,globalDispatch:h,edit_api:f,onSave:()=>{u&&u()}})]})},Mt=({courts:i=[],sports:r=[],edit_api:l,globalDispatch:n,fetchSettings:m,club:g})=>{const[_]=s.useState(!1),[T,O]=s.useState([]),[N,H]=s.useState(!1),[z,k]=s.useState(!1),v=new we,V=localStorage.getItem("user");s.useEffect(()=>{if(i&&i.length>0){const h=i.map(f=>{const u=f.court_settings||{};return{id:f.id,name:f.name,sport_id:f.sport_id,type:f.type||"",sub_type:f.sub_type||"",min_booking_time:u.min_booking_time||30,allow_reservation:u.allow_reservation!==!1,allow_lesson:u.allow_lesson!==!1,allow_clinic:u.allow_clinic!==!1,allow_buddy:u.allow_buddy!==!1,court_settings:u}});O(h)}},[i]);const U=(h,f,u)=>{O(A=>A.map(F=>F.id===h?{...F,[f]:u}:F)),k(!0)},Y=async()=>{H(!0);try{const h=T.map(f=>({court_id:f.id,court_settings:{min_booking_time:f.min_booking_time,allow_reservation:f.allow_reservation,allow_lesson:f.allow_lesson,allow_clinic:f.allow_clinic,allow_buddy:f.allow_buddy}}));await v.callRawAPI(l,{courts:h},"POST"),await xe(v,{user_id:V,activity_type:ye.court_management,action_type:oe.UPDATE,data:h,club_id:g==null?void 0:g.id,description:"Updated court settings"}),R(n,"Court settings saved successfully",3e3,"success"),k(!1),m&&m()}catch(h){console.error("Error saving court settings:",h),R(n,"Error saving court settings",3e3,"error")}finally{H(!1)}};return e.jsxs("div",{className:"mt-8",children:[_&&e.jsx(Pe,{}),e.jsx("div",{className:"mb-6 flex items-center justify-between",children:e.jsx("h2",{className:"text-xl font-semibold",children:"Court Settings"})}),e.jsxs("div",{className:"rounded-lg bg-white p-6 shadow",children:[e.jsx("div",{className:"mb-4",children:e.jsx("p",{className:"text-sm text-gray-600",children:'Configure minimum booking time and allowed activities for each court. Make your changes and click "Save Changes" to apply them.'})}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:"Court Name"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:"Sport"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:"Min. Booking Time"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-center text-xs font-medium uppercase tracking-wider text-gray-500",children:"Allow Reservation"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-center text-xs font-medium uppercase tracking-wider text-gray-500",children:"Allow Lesson"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-center text-xs font-medium uppercase tracking-wider text-gray-500",children:"Allow Clinic"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-center text-xs font-medium uppercase tracking-wider text-gray-500",children:"Allow Find-a-Buddy"})]})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:T.length>0?T.map(h=>{var f;return e.jsxs("tr",{children:[e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"text-sm font-medium text-gray-900",children:h.name})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"text-sm text-gray-500",children:((f=r.find(u=>u.id===h.sport_id))==null?void 0:f.name)||"N/A"})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{type:"number",min:"30",max:"1440",step:"30",value:h.min_booking_time,onChange:u=>{const A=parseInt(u.target.value,10);isNaN(A)||A<30||A>1440||U(h.id,"min_booking_time",A)},className:"w-20 rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"}),e.jsx("span",{className:"ml-2 text-sm text-gray-500",children:"minutes"})]})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"flex justify-center",children:e.jsx(Le,{checked:h.allow_reservation,onChange:u=>{U(h.id,"allow_reservation",u)},className:`${h.allow_reservation?"bg-blue-500":"bg-gray-200"} relative inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${h.allow_reservation?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"flex justify-center",children:e.jsx(Le,{checked:h.allow_lesson,onChange:u=>{U(h.id,"allow_lesson",u)},className:`${h.allow_lesson?"bg-blue-500":"bg-gray-200"} relative inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${h.allow_lesson?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"flex justify-center",children:e.jsx(Le,{checked:h.allow_clinic,onChange:u=>{U(h.id,"allow_clinic",u)},className:`${h.allow_clinic?"bg-blue-500":"bg-gray-200"} relative inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${h.allow_clinic?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"flex justify-center",children:e.jsx(Le,{checked:h.allow_buddy,onChange:u=>{U(h.id,"allow_buddy",u)},className:`${h.allow_buddy?"bg-blue-500":"bg-gray-200"} relative inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${h.allow_buddy?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})})})]},h.id)}):e.jsx("tr",{children:e.jsx("td",{colSpan:"7",className:"px-6 py-4 text-center text-sm text-gray-500",children:"No courts found. Please add courts in the General Settings tab."})})})]})}),T.length>0&&e.jsxs("div",{className:"mt-6 flex items-center justify-between",children:[e.jsx("p",{className:"text-xs text-gray-500",children:"* Minimum booking time can be set between 30 minutes and 24 hours (1440 minutes)."}),e.jsx("button",{onClick:Y,disabled:N,className:"inline-flex items-center rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",children:N?e.jsxs(e.Fragment,{children:[e.jsxs("svg",{className:"-ml-1 mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Saving..."]}):"Save Changes"})]})]})]})},se=new we;function Ht({profileSettings:i,fetchSettings:r,sports:l=[],club:n,courts:m,edit_api:g}){const[_,T]=s.useState("general-settings"),[O,N]=s.useState(null),[H,z]=s.useState("bottom"),[k,v]=s.useState(!1),[V,U]=s.useState(!1),[Y,h]=s.useState(!1),[f,u]=s.useState(null),[A,F]=s.useState(!1),{dispatch:S}=Ce.useContext(Be),[K,o]=s.useState(!1),[b,j]=s.useState(!1),[p,L]=s.useState(!1),[W,$]=s.useState(null),[Z,J]=s.useState([]),[I,X]=s.useState(""),[te,d]=s.useState(!1),[C,t]=s.useState(!1),[a,x]=s.useState(null),[D,M]=s.useState(!1),[Q,ne]=s.useState(!1),[Se,y]=s.useState([]),[G,le]=s.useState(!1),[ce,ue]=s.useState(""),[ae,pe]=s.useState(!1),[_e,de]=s.useState(null),[Ie,c]=s.useState(!1),[w,q]=s.useState(!1),[B,me]=s.useState(null),[he,ve]=s.useState(!1),[ke,fe]=s.useState(!1),[je,be]=s.useState(null),[Ee]=s.useState(!1),Ne=localStorage.getItem("role"),[ee,ie]=s.useState(n!=null&&n.exceptions?JSON.parse(n==null?void 0:n.exceptions):[]),re=localStorage.getItem("user");console.log("courts",m),s.useEffect(()=>{const E=P=>{O&&!P.target.closest(".relative")&&N(null)};return document.addEventListener("mousedown",E),()=>{document.removeEventListener("mousedown",E)}},[O]),s.useEffect(()=>{ie(n!=null&&n.exceptions?JSON.parse(n==null?void 0:n.exceptions):[])},[n==null?void 0:n.exceptions]),Ce.useEffect(()=>{S({type:"SETPATH",payload:{path:"court-management"}})},[]);const Me=async E=>{M(!0);const P=ee.filter((ge,Ae)=>Ae!==E);try{await se.callRawAPI(g,{exceptions:P},"POST"),await xe(se,{user_id:re,activity_type:ye.court_management,action_type:oe.DELETE,data:P,club_id:n==null?void 0:n.id,description:"Deleted court exception"}),ie(P),t(!1),M(!1),R(S,"Exception deleted successfully")}catch{M(!1)}},Te=E=>{if(E===""){ie(n!=null&&n.exceptions?JSON.parse(n==null?void 0:n.exceptions):[]);return}const P=ee.filter(ge=>ge.name.toLowerCase().includes(E.toLowerCase()));ie(P)},nt=async()=>{ve(!0);try{se.setTable("club_court"),await se.callRestAPI({id:je},"DELETE"),await xe(se,{user_id:re,activity_type:ye.court_management,action_type:oe.DELETE,data:je,club_id:n==null?void 0:n.id,description:"Deleted court"})}catch(E){console.log(E)}finally{ve(!1),be(null),fe(!1),r()}};console.log(ee);const at=()=>{switch(_){case"exceptions":return e.jsx(_t,{exceptions:ee,setExceptions:ie,selectedException:f,setSelectedException:u,showExceptionTimeSelector:Q,setShowExceptionTimeSelector:ne,selectedExceptionTimes:Se,setSelectedExceptionTimes:y,showDeleteExceptionModal:C,setShowDeleteExceptionModal:t,selectedExceptionIndex:a,setSelectedExceptionIndex:x,deletingException:D,deleteException:Me,setShowExceptionEditModal:h,showHowItWorksModal:k,setShowHowItWorksModal:v,timesSelectorIsSubmitting:G,setTimesSelectorIsSubmitting:le,userRole:Ne,profileSettings:i,globalDispatch:S,handleSearchException:Te});case"general-settings":return e.jsx(Et,{club:n,courts:m,exceptions:ee,setShowEditModal:U,searchQuery:ce,setSearchQuery:ue,filteredCourts:rt,activeDropdown:O,setActiveDropdown:N,dropdownPosition:H,setDropdownPosition:z,setSelectedCourtForEdit:de,setShowEditCourtModal:pe,setSelectedCourtForDelete:be,setShowDeleteCourtModal:fe,setShowAddCourtModal:F,sports:l,globalDispatch:S,edit_api:g,fetchSettings:r});case"court-settings":return e.jsx(Mt,{courts:m,sports:l,edit_api:g,globalDispatch:S,fetchSettings:r,club:n});default:return null}},rt=m==null?void 0:m.filter(E=>{var ge,Ae,We,Ve,Ze;const P=ce.toLowerCase();return((ge=E.name)==null?void 0:ge.toLowerCase().includes(P))||((We=(Ae=l.find(mt=>mt.id==E.sport_id))==null?void 0:Ae.name)==null?void 0:We.toLowerCase().includes(P))||((Ve=E.type)==null?void 0:Ve.toLowerCase().includes(P))||((Ze=E.sub_type)==null?void 0:Ze.toLowerCase().includes(P))}),lt=()=>k?e.jsx("div",{className:"fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50",children:e.jsxs("div",{className:"w-full max-w-lg rounded-2xl bg-white",children:[e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-4",children:e.jsx("h2",{className:"text-lg font-semibold",children:"How it works"})}),e.jsx("div",{className:"mb-6 space-y-4 text-sm text-gray-600",children:e.jsxs("p",{children:[e.jsxs("ol",{className:"list-inside list-decimal space-y-2",children:[e.jsx("li",{children:"You can define which hours the exceptions take place here and the exception will automatically be added everywhere in the schedule where you defined the hours for it."}),e.jsx("li",{children:"When adding an event in the daily scheduler, the club can specify these schedule exception names and define the date/time/repeating details."})]}),e.jsx("p",{className:"mt-2",children:'Separately, you can also create custom event types in the daily scheduler by using the event type "other" and defining the name of the event.'})]})})]}),e.jsx("div",{className:"flex justify-end border-t border-gray-200 p-6",children:e.jsx("button",{onClick:()=>v(!1),className:"rounded-xl bg-[#2B5F2B] px-5 py-3 text-sm text-white",children:"Close"})})]})}):null,Oe=Ce.useRef(null),it=async()=>{Oe.current&&await Oe.current.submit()},ot=async E=>{try{o(!0),await se.callRawAPI(g,E,"POST"),await xe(se,{user_id:re,activity_type:ye.court_management,action_type:oe.UPDATE,data:E,club_id:n==null?void 0:n.id,description:"Updated club settings"}),U(!1),R(S,"Settings saved successfully",3e3,"success"),r()}catch(P){console.error("Error saving settings:",P),R(S,"Error saving settings",3e3,"error")}finally{o(!1)}},Ue=async()=>{if(d(!0),!I.length){R(S,"Please enter a name",3e3,"error");return}const E={name:I,days:[]},P=[...ee,E];try{await se.callRawAPI(g,{exceptions:P},"POST"),await xe(se,{user_id:re,activity_type:ye.court_management,action_type:oe.CREATE,data:E,club_id:n==null?void 0:n.id,description:"Added new court exception"}),ie(P),R(S,"Exception added successfully",3e3,"success"),h(!1),u(E),ne(!0),X("")}catch(ge){console.error(ge),R(S,"Error adding exception",3e3,"error")}finally{d(!1)}},ct=async E=>{j(!0);try{const P={courts:[E]};await se.callRawAPI(g,P,"POST"),await xe(se,{user_id:re,activity_type:ye.court_management,action_type:oe.CREATE,data:E,club_id:n==null?void 0:n.id,description:"Added new court"}),F(!1),R(S,"Court added successfully",3e3,"success"),r()}catch(P){console.error(P)}finally{j(!1)}},dt=async E=>{c(!0);try{await se.callRawAPI(g,{courts:[E]},"POST"),await xe(se,{user_id:re,activity_type:ye.court_management,action_type:oe.UPDATE,data:E,club_id:n==null?void 0:n.id,description:"Updated court"}),R(S,"Court updated successfully",3e3,"success"),pe(!1),r()}catch(P){console.error(P),R(S,"Error updating court",3e3,"error")}finally{c(!1)}};return e.jsxs("div",{className:"",children:[Ee&&e.jsx(Pe,{}),e.jsxs("div",{className:"flex items-center justify-between border-b border-gray-200",children:[e.jsx("nav",{className:"-mb-px flex space-x-8",children:["General settings","Court settings","Exceptions"].map(E=>e.jsx("button",{className:`
                whitespace-nowrap border-b-2 px-1 py-4 text-sm font-medium
                ${_===E.toLowerCase().replace(" ","-")?"border-primaryBlue text-primaryBlue":"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"}
              `,onClick:()=>T(E.toLowerCase().replace(" ","-")),children:E},E))}),e.jsx(xt,{title:"Court Management History",emptyMessage:"No court management history found",activityType:ye.court_management})]}),at(),e.jsx(lt,{}),e.jsx(bt,{ref:Oe,club:n,onSubmit:ot,isOpen:V,onClose:()=>U(!1),title:"Edit club settings",onPrimaryAction:it,submitting:K}),e.jsx($e,{isOpen:Y,onClose:()=>{h(!1),u(null)},title:f?"Edit exception":"Add exception",onPrimaryAction:Ue,primaryButtonText:f?"Save changes":"Add exception",submitting:te,children:e.jsx(Nt,{initialData:f,onSubmit:Ue,setExceptionName:X,exceptionName:I})}),e.jsx(Ge,{profileSettings:i,club:n,sports:l,courts:m,onSubmit:ct,onClose:()=>F(!1),isEdit:!1,isOpen:A,title:"Add new court",showFooter:!1}),e.jsx(Ye,{selectedCourt:W,selectedTimes:Z,setSelectedTimes:J,showTimesAvailableModal:p,setShowTimesAvailableModal:L}),e.jsx(Ge,{onSubmit:dt,isEdit:!0,initialData:_e,sports:l,courts:m,profileSettings:i,onClose:()=>{pe(!1),de(null)},isOpen:ae,title:"Edit court",primaryButtonText:"Save changes",submitting:Ie,showFooter:!1}),e.jsx(Ye,{showTimesAvailableModal:w,setShowTimesAvailableModal:q,selectedCourt:B,selectedTimes:Z,setSelectedTimes:J,onSave:async E=>{try{const P={courts:[{court_id:B.id,availability:E}]};await se.callRawAPI(g,P,"POST"),R(S,"Times updated successfully",3e3,"success"),r()}catch(P){console.error(P),R(S,"Error updating times",3e3,"error")}}}),e.jsx(tt,{isOpen:ke,onClose:()=>{fe(!1),be(null)},title:"Delete court",loading:he,onDelete:nt,message:"Are you sure you want to delete this court?"})]})}export{Ht as C};

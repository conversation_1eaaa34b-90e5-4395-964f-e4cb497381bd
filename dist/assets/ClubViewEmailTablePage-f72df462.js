import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import{b as i,j as n}from"./vendor-851db8c1.js";import"./yup-54691517.js";import{M as x,G as a,t as p}from"./index-f915b394.js";import{S as j}from"./index-02625b16.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let l=new x;const _=()=>{i.useContext(a);const{dispatch:o}=i.useContext(a),[e,c]=i.useState({}),[d,m]=i.useState(!0),r=n();return i.useEffect(function(){(async function(){try{m(!0),l.setTable("email");const t=await l.callRestAPI({id:Number(r==null?void 0:r.id),join:""},"GET");t.error||(c(t.model),m(!1))}catch(t){m(!1),console.log("error",t),p(o,t.message)}})()},[]),s.jsx("div",{className:" shadow-md rounded  mx-auto p-5",children:d?s.jsx(j,{}):s.jsxs(s.Fragment,{children:[s.jsx("h4",{className:"text-2xl font-medium",children:"View Email"}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Slug"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.slug})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Subject"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.subject})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Tag"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.tag})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Html"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.html})]})})]})})};export{_ as default};

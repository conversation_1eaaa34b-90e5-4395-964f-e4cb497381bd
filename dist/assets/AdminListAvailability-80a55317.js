import{j as o}from"./@nivo/heatmap-ba1ecfff.js";import{A as C}from"./Availability-61b39886.js";import{r as a}from"./vendor-851db8c1.js";import{w as E,M as T,e as j}from"./index-f915b394.js";import{S as I}from"./react-select-c8303602.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./date-fns-07266b7d.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";let e=new T;function k(){a.useState(null);const[m,i]=a.useState(!1),[p,u]=a.useState([]),[d,f]=a.useState(null),[r,b]=a.useState([]),[l,h]=a.useState([]),[c,S]=a.useState([]),[n,y]=a.useState([]);async function A(){i(!0);try{e.setTable("clubs");const t=await e.callRestAPI({},"GETALL");u(t.list)}catch(t){console.error("Error fetching data:",t)}finally{i(!1)}}const g=async t=>{e.setTable("club_court");const s=await e.callRestAPI({filter:[`club_id,cs,${t}`]},"GETALL");y(s==null?void 0:s.list)},w=async t=>{e.setTable("booking");const s=await e.callRestAPI({},"GETALL");S(s==null?void 0:s.list)},x=async t=>{e.setTable("user");const s=await e.callRestAPI({filter:[`club_id,cs,${t}`,"role,cs,coach"]},"GETALL");b(s==null?void 0:s.list)},L=async t=>{e.setTable("user");const s=await e.callRestAPI({filter:[`club_id,cs,${t}`,"role,cs,staff"]},"GETALL");h(s==null?void 0:s.list)},v=async t=>{i(!0);try{await g(t.value),await w(t.value),await x(t.value),await L(t.value),f(t.value)}catch(s){console.error("Error fetching data:",s)}finally{i(!1)}};return console.log({courts:n,bookings:c,coaches:r,staff:l}),a.useEffect(()=>{A()},[]),o.jsxs("div",{className:"h-full bg-white p-4 sm:p-6 lg:p-8",children:[m&&o.jsx(j,{}),o.jsxs("div",{className:"mb-4 max-w-xl",children:[o.jsx("label",{className:"mb-2 block text-base font-medium text-gray-900",children:"Select club"}),o.jsx(I,{className:"w-full rounded-lg border border-gray-200 p-2.5 text-sm",options:p.map(t=>({value:t==null?void 0:t.id,label:t.name})),isMulti:!1,onChange:v})]}),o.jsx(C,{selectedClub:d,coaches:r,staff:l,courts:n,bookings:c})]})}const pt=E(k,"availability_dashboard","You don't have permission to access availability dashboard");export{pt as default};

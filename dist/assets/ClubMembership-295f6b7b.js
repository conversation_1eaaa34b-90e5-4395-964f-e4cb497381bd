import{j as i}from"./@nivo/heatmap-ba1ecfff.js";import{r as o,f as u,b as f}from"./vendor-851db8c1.js";import{G as S,e as b,M as d}from"./index-f915b394.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import{M as x}from"./MembershipModules-bd5ff935.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./index.esm-51ae62c8.js";import"./ChevronRightIcon-efb4c46c.js";import"./TrashIcon-7d213648.js";let g=new d;function it(){const[p,a]=o.useState([]),[l,n]=o.useState(null);o.useState(null),o.useState(!1),o.useState(""),u();const[c,r]=o.useState(!1);f.useContext(S);const e=async()=>{var m,s;r(!0);try{const t=await g.callRawAPI("/v3/api/custom/courtmatchup/club/profile",{},"GET");n(t==null?void 0:t.model),a(JSON.parse((s=(m=t==null?void 0:t.model)==null?void 0:m.club)==null?void 0:s.membership_settings)||[])}catch(t){console.log(t)}finally{r(!1)}};return o.useEffect(()=>{e()},[]),i.jsxs("div",{className:"flex flex-col gap-4",children:[c&&i.jsx(b,{}),i.jsx(x,{fetchProfileSettings:e,profileSettings:l,membershipPlans:p,role:"club"})]})}export{it as default};

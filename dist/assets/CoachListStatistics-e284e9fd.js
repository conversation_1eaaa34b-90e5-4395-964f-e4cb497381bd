import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as o,b}from"./vendor-851db8c1.js";import{M as Y,A as q,G as J,v as y,e as Q}from"./index-f915b394.js";import{M as K,H as U}from"./HeatMapChart-da4e0846.js";import{R as p,P as O,a as _,C as W,T as u,L as j,B as Z,b as I,X as G,Y as $,c as w}from"./recharts-614cb831.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-google-maps-db82edcf.js";const v=["#8884d8","#82ca9d","#FFBB28","#FF8042"],ee=({stats:f})=>{var m;const[C,d]=o.useState(!1),[s,h]=o.useState(!1),i=(m=f.revenueByModule)==null?void 0:m.map(l=>({name:l.module,value:l.revenue||0})),x=[{day:"Wed",Clinics:0,Coaching:0},{day:"Thu",Clinics:0,Coaching:0},{day:"Fri",Clinics:0,Coaching:0},{day:"Sat",Clinics:0,Coaching:0},{day:"Sun",Clinics:0,Coaching:0}],n=({title:l,children:r,onFullView:c})=>e.jsxs("div",{className:"rounded-2xl bg-white p-6 shadow-sm",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("h2",{className:"text-base font-semibold text-gray-800",children:l}),e.jsx("button",{onClick:c,className:"",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M5.75 12.75V17.25C5.75 17.8023 6.19772 18.25 6.75 18.25H11.25M12.75 5.75H17.25C17.8023 5.75 18.25 6.19772 18.25 6.75V11.25",stroke:"#525866","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})})]}),r]});return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[e.jsx(n,{title:"Revenue Made Through Each Module",onFullView:()=>d(!0),children:e.jsx("div",{className:"h-[300px]",children:e.jsx(p,{width:"100%",height:"100%",children:e.jsxs(O,{children:[e.jsx(_,{data:i,dataKey:"value",nameKey:"name",cx:"50%",cy:"50%",fill:"#8884d8",label:!0,children:i==null?void 0:i.map((l,r)=>e.jsx(W,{fill:v[r%v.length]},`cell-${r}`))}),e.jsx(u,{}),e.jsx(j,{})]})})})}),e.jsx(n,{title:"Revenue from Each Module",onFullView:()=>h(!0),children:e.jsx("div",{className:"h-[300px]",children:e.jsx(p,{width:"100%",height:"100%",children:e.jsxs(Z,{data:x,children:[e.jsx(I,{strokeDasharray:"3 3"}),e.jsx(G,{dataKey:"day"}),e.jsx($,{}),e.jsx(u,{}),e.jsx(j,{}),e.jsx(w,{dataKey:"Clinics",fill:"#8884d8"}),e.jsx(w,{dataKey:"Coaching",fill:"#82ca9d"})]})})})})]}),e.jsx(K,{isOpen:C,onClose:()=>d(!1),title:"Module Distribution",children:e.jsx("div",{className:"h-[600px] w-full",children:e.jsx(p,{width:"100%",height:"100%",children:e.jsxs(O,{children:[e.jsx(_,{data:i,dataKey:"value",nameKey:"name",cx:"50%",cy:"50%",fill:"#8884d8",label:!0,children:i==null?void 0:i.map((l,r)=>e.jsx(W,{fill:v[r%v.length]},`cell-${r}`))}),e.jsx(u,{}),e.jsx(j,{})]})})})}),e.jsx(K,{isOpen:s,onClose:()=>h(!1),title:"Revenue Distribution",children:e.jsx("div",{className:"h-[600px] w-full",children:e.jsx(p,{width:"100%",height:"100%",children:e.jsxs(Z,{data:x,children:[e.jsx(I,{strokeDasharray:"3 3"}),e.jsx(G,{dataKey:"day"}),e.jsx($,{}),e.jsx(u,{}),e.jsx(j,{}),e.jsx(w,{dataKey:"Clinics",fill:"#8884d8"}),e.jsx(w,{dataKey:"Coaching",fill:"#82ca9d"})]})})})})]})},se=new Y,Ke=()=>{var N,M,S,k,F,E,L,B,D,H,R,T;b.useContext(q);const{dispatch:f}=b.useContext(J),[C,d]=o.useState(!1),[s,h]=o.useState([]),[i,x]=o.useState(window.innerWidth<768),[n,m]=o.useState(new Date),[l,r]=o.useState(new Date),c=async(t=n,a=l)=>{try{d(!0);const g=t instanceof Date?t.toISOString().split("T")[0]:t,V=a instanceof Date?a.toISOString().split("T")[0]:a;console.log(g,V);const A=await se.callRawAPI(`/v3/api/custom/courtmatchup/coach/statistics?start_date=${g}&end_date=${V}&start_time=00:00&end_time=23:59`,{},"GET");A.error||h(A.model)}catch(g){console.log(g)}finally{d(!1)}},z=t=>{const a=t.target.value;m(new Date(a)),c(a,l.toISOString().split("T")[0])},P=t=>{const a=t.target.value;r(new Date(a)),c(n.toISOString().split("T")[0],a)};b.useEffect(()=>{f({type:"SETPATH",payload:{path:"dashboard"}}),c()},[]),o.useEffect(()=>{const t=()=>{x(window.innerWidth<768)};return window.addEventListener("resize",t),()=>{window.removeEventListener("resize",t)}},[]),o.useEffect(()=>{console.log("Mobile view state updated:",i)},[i]);const X=[{title:"Coach reservations",stats:[{label:"Hours",value:((M=(N=s==null?void 0:s.coachReservations)==null?void 0:N[0])==null?void 0:M.total_hours)||0,icon:e.jsxs("svg",{width:"33",height:"32",viewBox:"0 0 33 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",width:"32",height:"32",rx:"16",fill:"#EBF1FF"}),e.jsx("path",{d:"M16.4993 12.4587V16.0003L18.791 18.292M24.2077 16.0003C24.2077 20.2575 20.7565 23.7087 16.4993 23.7087C12.2422 23.7087 8.79102 20.2575 8.79102 16.0003C8.79102 11.7431 12.2422 8.29199 16.4993 8.29199C20.7565 8.29199 24.2077 11.7431 24.2077 16.0003Z",stroke:"#253EA7",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})},{label:"Revenue",value:y(((k=(S=s==null?void 0:s.coachReservations)==null?void 0:S[0])==null?void 0:k.total_revenue)||0),icon:e.jsxs("svg",{width:"33",height:"32",viewBox:"0 0 33 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",width:"32",height:"32",rx:"16",fill:"#EBF1FF"}),e.jsx("path",{d:"M10.459 12.875H11.2923M21.709 19.125H22.5423M7.95898 20.7917V11.2083C7.95898 10.7481 8.33208 10.375 8.79232 10.375H24.209C24.6692 10.375 25.0423 10.7481 25.0423 11.2083V20.7917C25.0423 21.2519 24.6692 21.625 24.209 21.625H8.79232C8.33208 21.625 7.95898 21.2519 7.95898 20.7917ZM18.1673 16C18.1673 16.9205 17.4211 17.6667 16.5007 17.6667C15.5802 17.6667 14.834 16.9205 14.834 16C14.834 15.0795 15.5802 14.3333 16.5007 14.3333C17.4211 14.3333 18.1673 15.0795 18.1673 16Z",stroke:"#253EA7",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}]},{title:"Clinic reservations",stats:[{label:"Hours",value:((E=(F=s==null?void 0:s.clinicReservations)==null?void 0:F[0])==null?void 0:E.total_hours)||0,icon:e.jsxs("svg",{width:"33",height:"32",viewBox:"0 0 33 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",width:"32",height:"32",rx:"16",fill:"#EBF1FF"}),e.jsx("path",{d:"M16.4993 12.4587V16.0003L18.791 18.292M24.2077 16.0003C24.2077 20.2575 20.7565 23.7087 16.4993 23.7087C12.2422 23.7087 8.79102 20.2575 8.79102 16.0003C8.79102 11.7431 12.2422 8.29199 16.4993 8.29199C20.7565 8.29199 24.2077 11.7431 24.2077 16.0003Z",stroke:"#253EA7",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})},{label:"Revenue",value:y(((B=(L=s==null?void 0:s.clinicReservations)==null?void 0:L[0])==null?void 0:B.total_revenue)||0),icon:e.jsxs("svg",{width:"33",height:"32",viewBox:"0 0 33 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",width:"32",height:"32",rx:"16",fill:"#EBF1FF"}),e.jsx("path",{d:"M10.459 12.875H11.2923M21.709 19.125H22.5423M7.95898 20.7917V11.2083C7.95898 10.7481 8.33208 10.375 8.79232 10.375H24.209C24.6692 10.375 25.0423 10.7481 25.0423 11.2083V20.7917C25.0423 21.2519 24.6692 21.625 24.209 21.625H8.79232C8.33208 21.625 7.95898 21.2519 7.95898 20.7917ZM18.1673 16C18.1673 16.9205 17.4211 17.6667 16.5007 17.6667C15.5802 17.6667 14.834 16.9205 14.834 16C14.834 15.0795 15.5802 14.3333 16.5007 14.3333C17.4211 14.3333 18.1673 15.0795 18.1673 16Z",stroke:"#253EA7",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}]},{title:"Total reservations",stats:[{label:"Hours",value:((H=(D=s==null?void 0:s.totalReservations)==null?void 0:D[0])==null?void 0:H.total_hours)||0,icon:e.jsxs("svg",{width:"33",height:"32",viewBox:"0 0 33 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",width:"32",height:"32",rx:"16",fill:"#EBF1FF"}),e.jsx("path",{d:"M16.4993 12.4587V16.0003L18.791 18.292M24.2077 16.0003C24.2077 20.2575 20.7565 23.7087 16.4993 23.7087C12.2422 23.7087 8.79102 20.2575 8.79102 16.0003C8.79102 11.7431 12.2422 8.29199 16.4993 8.29199C20.7565 8.29199 24.2077 11.7431 24.2077 16.0003Z",stroke:"#253EA7",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})},{label:"Revenue",value:y(((T=(R=s==null?void 0:s.totalReservations)==null?void 0:R[0])==null?void 0:T.total_revenue)||0),icon:e.jsxs("svg",{width:"33",height:"32",viewBox:"0 0 33 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",width:"32",height:"32",rx:"16",fill:"#EBF1FF"}),e.jsx("path",{d:"M10.459 12.875H11.2923M21.709 19.125H22.5423M7.95898 20.7917V11.2083C7.95898 10.7481 8.33208 10.375 8.79232 10.375H24.209C24.6692 10.375 25.0423 10.7481 25.0423 11.2083V20.7917C25.0423 21.2519 24.6692 21.625 24.209 21.625H8.79232C8.33208 21.625 7.95898 21.2519 7.95898 20.7917ZM18.1673 16C18.1673 16.9205 17.4211 17.6667 16.5007 17.6667C15.5802 17.6667 14.834 16.9205 14.834 16C14.834 15.0795 15.5802 14.3333 16.5007 14.3333C17.4211 14.3333 18.1673 15.0795 18.1673 16Z",stroke:"#253EA7",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}]}];return e.jsxs(e.Fragment,{children:[C&&e.jsx(Q,{}),e.jsxs("div",{className:"p-3 sm:p-4 md:p-6",children:[e.jsx("div",{className:"mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between",children:e.jsxs("div",{className:"flex flex-col gap-3 sm:flex-row sm:items-center",children:[e.jsxs("div",{className:"flex items-center gap-2 sm:gap-4",children:[e.jsx("label",{htmlFor:"start-date",className:"text-sm font-medium text-gray-700 sm:hidden",children:"From:"}),e.jsx("input",{id:"start-date",type:"date",onChange:z,value:n.toISOString().split("T")[0],className:"w-full rounded-lg border border-gray-300 px-3 py-2 text-sm text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 sm:w-auto","aria-label":"Start date"})]}),e.jsx("div",{className:"hidden sm:block",children:"-"}),e.jsxs("div",{className:"flex items-center gap-2 sm:gap-4",children:[e.jsx("label",{htmlFor:"end-date",className:"text-sm font-medium text-gray-700 sm:hidden",children:"To:"}),e.jsx("input",{id:"end-date",type:"date",onChange:P,value:l.toISOString().split("T")[0],className:"w-full rounded-lg border border-gray-300 px-3 py-2 text-sm text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 sm:w-auto","aria-label":"End date"})]})]})}),e.jsx("div",{className:"grid grid-cols-1 items-center justify-center gap-4 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4",children:X.map((t,a)=>e.jsxs("div",{className:"rounded-2xl bg-white p-4 shadow-sm sm:p-6",children:[e.jsx("h2",{className:"mb-4 text-base font-semibold text-gray-800 sm:text-lg",children:t.title}),e.jsxs("div",{className:"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3 sm:flex-col sm:items-center",children:[e.jsx("div",{className:"rounded-full bg-gray-100 p-2",children:t.stats[0].icon}),e.jsxs("div",{className:"sm:text-center",children:[e.jsx("p",{className:"text-sm text-gray-600 sm:text-base",children:t.stats[0].label}),e.jsx("p",{className:"text-lg font-semibold sm:text-xl",children:t.stats[0].value})]})]}),e.jsx("div",{className:"hidden h-20 w-[1px] bg-gray-200 sm:block"}),e.jsx("div",{className:"my-3 h-[1px] w-full bg-gray-200 sm:hidden"}),e.jsxs("div",{className:"flex items-center gap-3 sm:flex-col sm:items-center sm:justify-center",children:[e.jsx("div",{className:"rounded-full bg-gray-100 p-2",children:t.stats[1].icon}),e.jsxs("div",{className:"sm:text-center",children:[e.jsx("p",{className:"text-sm text-gray-600 sm:text-base",children:t.stats[1].label}),e.jsxs("p",{className:"text-lg font-semibold sm:text-xl",children:[t.stats[1].value,".00"]})]})]})]})]},a))}),e.jsxs("div",{className:"mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3",children:[e.jsx("div",{className:"col-span-1 lg:col-span-2",children:e.jsx("div",{className:"overflow-hidden rounded-lg",children:e.jsx(ee,{stats:s})})}),e.jsx("div",{className:"col-span-1",children:e.jsx("div",{className:"overflow-hidden rounded-lg",children:e.jsx(U,{stats:s})})})]})]})]})};export{Ke as default};

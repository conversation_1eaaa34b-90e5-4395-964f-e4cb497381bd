import{j as o}from"./@nivo/heatmap-ba1ecfff.js";import{r}from"./vendor-851db8c1.js";import{u as n,aO as f,e as u,aC as l,M as d}from"./index-f915b394.js";import{L as x}from"./ListReservations-69ca8dee.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./yup-54691517.js";import"./yup-2824f222.js";import"./@hookform/resolvers-67648cca.js";import"./index-eb1bc208.js";import"./Skeleton-1e8bf077.js";import"./react-loading-skeleton-3d87d1f5.js";import"./CheckinModal-850ee319.js";import"./BottomDrawer-7b010fbb.js";import"./BackButton-11ba52b2.js";import"./TimeSlots-683a5ba6.js";import"./react-tooltip-7a26650a.js";import"./Calendar-9031b5fe.js";import"./ChevronLeftIcon-e5eecf9c.js";import"./ChevronRightIcon-efb4c46c.js";import"./SportTypeSelection-ee0cc3da.js";import"./SelectionOptionsCard-0d5c6ddd.js";import"./SelectionOption-01b973e9.js";import"./ReservationSummary-4650f2bc.js";import"./LoadingOverlay-87926629.js";import"./ReservationStatus-5ced670f.js";import"./DataTable-a2248415.js";import"./HistoryComponent-a235a60c.js";new d;function jt(){const[p,i]=r.useState(!1),[m,s]=r.useState(null),{club:t,courts:e,sports:a}=n();return r.useEffect(()=>{(async()=>{i(!0);const c=await f(t==null?void 0:t.id);s(c),i(!1)})()},[t==null?void 0:t.id]),o.jsxs("div",{className:"h-full bg-white p-4 sm:p-6 lg:p-8",children:[p&&o.jsx(u,{}),m&&m.staff.court_reservation?o.jsx(x,{club:t,sports:a,courts:e}):o.jsx(l,{message:"You don't have permission to access the reservations"})]})}export{jt as default};

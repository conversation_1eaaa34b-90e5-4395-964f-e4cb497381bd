import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as c,b as V}from"./vendor-851db8c1.js";import{G as W,al as Y,R as Z,a1 as B,M as U,b as u}from"./index-f915b394.js";import{P as _}from"./PlusIcon-7e8d14d7.js";import{P as ee}from"./PencilIcon-35185602.js";import{T as se}from"./TrashIcon-aaaccaf2.js";import{I as ae}from"./InformationCircleIcon-d35f3488.js";let y=new U;function de({fetchSettings:v,sports:m,clubUser:j}){const[E,S]=c.useState(!1),[R,k]=c.useState(!1),{dispatch:i}=c.useContext(W),[g,p]=c.useState("add"),[P,x]=c.useState(null),[N,C]=c.useState(!1),[I,A]=c.useState([]),[G,h]=c.useState(!1),w=localStorage.getItem("role"),[l,n]=c.useState({name:"",types:[],sport_id:null,allow_cancel_reservation:1,cancel_hours_before:24});c.useEffect(()=>{if((m==null?void 0:m.length)>0){const s=m==null?void 0:m.map(a=>{var r;return{sport_id:a.id,name:a.name,club_id:a.club_id,types:((r=a.sport_types)==null?void 0:r.map(t=>({name:t.type,sub_type:t.subtype||[],club_sport_type_id:t.club_sport_type_id})))||[],status:a.status||0,allow_cancel_reservation:a.allow_cancel_reservation!==void 0?a.allow_cancel_reservation:1,cancel_hours_before:a.cancel_hours_before||24}});A(s)}else A([])},[m]);const L=async s=>{S(!0);try{y.setTable("sports"),await y.callRestAPI({id:s.sport_id,status:s.status===1?0:1},"PUT"),await v()}catch(a){console.error(a),u(i,"Failed to update sport status",3e3,"error")}finally{S(!1)}},M=async s=>{try{k(!0),y.setTable("sports"),await y.callRestAPI({id:s},"DELETE"),await v(),u(i,"Sport deleted successfully",3e3,"success")}catch(a){console.log(a),u(i,"Failed to delete sport",3e3,"error")}finally{k(!1)}},D=s=>{if(!s){p("add"),x(null),h(!1),n({name:"",types:[],sport_id:null,allow_cancel_reservation:1,cancel_hours_before:24});return}p("edit"),x(s),h(!0),n({name:s.name,sport_id:s.sport_id,types:s.types||[],allow_cancel_reservation:s.allow_cancel_reservation!==void 0?s.allow_cancel_reservation:1,cancel_hours_before:s.cancel_hours_before||24})},T=()=>{n(s=>({...s,types:[...s.types,{name:"",sub_type:[]}]}))},F=s=>{n(a=>({...a,types:a.types.filter((r,t)=>t!==s)}))},$=(s,a)=>{n(r=>({...r,types:r.types.map((t,o)=>o===s?{...t,name:a}:t)}))},O=s=>{n(a=>({...a,types:a.types.map((r,t)=>t===s?{...r,sub_type:[...r.sub_type,""]}:r)}))},H=(s,a)=>{n(r=>({...r,types:r.types.map((t,o)=>o===s?{...t,sub_type:t.sub_type.filter((d,f)=>f!==a)}:t)}))},q=(s,a,r)=>{n(t=>({...t,types:t.types.map((o,d)=>d===s?{...o,sub_type:o.sub_type.map((f,b)=>b===a?r:f)}:o)}))},K=async()=>{if(!l.name.trim()){u(i,"Sport name is required",3e3,"error");return}if(l.allow_cancel_reservation===1&&(!l.cancel_hours_before||l.cancel_hours_before<0)){u(i,"Hours before cancellation must be a positive number",3e3,"error");return}try{C(!0);const s={name:l.name,types:l.types.map(a=>({name:a.name,sub_type:a.sub_type,...a.club_sport_type_id&&{club_sport_type_id:a.club_sport_type_id}})),allow_cancel_reservation:l.allow_cancel_reservation,sport_id:l.sport_id,cancel_hours_before:l.allow_cancel_reservation===1?l.cancel_hours_before:0};g==="edit"&&(s.sport_id=P.sport_id),w=="admin"||w=="admin_staff"?await y.callRawAPI(`/v3/api/custom/courtmatchup/admin/profile-edit/${j==null?void 0:j.id}`,{sports:[s]},"POST"):await y.callRawAPI(`/v3/api/custom/courtmatchup/${w}/profile-edit`,{sports:[s]},"POST"),await v(),u(i,`Sport ${g==="edit"?"updated":"added"} successfully`,3e3,"success"),h(!1),x(null),p("add"),n({name:"",types:[],sport_id:null,allow_cancel_reservation:1,cancel_hours_before:24})}catch(s){console.error(s),u(i,s.message,3e3,"error")}finally{C(!1)}},X=[...I].sort((s,a)=>a.id-s.id),z=()=>{h(!1),x(null),p("add"),n({name:"",types:[],sport_id:null,allow_cancel_reservation:1,cancel_hours_before:24})};return V.useEffect(()=>{i({type:"SETPATH",payload:{path:"club-ui"}})},[]),e.jsx(Y,{isLoading:R||N||E,children:e.jsxs("div",{className:"flex flex-col gap-6",children:[e.jsx("div",{className:"flex justify-end",children:e.jsxs("button",{onClick:()=>{h(!0),p("add"),x(null),n({name:"",types:[],sport_id:null,allow_cancel_reservation:1,cancel_hours_before:24})},className:"flex items-center gap-2 rounded-lg bg-primaryBlue px-4 py-2 text-white transition-all hover:bg-primaryBlue/90 focus:outline-none focus:ring-2 focus:ring-primaryBlue/50 focus:ring-offset-2",children:[e.jsx(_,{className:"h-5 w-5"}),e.jsx("span",{children:"Add Sport"})]})}),e.jsxs("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3",children:[X.map(s=>{var a,r,t,o;return e.jsxs("div",{className:`relative flex h-full flex-col overflow-hidden rounded-xl border ${s.status===1?"border-primaryGreen/20 bg-white shadow-md":"border-gray-200 bg-gray-50"} transition-all duration-200 hover:shadow-lg`,children:[s.status===1&&e.jsx("div",{className:"absolute right-0 top-0 rounded-bl-lg bg-primaryGreen px-3 py-1 text-xs font-medium text-white",children:"Active"}),e.jsxs("div",{className:"flex items-center justify-between border-b border-gray-100 p-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:`flex h-10 w-10 items-center justify-center rounded-full ${s.status===1?"bg-primaryGreen/10 text-primaryGreen":"bg-gray-100 text-gray-400"}`,children:s.name.charAt(0).toUpperCase()}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-800",children:s.name}),e.jsxs("p",{className:"text-xs text-gray-500",children:[((a=s.types)==null?void 0:a.length)||0," ",((r=s.types)==null?void 0:r.length)===1?"type":"types"]})]})]}),e.jsx("div",{className:"flex items-center",children:e.jsxs("label",{className:"relative inline-flex cursor-pointer items-center",children:[e.jsx("input",{type:"checkbox",className:"peer sr-only",checked:s.status===1,onChange:()=>L(s)}),e.jsx("div",{className:"peer h-6 w-11 rounded-full bg-gray-200 after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-primaryGreen peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:ring-2 peer-focus:ring-primaryGreen/30"})]})})]}),e.jsxs("div",{className:"flex-grow p-4",children:[s.status===1&&((t=s.types)==null?void 0:t.length)>0&&e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:"mb-2 text-sm font-medium text-gray-700",children:"Types & Subtypes"}),e.jsx("div",{className:"space-y-2",children:(o=s.types)==null?void 0:o.map((d,f)=>{var b;return e.jsxs("div",{className:"rounded-lg bg-gray-50 p-3",children:[d.name&&e.jsx("p",{className:"text-sm font-medium text-gray-700",children:d.name}),((b=d.sub_type)==null?void 0:b.length)>0&&e.jsx("div",{className:"mt-1 flex flex-wrap gap-1",children:d.sub_type.map((J,Q)=>e.jsx("span",{className:"inline-flex rounded-full bg-gray-100 px-2 py-1 text-xs text-gray-600",children:J},Q))})]},d.club_sport_type_id||f)})})]}),s.status===1&&e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 text-sm font-medium text-gray-700",children:"Reservation Settings"}),e.jsxs("div",{className:"rounded-lg bg-gray-50 p-3",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`mr-2 h-2 w-2 rounded-full ${s.allow_cancel_reservation===1?"bg-green-500":"bg-red-500"}`}),e.jsxs("p",{className:"text-sm text-gray-700",children:["Cancellation:"," ",e.jsx("span",{className:"font-medium",children:s.allow_cancel_reservation===1?"Allowed":"Not allowed"})]})]}),s.allow_cancel_reservation===1&&e.jsxs("p",{className:"mt-1 text-sm text-gray-700",children:[e.jsxs("span",{className:"inline-block rounded bg-primaryGreen/10 px-2 py-0.5 text-xs font-medium text-primaryGreen",children:[s.cancel_hours_before," hours"]}),e.jsx("span",{className:"ml-1",children:"before reservation"})]})]})]})]}),e.jsxs("div",{className:"mt-auto flex border-t border-gray-100 bg-gray-50",children:[e.jsxs("button",{onClick:()=>D(s),className:"flex flex-1 items-center justify-center gap-1 border-r border-gray-200 py-3 text-sm font-medium text-gray-600 transition-colors hover:bg-gray-100",children:[e.jsx(ee,{className:"h-4 w-4"}),"Edit"]}),e.jsxs("button",{onClick:()=>M(s.sport_id),className:"flex flex-1 items-center justify-center gap-1 py-3 text-sm font-medium text-red-600 transition-colors hover:bg-red-50",children:[e.jsx(se,{className:"h-4 w-4"}),"Delete"]})]})]},s.sport_id||s.name)}),e.jsxs("div",{onClick:()=>{h(!0),p("add"),x(null),n({name:"",types:[],sport_id:null,allow_cancel_reservation:1,cancel_hours_before:24})},className:"flex h-full min-h-[250px] cursor-pointer flex-col items-center justify-center rounded-xl border-2 border-dashed border-gray-200 bg-white p-6 text-center transition-all hover:border-primaryBlue/30 hover:bg-primaryBlue/5 hover:shadow-md",children:[e.jsx("div",{className:"mb-3 rounded-full bg-primaryBlue/10 p-4",children:e.jsx(_,{className:"h-7 w-7 text-primaryBlue"})}),e.jsx("h3",{className:"text-lg font-medium text-primaryBlue",children:"Add New Sport"}),e.jsx("p",{className:"mt-2 max-w-xs text-sm text-gray-500",children:"Click to add a new sport to your club with custom types and subtypes"})]})]}),e.jsx(Z,{isOpen:G,onClose:z,title:g==="add"?"Add New Sport":"Edit Sport",showFooter:!0,primaryButtonText:N?"Saving...":"Save Sport",onPrimaryAction:K,submitting:N,children:G&&e.jsx("div",{children:e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"mb-6",children:[e.jsxs("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:["Sport name ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("input",{type:"text",className:"w-full rounded-lg border border-gray-300 px-4 py-3 text-base shadow-sm focus:border-primaryGreen focus:outline-none focus:ring-1 focus:ring-primaryGreen",value:l.name,onChange:s=>n(a=>({...a,name:s.target.value})),placeholder:"Enter sport name"})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"mb-3 flex items-center justify-between",children:[e.jsxs("label",{className:"text-sm font-medium text-gray-700",children:["Types ",e.jsx("span",{className:"text-gray-500",children:"(optional)"})]}),e.jsxs("button",{onClick:T,className:"flex items-center gap-1 rounded-md bg-primaryBlue/10 px-3 py-1 text-sm font-medium text-primaryBlue transition-colors hover:bg-primaryBlue/20",children:[e.jsx(_,{className:"h-4 w-4"}),"Add Type"]})]}),l.types.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-300 bg-gray-50 p-6 text-center",children:[e.jsx("div",{className:"mb-2 rounded-full bg-gray-100 p-2",children:e.jsx(ae,{className:"h-5 w-5 text-gray-400"})}),e.jsx("p",{className:"text-sm text-gray-500",children:"No types added yet"}),e.jsx("button",{onClick:T,className:"mt-2 text-sm font-medium text-primaryBlue hover:text-primaryBlue/80",children:"Add your first type"})]}):e.jsx("div",{className:"space-y-4",children:l.types.map((s,a)=>e.jsxs("div",{className:"rounded-lg border border-gray-200 bg-gray-50 p-4 shadow-sm",children:[e.jsxs("div",{className:"mb-3 flex items-center gap-2",children:[e.jsx("input",{type:"text",className:"w-full rounded-lg border border-gray-300 px-3 py-2 text-base shadow-sm focus:border-primaryGreen focus:outline-none focus:ring-1 focus:ring-primaryGreen",value:s.name,onChange:r=>$(a,r.target.value),placeholder:"Enter type name"}),e.jsx("button",{onClick:()=>F(a),className:"rounded-md p-1 text-gray-400 hover:bg-gray-200 hover:text-gray-600",title:"Remove type",children:e.jsx(B,{className:"h-5 w-5"})})]}),e.jsxs("div",{className:"ml-4",children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm font-medium text-gray-600",children:["Sub-types"," ",e.jsx("span",{className:"text-gray-500",children:"(optional)"})]}),e.jsxs("button",{onClick:()=>O(a),className:"flex items-center gap-1 rounded-md px-2 py-1 text-xs font-medium text-primaryBlue hover:bg-primaryBlue/10",children:[e.jsx(_,{className:"h-3 w-3"}),"Add Sub-type"]})]}),s.sub_type.length===0?e.jsx("p",{className:"text-xs italic text-gray-400",children:"No sub-types added"}):e.jsx("div",{className:"space-y-2",children:s.sub_type.map((r,t)=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"text",className:"w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm focus:border-primaryGreen focus:outline-none focus:ring-1 focus:ring-primaryGreen",value:r,onChange:o=>q(a,t,o.target.value),placeholder:"Enter sub-type name"}),e.jsx("button",{onClick:()=>H(a,t),className:"rounded-md p-1 text-gray-400 hover:bg-gray-200 hover:text-gray-600",title:"Remove sub-type",children:e.jsx(B,{className:"h-4 w-4"})})]},t))})]})]},s.club_sport_type_id||a))})]}),e.jsxs("div",{className:"rounded-lg border border-gray-200 bg-gray-50 p-4 shadow-sm",children:[e.jsx("label",{className:"mb-3 block text-sm font-medium text-gray-700",children:"Reservation Cancellation Settings"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",id:"allow-cancel-reservation",className:"h-5 w-5 rounded-md border-gray-300 text-primaryGreen focus:ring-primaryGreen",checked:l.allow_cancel_reservation===1,onChange:s=>n(a=>({...a,allow_cancel_reservation:s.target.checked?1:0}))}),e.jsx("label",{htmlFor:"allow-cancel-reservation",className:"ml-2 text-sm font-medium text-gray-700",children:"Allow users to cancel reservations"})]}),l.allow_cancel_reservation===1&&e.jsxs("div",{className:"ml-7 rounded-lg bg-white p-3",children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:"Hours before reservation when cancel button is clickable"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{type:"number",min:"1",max:"168",value:l.cancel_hours_before,onChange:s=>{const a=parseInt(s.target.value,10);isNaN(a)||a<1||a>168||n(r=>({...r,cancel_hours_before:a}))},className:"w-24 rounded-lg border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-primaryGreen focus:outline-none focus:ring-1 focus:ring-primaryGreen"}),e.jsx("span",{className:"ml-2 text-sm text-gray-500",children:"hours"})]}),e.jsx("p",{className:"mt-2 text-xs text-gray-500",children:"Users will be able to cancel reservations up to this many hours before the scheduled time."})]})]})]})]})})})]})})}export{de as S};

import{j}from"./@nivo/heatmap-ba1ecfff.js";import{b as M,r as s}from"./vendor-851db8c1.js";import{S as V}from"./SelectionOptionsCard-0d5c6ddd.js";const $=M.memo(({sports:i,onSelectionChange:w,isChildren:k=!1,initialSport:u=null,initialType:p=null,initialSubType:f=null,userPermissions:S=null})=>{const[c,z]=s.useState(u),[t,v]=s.useState(p),[h,a]=s.useState(f),[q,A]=s.useState([]),[H,x]=s.useState([]),[d,I]=s.useState(!1),[R,E]=s.useState(!1),[l,B]=s.useState(!1),m=s.useRef(u),T=s.useRef(p),N=s.useRef(f),n=M.useMemo(()=>!i||!Array.isArray(i)?[]:!(S!=null&&S.applicable_sports)||!Array.isArray(S.applicable_sports)?i:i.filter(e=>S.applicable_sports.includes(e.id)),[i,S]),b=n==null?void 0:n.find(e=>e.id===c);return s.useEffect(()=>{z(u),v(p),a(f),m.current=u,T.current=p,N.current=f,B(!0)},[u,p,f]),s.useEffect(()=>{if((n==null?void 0:n.length)>0&&l){const e=n==null?void 0:n.find(o=>o.id===u);if(e){const o=(e==null?void 0:e.sport_types)||[],y=o.filter(g=>g.type&&g.type.trim()!=="").length>0;A(o),I(y);let O=!1;if(p&&y){const g=e.sport_types.find(_=>_.type===p),C=((g==null?void 0:g.subtype)||[]).filter(_=>_&&_.trim()!=="");O=C.length>0,O&&x(C)}E(O),(u!==null||p!==null||f!==null)&&w({sport:u,type:p,subType:f,hasTypes:y,hasSubTypes:O})}}},[n,l,u,p,f,w]),s.useEffect(()=>{if(b){const e=b.sport_types||[];A(e);const o=e.filter(r=>r.type&&r.type.trim()!=="");I(o.length>0),l&&c!==m.current&&(o.length===0?v(""):v(null),a(null))}else A([]),I(!1),l&&c!==m.current&&(v(null),a(null))},[c,b,l]),s.useEffect(()=>{if(b&&t!==null){if(!d||t===""){x([]),E(!1),l&&t!==T.current&&a("");return}const e=b.sport_types.find(y=>y.type===t),r=((e==null?void 0:e.subtype)||[]).filter(y=>y&&y.trim()!=="");x(r),E(r.length>0),l&&t!==T.current&&(r.length===0?a(""):a(null))}else(!b||t===null)&&(x([]),E(!1),l&&t!==T.current&&a(null))},[t,b,d,l]),s.useEffect(()=>{if(l){const e=c!==m.current,o=t!==T.current,r=h!==N.current;(e||o||r)&&(w({sport:c,type:t,subType:h,hasTypes:d,hasSubTypes:R}),m.current=c,T.current=t,N.current=h)}},[c,t,h,d,R,l]),console.log("SportTypeSelection Debug:",{hasTypes:d,selectedType:t,hasSubTypes:R,availableSubTypes:H.length,selectedSubType:h,isInitialized:l}),j.jsxs("div",{className:`h-fit w-full space-y-6 rounded-lg bg-white ${k?"p-0 shadow-none":"p-4 shadow-5"}`,children:[j.jsx(V,{title:"Sports",options:n||[],selectedOption:c,onOptionSelect:z,emptyMessage:"No sports found",optionType:"sport"}),d&&j.jsx(V,{title:"Type",options:q,selectedOption:t,onOptionSelect:v,showPlaceholder:!c,optionType:"type"}),d&&t&&R&&H.length>0&&j.jsx(V,{title:"Sub-Type",options:H,selectedOption:h,onOptionSelect:e=>{console.log("Subtype selected:",e),a(e)},showPlaceholder:!t,optionType:"subtype"})]})});$.displayName="SportTypeSelection";const L=$;export{L as S};

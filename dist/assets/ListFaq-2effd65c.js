import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{b as s,f as V,r as g}from"./vendor-851db8c1.js";import{M as W,T as I,G as J,A as U,c as X,E as Y,R as j,t as Z,i as ee}from"./index-f915b394.js";import{c as te,a as c}from"./yup-54691517.js";import{u as se}from"./react-hook-form-687afde5.js";import{o as ae}from"./yup-2824f222.js";import{P as oe}from"./index-eb1bc208.js";import"./lodash-91d5d207.js";import ne from"./Skeleton-1e8bf077.js";import{L as re}from"./LoadingOverlay-87926629.js";import{D as ie}from"./DataTable-a2248415.js";import{A as le,F as ce}from"./FaqDetails-ee3d1e52.js";import{H as de}from"./HistoryComponent-a235a60c.js";let pe=new W,me=new I;const fe=[{header:"Subcategory",accessor:"subcategory_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Answer",accessor:"answer",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],ge=({club:o})=>{const{dispatch:v,state:ue}=s.useContext(J),{dispatch:b}=s.useContext(U),[C,N]=s.useState([]),[n,u]=s.useState(10),[F,w]=s.useState(0),[xe,A]=s.useState(0),[d,D]=s.useState(1),[_,q]=s.useState(!1),[P,E]=s.useState(!1),[he,k]=s.useState(!1),[p,m]=s.useState(!0),[L,ye]=s.useState(!1),[T,Se]=s.useState(!1);s.useState(),V();const x=s.useRef(null),[M,h]=s.useState(!1),[R,O]=s.useState(null),[$,f]=g.useState(!1);g.useState([]),g.useState([]);const z=te({id:c(),email:c(),role:c(),status:c()});se({resolver:ae(z)});function H(){r(d-1,n)}function Q(){r(d+1,n)}async function r(e,i,S={},l=[]){m(!(T||L));try{const a=await me.getPaginate("faq",{page:e,limit:i,filter:[...l,`club_id,cs,${o==null?void 0:o.id}`],join:["faq_subcategory|subcategory_id",`${pe._project_id}_faq_category|faq_subcategory.category_id,id`],size:n});a&&(m(!1),N(a.list),u(a.limit),w(a.num_pages),D(a.page),A(a.total),q(a.page>1),E(a.page+1<=a.num_pages))}catch(a){m(!1),console.log("ERROR",a),Z(b,a.message)}}s.useEffect(()=>{v({type:"SETPATH",payload:{path:"faq"}}),o!=null&&o.id&&r(1,n,{})},[o==null?void 0:o.id]);const y=e=>{x.current&&!x.current.contains(e.target)&&k(!1)};s.useEffect(()=>(document.addEventListener("mousedown",y),()=>{document.removeEventListener("mousedown",y)}),[]);const B=e=>{O(e),h(!0)},G={"":e=>t.jsx("div",{className:"flex items-center gap-3",children:t.jsx("button",{className:"rounded-full p-2 hover:bg-gray-100",onClick:()=>B(e),children:t.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M15.2083 12.7077V4.79102M15.2083 4.79102H7.29167M15.2083 4.79102L5 14.9993",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})})}),type:e=>{var i;return t.jsx("span",{className:"capitalize",children:((i=ee.find(S=>{var l;return S.value==((l=e==null?void 0:e.booking)==null?void 0:l.reservation_type)}))==null?void 0:i.label)||"--"})},question:e=>t.jsx("span",{className:"capitalize",children:(e==null?void 0:e.faq_subcategory.name)||"--"}),answer:e=>t.jsx(t.Fragment,{children:(e==null?void 0:e.answer)||"--"}),category:e=>t.jsx(t.Fragment,{children:(e==null?void 0:e.faq_category.name)||"--"}),subcategory_id:e=>t.jsx(t.Fragment,{children:(e==null?void 0:e.faq_subcategory.name)||"--"})},K=()=>{r(1,n)};return t.jsxs("div",{className:"h-screen px-8",children:[p&&t.jsx(re,{}),t.jsxs("div",{className:"flex flex-col justify-between gap-4 py-3 md:flex-row md:items-center",children:[t.jsx("div",{className:"flex items-center gap-4",children:t.jsxs("form",{className:"relative flex flex-1 items-center",children:[t.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:t.jsx(X,{className:"text-gray-500"})}),t.jsx("input",{type:"text",className:"block w-full rounded-lg border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Search FAQ by answer",onChange:e=>{const i=e.target.value.trim();i?r(1,n,{},[`answer,cs,${i}`]):r(1,n)}})]})}),t.jsxs("div",{className:"flex items-center gap-4",children:[t.jsxs("button",{onClick:()=>f(!0),className:"inline-flex max-w-fit items-center gap-2 rounded-lg bg-[#1D275F] px-4 py-2 text-sm font-semibold text-white hover:bg-blue-700",children:[t.jsx("span",{children:"+"}),"Add new"]}),t.jsx(de,{activityType:Y.faq,emptyMessage:"No FAQ history found",title:"FAQ History"})]})]}),t.jsx(j,{isOpen:$,onClose:()=>f(!1),title:"Add New FAQ",showFooter:!1,children:t.jsx(le,{onClose:()=>f(!1),onSuccess:K,club:o})}),t.jsx(j,{isOpen:M,onClose:()=>h(!1),title:"FAQ Details",showFooter:!1,children:t.jsx(ce,{faq:R})}),p?t.jsx(ne,{}):t.jsx("div",{className:"overflow-x-auto",children:t.jsx(ie,{columns:fe,data:C,loading:p,renderCustomCell:G,rowClassName:"hover:bg-gray-40 bg-gray-100 px-4 py-3 text-gray-500",cellClassName:"whitespace-nowrap px-6 py-4",headerClassName:"px-6 py-4 text-left text-sm font-medium text-gray-500",emptyMessage:"No data available"})}),t.jsx(oe,{currentPage:d,pageCount:F,pageSize:n,canPreviousPage:_,canNextPage:P,updatePageSize:e=>{u(e),r(1,e)},previousPage:H,nextPage:Q,gotoPage:e=>r(e,n)})]})},ke=ge;export{ke as L};

const R=Symbol.for("constructDateFrom");function g(n,e){return typeof n=="function"?n(e):n&&typeof n=="object"&&R in n?n[R](e):n instanceof Date?new n.constructor(e):new Date(e)}function l(n,e){return g(e||n,n)}function U(n,e,t){const r=l(n,t==null?void 0:t.in);return isNaN(e)?g((t==null?void 0:t.in)||n,NaN):(e&&r.setDate(r.getDate()+e),r)}function K(n,e,t){const r=l(n,t==null?void 0:t.in);if(isNaN(e))return g((t==null?void 0:t.in)||n,NaN);if(!e)return r;const a=r.getDate(),i=g((t==null?void 0:t.in)||n,r.getTime());i.setMonth(r.getMonth()+e+1,0);const s=i.getDate();return a>=s?i:(r.setFullYear(i.getFullYear(),i.getMonth(),a),r)}let p={};function P(){return p}function Y(n,e){var u,o,c,f;const t=P(),r=(e==null?void 0:e.weekStartsOn)??((o=(u=e==null?void 0:e.locale)==null?void 0:u.options)==null?void 0:o.weekStartsOn)??t.weekStartsOn??((f=(c=t.locale)==null?void 0:c.options)==null?void 0:f.weekStartsOn)??0,a=l(n,e==null?void 0:e.in),i=a.getDay(),s=(i<r?7:0)+i-r;return a.setDate(a.getDate()-s),a.setHours(0,0,0,0),a}function C(n,e){return Y(n,{...e,weekStartsOn:1})}function B(n,e){const t=l(n,e==null?void 0:e.in),r=t.getFullYear(),a=g(t,0);a.setFullYear(r+1,0,4),a.setHours(0,0,0,0);const i=C(a),s=g(t,0);s.setFullYear(r,0,4),s.setHours(0,0,0,0);const u=C(s);return t.getTime()>=i.getTime()?r+1:t.getTime()>=u.getTime()?r:r-1}function I(n){const e=l(n),t=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return t.setUTCFullYear(e.getFullYear()),+n-+t}function v(n,...e){const t=g.bind(null,n||e.find(r=>typeof r=="object"));return e.map(t)}function E(n,e){const t=l(n,e==null?void 0:e.in);return t.setHours(0,0,0,0),t}function ee(n,e,t){const[r,a]=v(t==null?void 0:t.in,n,e),i=E(r),s=E(a),u=+i-I(i),o=+s-I(s);return Math.round((u-o)/864e5)}function te(n,e){const t=B(n,e),r=g((e==null?void 0:e.in)||n,0);return r.setFullYear(t,0,4),r.setHours(0,0,0,0),C(r)}function ne(n,e,t){return U(n,e*7,t)}function F(n,e){const t=+l(n)-+l(e);return t<0?-1:t>0?1:t}function V(n){return g(n,Date.now())}function re(n,e,t){const[r,a]=v(t==null?void 0:t.in,n,e);return+E(r)==+E(a)}function ae(n){return n instanceof Date||typeof n=="object"&&Object.prototype.toString.call(n)==="[object Date]"}function ie(n){return!(!ae(n)&&typeof n!="number"||isNaN(+l(n)))}function ue(n,e,t){const[r,a]=v(t==null?void 0:t.in,n,e),i=r.getFullYear()-a.getFullYear(),s=r.getMonth()-a.getMonth();return i*12+s}function se(n){return e=>{const r=(n?Math[n]:Math.trunc)(e);return r===0?0:r}}function ce(n,e){return+l(n)-+l(e)}function oe(n,e){const t=l(n,e==null?void 0:e.in);return t.setHours(23,59,59,999),t}function de(n,e){const t=l(n,e==null?void 0:e.in),r=t.getMonth();return t.setFullYear(t.getFullYear(),r+1,0),t.setHours(23,59,59,999),t}function fe(n,e){const t=l(n,e==null?void 0:e.in);return+oe(t,e)==+de(t,e)}function le(n,e,t){const[r,a,i]=v(t==null?void 0:t.in,n,n,e),s=F(a,i),u=Math.abs(ue(a,i));if(u<1)return 0;a.getMonth()===1&&a.getDate()>27&&a.setDate(30),a.setMonth(a.getMonth()-s*u);let o=F(a,i)===-s;fe(r)&&u===1&&F(r,i)===1&&(o=!1);const c=s*(u-+o);return c===0?0:c}function he(n,e,t){const r=ce(n,e)/1e3;return se(t==null?void 0:t.roundingMethod)(r)}function Yt(n,e){const t=l(n,e==null?void 0:e.in);return t.setDate(1),t.setHours(0,0,0,0),t}function me(n,e){const t=l(n,e==null?void 0:e.in);return t.setFullYear(t.getFullYear(),0,1),t.setHours(0,0,0,0),t}function vt(n,e){var u,o,c,f;const t=P(),r=(e==null?void 0:e.weekStartsOn)??((o=(u=e==null?void 0:e.locale)==null?void 0:u.options)==null?void 0:o.weekStartsOn)??t.weekStartsOn??((f=(c=t.locale)==null?void 0:c.options)==null?void 0:f.weekStartsOn)??0,a=l(n,e==null?void 0:e.in),i=a.getDay(),s=(i<r?-7:0)+6-(i-r);return a.setDate(a.getDate()+s),a.setHours(23,59,59,999),a}const ge={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},we=(n,e,t)=>{let r;const a=ge[n];return typeof a=="string"?r=a:e===1?r=a.one:r=a.other.replace("{{count}}",e.toString()),t!=null&&t.addSuffix?t.comparison&&t.comparison>0?"in "+r:r+" ago":r};function H(n){return(e={})=>{const t=e.width?String(e.width):n.defaultWidth;return n.formats[t]||n.formats[n.defaultWidth]}}const ye={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},be={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},De={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},Me={date:H({formats:ye,defaultWidth:"full"}),time:H({formats:be,defaultWidth:"full"}),dateTime:H({formats:De,defaultWidth:"full"})},Oe={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},ke=(n,e,t,r)=>Oe[n];function S(n){return(e,t)=>{const r=t!=null&&t.context?String(t.context):"standalone";let a;if(r==="formatting"&&n.formattingValues){const s=n.defaultFormattingWidth||n.defaultWidth,u=t!=null&&t.width?String(t.width):s;a=n.formattingValues[u]||n.formattingValues[s]}else{const s=n.defaultWidth,u=t!=null&&t.width?String(t.width):n.defaultWidth;a=n.values[u]||n.values[s]}const i=n.argumentCallback?n.argumentCallback(e):e;return a[i]}}const xe={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},Pe={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},Se={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},Te={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},We={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},Ye={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},ve=(n,e)=>{const t=Number(n),r=t%100;if(r>20||r<10)switch(r%10){case 1:return t+"st";case 2:return t+"nd";case 3:return t+"rd"}return t+"th"},Ne={ordinalNumber:ve,era:S({values:xe,defaultWidth:"wide"}),quarter:S({values:Pe,defaultWidth:"wide",argumentCallback:n=>n-1}),month:S({values:Se,defaultWidth:"wide"}),day:S({values:Te,defaultWidth:"wide"}),dayPeriod:S({values:We,defaultWidth:"wide",formattingValues:Ye,defaultFormattingWidth:"wide"})};function T(n){return(e,t={})=>{const r=t.width,a=r&&n.matchPatterns[r]||n.matchPatterns[n.defaultMatchWidth],i=e.match(a);if(!i)return null;const s=i[0],u=r&&n.parsePatterns[r]||n.parsePatterns[n.defaultParseWidth],o=Array.isArray(u)?Ce(u,b=>b.test(s)):Fe(u,b=>b.test(s));let c;c=n.valueCallback?n.valueCallback(o):o,c=t.valueCallback?t.valueCallback(c):c;const f=e.slice(s.length);return{value:c,rest:f}}}function Fe(n,e){for(const t in n)if(Object.prototype.hasOwnProperty.call(n,t)&&e(n[t]))return t}function Ce(n,e){for(let t=0;t<n.length;t++)if(e(n[t]))return t}function Ie(n){return(e,t={})=>{const r=e.match(n.matchPattern);if(!r)return null;const a=r[0],i=e.match(n.parsePattern);if(!i)return null;let s=n.valueCallback?n.valueCallback(i[0]):i[0];s=t.valueCallback?t.valueCallback(s):s;const u=e.slice(a.length);return{value:s,rest:u}}}const Ee=/^(\d+)(th|st|nd|rd)?/i,_e=/\d+/i,He={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},qe={any:[/^b/i,/^(a|c)/i]},Xe={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},Le={any:[/1/i,/2/i,/3/i,/4/i]},Re={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},Qe={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},je={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},Ae={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},Ge={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},Ue={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},Be={ordinalNumber:Ie({matchPattern:Ee,parsePattern:_e,valueCallback:n=>parseInt(n,10)}),era:T({matchPatterns:He,defaultMatchWidth:"wide",parsePatterns:qe,defaultParseWidth:"any"}),quarter:T({matchPatterns:Xe,defaultMatchWidth:"wide",parsePatterns:Le,defaultParseWidth:"any",valueCallback:n=>n+1}),month:T({matchPatterns:Re,defaultMatchWidth:"wide",parsePatterns:Qe,defaultParseWidth:"any"}),day:T({matchPatterns:je,defaultMatchWidth:"wide",parsePatterns:Ae,defaultParseWidth:"any"}),dayPeriod:T({matchPatterns:Ge,defaultMatchWidth:"any",parsePatterns:Ue,defaultParseWidth:"any"})},$={code:"en-US",formatDistance:we,formatLong:Me,formatRelative:ke,localize:Ne,match:Be,options:{weekStartsOn:0,firstWeekContainsDate:1}};function Ve(n,e){const t=l(n,e==null?void 0:e.in);return ee(t,me(t))+1}function $e(n,e){const t=l(n,e==null?void 0:e.in),r=+C(t)-+te(t);return Math.round(r/6048e5)+1}function z(n,e){var f,b,h,y;const t=l(n,e==null?void 0:e.in),r=t.getFullYear(),a=P(),i=(e==null?void 0:e.firstWeekContainsDate)??((b=(f=e==null?void 0:e.locale)==null?void 0:f.options)==null?void 0:b.firstWeekContainsDate)??a.firstWeekContainsDate??((y=(h=a.locale)==null?void 0:h.options)==null?void 0:y.firstWeekContainsDate)??1,s=g((e==null?void 0:e.in)||n,0);s.setFullYear(r+1,0,i),s.setHours(0,0,0,0);const u=Y(s,e),o=g((e==null?void 0:e.in)||n,0);o.setFullYear(r,0,i),o.setHours(0,0,0,0);const c=Y(o,e);return+t>=+u?r+1:+t>=+c?r:r-1}function ze(n,e){var u,o,c,f;const t=P(),r=(e==null?void 0:e.firstWeekContainsDate)??((o=(u=e==null?void 0:e.locale)==null?void 0:u.options)==null?void 0:o.firstWeekContainsDate)??t.firstWeekContainsDate??((f=(c=t.locale)==null?void 0:c.options)==null?void 0:f.firstWeekContainsDate)??1,a=z(n,e),i=g((e==null?void 0:e.in)||n,0);return i.setFullYear(a,0,r),i.setHours(0,0,0,0),Y(i,e)}function Je(n,e){const t=l(n,e==null?void 0:e.in),r=+Y(t,e)-+ze(t,e);return Math.round(r/6048e5)+1}function d(n,e){const t=n<0?"-":"",r=Math.abs(n).toString().padStart(e,"0");return t+r}const M={y(n,e){const t=n.getFullYear(),r=t>0?t:1-t;return d(e==="yy"?r%100:r,e.length)},M(n,e){const t=n.getMonth();return e==="M"?String(t+1):d(t+1,2)},d(n,e){return d(n.getDate(),e.length)},a(n,e){const t=n.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return t.toUpperCase();case"aaa":return t;case"aaaaa":return t[0];case"aaaa":default:return t==="am"?"a.m.":"p.m."}},h(n,e){return d(n.getHours()%12||12,e.length)},H(n,e){return d(n.getHours(),e.length)},m(n,e){return d(n.getMinutes(),e.length)},s(n,e){return d(n.getSeconds(),e.length)},S(n,e){const t=e.length,r=n.getMilliseconds(),a=Math.trunc(r*Math.pow(10,t-3));return d(a,e.length)}},x={am:"am",pm:"pm",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},Q={G:function(n,e,t){const r=n.getFullYear()>0?1:0;switch(e){case"G":case"GG":case"GGG":return t.era(r,{width:"abbreviated"});case"GGGGG":return t.era(r,{width:"narrow"});case"GGGG":default:return t.era(r,{width:"wide"})}},y:function(n,e,t){if(e==="yo"){const r=n.getFullYear(),a=r>0?r:1-r;return t.ordinalNumber(a,{unit:"year"})}return M.y(n,e)},Y:function(n,e,t,r){const a=z(n,r),i=a>0?a:1-a;if(e==="YY"){const s=i%100;return d(s,2)}return e==="Yo"?t.ordinalNumber(i,{unit:"year"}):d(i,e.length)},R:function(n,e){const t=B(n);return d(t,e.length)},u:function(n,e){const t=n.getFullYear();return d(t,e.length)},Q:function(n,e,t){const r=Math.ceil((n.getMonth()+1)/3);switch(e){case"Q":return String(r);case"QQ":return d(r,2);case"Qo":return t.ordinalNumber(r,{unit:"quarter"});case"QQQ":return t.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return t.quarter(r,{width:"narrow",context:"formatting"});case"QQQQ":default:return t.quarter(r,{width:"wide",context:"formatting"})}},q:function(n,e,t){const r=Math.ceil((n.getMonth()+1)/3);switch(e){case"q":return String(r);case"qq":return d(r,2);case"qo":return t.ordinalNumber(r,{unit:"quarter"});case"qqq":return t.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return t.quarter(r,{width:"narrow",context:"standalone"});case"qqqq":default:return t.quarter(r,{width:"wide",context:"standalone"})}},M:function(n,e,t){const r=n.getMonth();switch(e){case"M":case"MM":return M.M(n,e);case"Mo":return t.ordinalNumber(r+1,{unit:"month"});case"MMM":return t.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return t.month(r,{width:"narrow",context:"formatting"});case"MMMM":default:return t.month(r,{width:"wide",context:"formatting"})}},L:function(n,e,t){const r=n.getMonth();switch(e){case"L":return String(r+1);case"LL":return d(r+1,2);case"Lo":return t.ordinalNumber(r+1,{unit:"month"});case"LLL":return t.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return t.month(r,{width:"narrow",context:"standalone"});case"LLLL":default:return t.month(r,{width:"wide",context:"standalone"})}},w:function(n,e,t,r){const a=Je(n,r);return e==="wo"?t.ordinalNumber(a,{unit:"week"}):d(a,e.length)},I:function(n,e,t){const r=$e(n);return e==="Io"?t.ordinalNumber(r,{unit:"week"}):d(r,e.length)},d:function(n,e,t){return e==="do"?t.ordinalNumber(n.getDate(),{unit:"date"}):M.d(n,e)},D:function(n,e,t){const r=Ve(n);return e==="Do"?t.ordinalNumber(r,{unit:"dayOfYear"}):d(r,e.length)},E:function(n,e,t){const r=n.getDay();switch(e){case"E":case"EE":case"EEE":return t.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return t.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return t.day(r,{width:"short",context:"formatting"});case"EEEE":default:return t.day(r,{width:"wide",context:"formatting"})}},e:function(n,e,t,r){const a=n.getDay(),i=(a-r.weekStartsOn+8)%7||7;switch(e){case"e":return String(i);case"ee":return d(i,2);case"eo":return t.ordinalNumber(i,{unit:"day"});case"eee":return t.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return t.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return t.day(a,{width:"short",context:"formatting"});case"eeee":default:return t.day(a,{width:"wide",context:"formatting"})}},c:function(n,e,t,r){const a=n.getDay(),i=(a-r.weekStartsOn+8)%7||7;switch(e){case"c":return String(i);case"cc":return d(i,e.length);case"co":return t.ordinalNumber(i,{unit:"day"});case"ccc":return t.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return t.day(a,{width:"narrow",context:"standalone"});case"cccccc":return t.day(a,{width:"short",context:"standalone"});case"cccc":default:return t.day(a,{width:"wide",context:"standalone"})}},i:function(n,e,t){const r=n.getDay(),a=r===0?7:r;switch(e){case"i":return String(a);case"ii":return d(a,e.length);case"io":return t.ordinalNumber(a,{unit:"day"});case"iii":return t.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return t.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return t.day(r,{width:"short",context:"formatting"});case"iiii":default:return t.day(r,{width:"wide",context:"formatting"})}},a:function(n,e,t){const a=n.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return t.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"aaa":return t.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return t.dayPeriod(a,{width:"narrow",context:"formatting"});case"aaaa":default:return t.dayPeriod(a,{width:"wide",context:"formatting"})}},b:function(n,e,t){const r=n.getHours();let a;switch(r===12?a=x.noon:r===0?a=x.midnight:a=r/12>=1?"pm":"am",e){case"b":case"bb":return t.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"bbb":return t.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return t.dayPeriod(a,{width:"narrow",context:"formatting"});case"bbbb":default:return t.dayPeriod(a,{width:"wide",context:"formatting"})}},B:function(n,e,t){const r=n.getHours();let a;switch(r>=17?a=x.evening:r>=12?a=x.afternoon:r>=4?a=x.morning:a=x.night,e){case"B":case"BB":case"BBB":return t.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"BBBBB":return t.dayPeriod(a,{width:"narrow",context:"formatting"});case"BBBB":default:return t.dayPeriod(a,{width:"wide",context:"formatting"})}},h:function(n,e,t){if(e==="ho"){let r=n.getHours()%12;return r===0&&(r=12),t.ordinalNumber(r,{unit:"hour"})}return M.h(n,e)},H:function(n,e,t){return e==="Ho"?t.ordinalNumber(n.getHours(),{unit:"hour"}):M.H(n,e)},K:function(n,e,t){const r=n.getHours()%12;return e==="Ko"?t.ordinalNumber(r,{unit:"hour"}):d(r,e.length)},k:function(n,e,t){let r=n.getHours();return r===0&&(r=24),e==="ko"?t.ordinalNumber(r,{unit:"hour"}):d(r,e.length)},m:function(n,e,t){return e==="mo"?t.ordinalNumber(n.getMinutes(),{unit:"minute"}):M.m(n,e)},s:function(n,e,t){return e==="so"?t.ordinalNumber(n.getSeconds(),{unit:"second"}):M.s(n,e)},S:function(n,e){return M.S(n,e)},X:function(n,e,t){const r=n.getTimezoneOffset();if(r===0)return"Z";switch(e){case"X":return A(r);case"XXXX":case"XX":return O(r);case"XXXXX":case"XXX":default:return O(r,":")}},x:function(n,e,t){const r=n.getTimezoneOffset();switch(e){case"x":return A(r);case"xxxx":case"xx":return O(r);case"xxxxx":case"xxx":default:return O(r,":")}},O:function(n,e,t){const r=n.getTimezoneOffset();switch(e){case"O":case"OO":case"OOO":return"GMT"+j(r,":");case"OOOO":default:return"GMT"+O(r,":")}},z:function(n,e,t){const r=n.getTimezoneOffset();switch(e){case"z":case"zz":case"zzz":return"GMT"+j(r,":");case"zzzz":default:return"GMT"+O(r,":")}},t:function(n,e,t){const r=Math.trunc(+n/1e3);return d(r,e.length)},T:function(n,e,t){return d(+n,e.length)}};function j(n,e=""){const t=n>0?"-":"+",r=Math.abs(n),a=Math.trunc(r/60),i=r%60;return i===0?t+String(a):t+String(a)+e+d(i,2)}function A(n,e){return n%60===0?(n>0?"-":"+")+d(Math.abs(n)/60,2):O(n,e)}function O(n,e=""){const t=n>0?"-":"+",r=Math.abs(n),a=d(Math.trunc(r/60),2),i=d(r%60,2);return t+a+e+i}const G=(n,e)=>{switch(n){case"P":return e.date({width:"short"});case"PP":return e.date({width:"medium"});case"PPP":return e.date({width:"long"});case"PPPP":default:return e.date({width:"full"})}},J=(n,e)=>{switch(n){case"p":return e.time({width:"short"});case"pp":return e.time({width:"medium"});case"ppp":return e.time({width:"long"});case"pppp":default:return e.time({width:"full"})}},Ze=(n,e)=>{const t=n.match(/(P+)(p+)?/)||[],r=t[1],a=t[2];if(!a)return G(n,e);let i;switch(r){case"P":i=e.dateTime({width:"short"});break;case"PP":i=e.dateTime({width:"medium"});break;case"PPP":i=e.dateTime({width:"long"});break;case"PPPP":default:i=e.dateTime({width:"full"});break}return i.replace("{{date}}",G(r,e)).replace("{{time}}",J(a,e))},Ke={p:J,P:Ze},pe=/^D+$/,et=/^Y+$/,tt=["D","DD","YY","YYYY"];function nt(n){return pe.test(n)}function rt(n){return et.test(n)}function at(n,e,t){const r=it(n,e,t);if(console.warn(r),tt.includes(n))throw new RangeError(r)}function it(n,e,t){const r=n[0]==="Y"?"years":"days of the month";return`Use \`${n.toLowerCase()}\` instead of \`${n}\` (in \`${e}\`) for formatting ${r} to the input \`${t}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}const ut=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,st=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,ct=/^'([^]*?)'?$/,ot=/''/g,dt=/[a-zA-Z]/;function Nt(n,e,t){var f,b,h,y,D,k,X,L;const r=P(),a=(t==null?void 0:t.locale)??r.locale??$,i=(t==null?void 0:t.firstWeekContainsDate)??((b=(f=t==null?void 0:t.locale)==null?void 0:f.options)==null?void 0:b.firstWeekContainsDate)??r.firstWeekContainsDate??((y=(h=r.locale)==null?void 0:h.options)==null?void 0:y.firstWeekContainsDate)??1,s=(t==null?void 0:t.weekStartsOn)??((k=(D=t==null?void 0:t.locale)==null?void 0:D.options)==null?void 0:k.weekStartsOn)??r.weekStartsOn??((L=(X=r.locale)==null?void 0:X.options)==null?void 0:L.weekStartsOn)??0,u=l(n,t==null?void 0:t.in);if(!ie(u))throw new RangeError("Invalid time value");let o=e.match(st).map(w=>{const m=w[0];if(m==="p"||m==="P"){const _=Ke[m];return _(w,a.formatLong)}return w}).join("").match(ut).map(w=>{if(w==="''")return{isToken:!1,value:"'"};const m=w[0];if(m==="'")return{isToken:!1,value:ft(w)};if(Q[m])return{isToken:!0,value:w};if(m.match(dt))throw new RangeError("Format string contains an unescaped latin alphabet character `"+m+"`");return{isToken:!1,value:w}});a.localize.preprocessor&&(o=a.localize.preprocessor(u,o));const c={firstWeekContainsDate:i,weekStartsOn:s,locale:a};return o.map(w=>{if(!w.isToken)return w.value;const m=w.value;(!(t!=null&&t.useAdditionalWeekYearTokens)&&rt(m)||!(t!=null&&t.useAdditionalDayOfYearTokens)&&nt(m))&&at(m,e,String(n));const _=Q[m[0]];return _(u,m,a.localize,c)}).join("")}function ft(n){const e=n.match(ct);return e?e[1].replace(ot,"'"):n}function lt(n,e,t){const r=P(),a=(t==null?void 0:t.locale)??r.locale??$,i=2520,s=F(n,e);if(isNaN(s))throw new RangeError("Invalid time value");const u=Object.assign({},t,{addSuffix:t==null?void 0:t.addSuffix,comparison:s}),[o,c]=v(t==null?void 0:t.in,...s>0?[e,n]:[n,e]),f=he(c,o),b=(I(c)-I(o))/1e3,h=Math.round((f-b)/60);let y;if(h<2)return t!=null&&t.includeSeconds?f<5?a.formatDistance("lessThanXSeconds",5,u):f<10?a.formatDistance("lessThanXSeconds",10,u):f<20?a.formatDistance("lessThanXSeconds",20,u):f<40?a.formatDistance("halfAMinute",0,u):f<60?a.formatDistance("lessThanXMinutes",1,u):a.formatDistance("xMinutes",1,u):h===0?a.formatDistance("lessThanXMinutes",1,u):a.formatDistance("xMinutes",h,u);if(h<45)return a.formatDistance("xMinutes",h,u);if(h<90)return a.formatDistance("aboutXHours",1,u);if(h<1440){const D=Math.round(h/60);return a.formatDistance("aboutXHours",D,u)}else{if(h<i)return a.formatDistance("xDays",1,u);if(h<43200){const D=Math.round(h/1440);return a.formatDistance("xDays",D,u)}else if(h<43200*2)return y=Math.round(h/43200),a.formatDistance("aboutXMonths",y,u)}if(y=le(c,o),y<12){const D=Math.round(h/43200);return a.formatDistance("xMonths",D,u)}else{const D=y%12,k=Math.trunc(y/12);return D<3?a.formatDistance("aboutXYears",k,u):D<9?a.formatDistance("overXYears",k,u):a.formatDistance("almostXYears",k+1,u)}}function Ft(n,e){return lt(n,V(n),e)}function Ct(n,e){return+l(n)>+l(e)}function It(n,e){return re(g((e==null?void 0:e.in)||n,n),V((e==null?void 0:e.in)||n))}function Et(n,e,t){return U(n,-e,t)}function _t(n,e){const t=()=>g(e==null?void 0:e.in,NaN),r=(e==null?void 0:e.additionalDigits)??2,a=wt(n);let i;if(a.date){const c=yt(a.date,r);i=bt(c.restDateString,c.year)}if(!i||isNaN(+i))return t();const s=+i;let u=0,o;if(a.time&&(u=Dt(a.time),isNaN(u)))return t();if(a.timezone){if(o=Mt(a.timezone),isNaN(o))return t()}else{const c=new Date(s+u),f=l(0,e==null?void 0:e.in);return f.setFullYear(c.getUTCFullYear(),c.getUTCMonth(),c.getUTCDate()),f.setHours(c.getUTCHours(),c.getUTCMinutes(),c.getUTCSeconds(),c.getUTCMilliseconds()),f}return l(s+u+o,e==null?void 0:e.in)}const N={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},ht=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,mt=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,gt=/^([+-])(\d{2})(?::?(\d{2}))?$/;function wt(n){const e={},t=n.split(N.dateTimeDelimiter);let r;if(t.length>2)return e;if(/:/.test(t[0])?r=t[0]:(e.date=t[0],r=t[1],N.timeZoneDelimiter.test(e.date)&&(e.date=n.split(N.timeZoneDelimiter)[0],r=n.substr(e.date.length,n.length))),r){const a=N.timezone.exec(r);a?(e.time=r.replace(a[1],""),e.timezone=a[1]):e.time=r}return e}function yt(n,e){const t=new RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+e)+"})|(\\d{2}|[+-]\\d{"+(2+e)+"})$)"),r=n.match(t);if(!r)return{year:NaN,restDateString:""};const a=r[1]?parseInt(r[1]):null,i=r[2]?parseInt(r[2]):null;return{year:i===null?a:i*100,restDateString:n.slice((r[1]||r[2]).length)}}function bt(n,e){if(e===null)return new Date(NaN);const t=n.match(ht);if(!t)return new Date(NaN);const r=!!t[4],a=W(t[1]),i=W(t[2])-1,s=W(t[3]),u=W(t[4]),o=W(t[5])-1;if(r)return St(e,u,o)?Ot(e,u,o):new Date(NaN);{const c=new Date(0);return!xt(e,i,s)||!Pt(e,a)?new Date(NaN):(c.setUTCFullYear(e,i,Math.max(a,s)),c)}}function W(n){return n?parseInt(n):1}function Dt(n){const e=n.match(mt);if(!e)return NaN;const t=q(e[1]),r=q(e[2]),a=q(e[3]);return Tt(t,r,a)?t*36e5+r*6e4+a*1e3:NaN}function q(n){return n&&parseFloat(n.replace(",","."))||0}function Mt(n){if(n==="Z")return 0;const e=n.match(gt);if(!e)return 0;const t=e[1]==="+"?-1:1,r=parseInt(e[2]),a=e[3]&&parseInt(e[3])||0;return Wt(r,a)?t*(r*36e5+a*6e4):NaN}function Ot(n,e,t){const r=new Date(0);r.setUTCFullYear(n,0,4);const a=r.getUTCDay()||7,i=(e-1)*7+t+1-a;return r.setUTCDate(r.getUTCDate()+i),r}const kt=[31,null,31,30,31,30,31,31,30,31,30,31];function Z(n){return n%400===0||n%4===0&&n%100!==0}function xt(n,e,t){return e>=0&&e<=11&&t>=1&&t<=(kt[e]||(Z(n)?29:28))}function Pt(n,e){return e>=1&&e<=(Z(n)?366:365)}function St(n,e,t){return e>=1&&e<=53&&t>=0&&t<=6}function Tt(n,e,t){return n===24?e===0&&t===0:t>=0&&t<60&&e>=0&&e<60&&n>=0&&n<25}function Wt(n,e){return e>=0&&e<=59}function Ht(n,e,t){return K(n,-e,t)}function qt(n,e,t){return ne(n,-e,t)}export{U as a,qt as b,ne as c,K as d,Yt as e,Nt as f,Ft as g,E as h,re as i,Ct as j,me as k,Ht as l,Et as m,vt as n,It as o,_t as p,Y as s};

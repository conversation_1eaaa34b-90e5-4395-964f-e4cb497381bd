import{j as a}from"./@nivo/heatmap-ba1ecfff.js";import"./vendor-851db8c1.js";import{M as z}from"./react-tooltip-7a26650a.js";import{h as r}from"./moment-a9aaa855.js";import{C as P}from"./ChevronLeftIcon-e5eecf9c.js";import{C as R}from"./ChevronRightIcon-efb4c46c.js";const q=({currentMonth:s,selectedDate:n,selectedEndDate:i,onDateSelect:f,onEndDateSelect:g,onPreviousMonth:C,onNextMonth:v,daysOff:N=[],coachAvailability:p=null,bookings:c=[],clinics:Y=[],buddies:u=[],allowRangeSelection:h=!1})=>{console.log("calendar bookings",c);const M=new Date(s.getFullYear(),s.getMonth(),1),F=new Date(s.getFullYear(),s.getMonth()+1,0);let x=M.getDay();x=x===0?6:x-1;const k=F.getDate(),O=["M","T","W","T","F","S","S"],$=Array(x).fill(null),T=Array.from({length:k},(e,t)=>t+1),L=[...$,...T],d=e=>{if(!n||!e)return!1;const t=r(new Date(s.getFullYear(),s.getMonth(),e)).format("YYYY-MM-DD");return r(n).format("YYYY-MM-DD")===t},w=e=>{if(!i||!e)return!1;const t=r(new Date(s.getFullYear(),s.getMonth(),e)).format("YYYY-MM-DD");return r(i).format("YYYY-MM-DD")===t},B=e=>{if(!h||!n||!i||!e)return!1;const t=new Date(s.getFullYear(),s.getMonth(),e),l=new Date(n),o=new Date(i);return t.setHours(0,0,0,0),l.setHours(0,0,0,0),o.setHours(0,0,0,0),t>=l&&t<=o},H=e=>{const t=new Date;return e===t.getDate()&&s.getMonth()===t.getMonth()&&s.getFullYear()===t.getFullYear()},b=e=>{if(!e)return!1;const t=new Date,l=new Date(s.getFullYear(),s.getMonth(),e);return t.setHours(0,0,0,0),l<t},D=e=>{if(!e)return!1;const l=new Date(s.getFullYear(),s.getMonth(),e).toLocaleDateString("en-US",{weekday:"long"});return N.includes(l)},j=e=>{if(!e)return!1;if(!p)return!0;const l=new Date(s.getFullYear(),s.getMonth(),e).toLocaleDateString("en-US",{weekday:"long"}).toLowerCase();return p.some(o=>o.day===l)};console.log("bookings",c);const I=e=>{if(!e||!(c!=null&&c.length))return!1;const t=new Date(s.getFullYear(),s.getMonth(),e),l=r(t).format("YYYY-MM-DD");return c.some(o=>{const m=r(o.date).format("YYYY-MM-DD");return l===m})},S=e=>{if(!e||!(Y!=null&&Y.length))return!1;const t=new Date(s.getFullYear(),s.getMonth(),e),l=r(t).format("YYYY-MM-DD");return Y.some(o=>{const m=r(o.clinic_date).format("YYYY-MM-DD");return l===m})},A=e=>{if(!e||!(u!=null&&u.length))return!1;const t=new Date(s.getFullYear(),s.getMonth(),e),l=r(t).format("YYYY-MM-DD");return u.some(o=>{const m=r(o.date).format("YYYY-MM-DD");return l===m})},U=e=>{if(!e||b(e)||D(e)||!j(e))return;const t=new Date(s.getFullYear(),s.getMonth(),e);h&&n?i?(f(t),g&&g(null)):t>=n?g&&g(t):(g&&g(n),f(t)):f(t)};return a.jsxs("div",{className:"w-full",children:[a.jsxs("div",{className:"mb-4 flex items-center justify-between rounded-lg bg-gray-50 p-2",children:[a.jsx(P,{className:"h-5 w-5 cursor-pointer text-gray-600 hover:text-gray-800",onClick:C}),a.jsxs("h2",{className:"text-lg font-medium",children:[s.toLocaleString("default",{month:"long"})," ",s.getFullYear()]}),a.jsx(R,{className:"h-5 w-5 cursor-pointer text-gray-600 hover:text-gray-800",onClick:v})]}),h&&a.jsxs("div",{className:"mb-3 rounded-lg bg-blue-50 p-3",children:[a.jsxs("p",{className:"text-sm text-blue-800",children:[a.jsx("span",{className:"font-medium",children:"Select dates:"})," Click to select start date, then click again for end date (optional)"]}),n&&a.jsxs("div",{className:"mt-2 text-xs text-blue-700",children:[a.jsx("span",{className:"font-medium",children:"Start:"})," ",r(n).format("MMM DD, YYYY"),i&&a.jsxs(a.Fragment,{children:[a.jsx("span",{className:"mx-2",children:"•"}),a.jsx("span",{className:"font-medium",children:"End:"})," ",r(i).format("MMM DD, YYYY")]})]})]}),a.jsxs("div",{className:"grid grid-cols-7 gap-2",children:[O.map(e=>a.jsx("div",{className:"text-center text-sm font-medium text-gray-500",children:e},e)),L.map((e,t)=>{const l=j(e),o=I(e),m=S(e),W=A(e),_=D(e)?"Club Closed":!l&&e?"Coach not available":"";return a.jsxs("div",{"data-tooltip-id":`day-${t}`,"data-tooltip-content":_,onClick:()=>U(e),className:"relative flex flex-col items-center",children:[a.jsx("div",{className:`
                  h-8 w-8 cursor-pointer rounded-lg p-1 text-center text-sm
                  ${e?l?"hover:bg-gray-100":"":"cursor-default"}
                  ${b(e)||D(e)||!l?"cursor-not-allowed opacity-50 hover:bg-transparent":""}
                  ${d(e)||w(e)?"bg-blue-600 text-white hover:bg-blue-700":B(e)?"bg-blue-100 text-blue-800":""}
                  ${H(e)&&!d(e)&&!w(e)?"border border-blue-600":""}
                `,children:e}),o&&e&&a.jsx("div",{className:"mt-0 h-1.5 w-1.5 rounded-full bg-blue-600"}),m&&e&&a.jsx("div",{className:"mt-0 h-1.5 w-1.5 rounded-full bg-green-600"}),W&&e&&a.jsx("div",{className:"mt-0 h-1.5 w-1.5 rounded-full bg-red-600"}),(D(e)||!l)&&e&&a.jsx(z,{id:`day-${t}`,place:"top",className:"z-50 !bg-gray-900 !px-2 !py-1 !text-xs !text-white"})]},t)})]})]})},X=q;export{X as C};

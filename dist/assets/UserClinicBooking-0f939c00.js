import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as n,u as be,j as ke,f as Re,b as Te,L as W}from"./vendor-851db8c1.js";import"./BottomDrawer-7b010fbb.js";import{M as Pe,T as Be,u as Fe,G as Ee,A as Ie,aI as Le,m as De,aC as Ae,am as Oe,e as Ye,v as g,d as $e,aD as Ve,ar as He,E as qe,J as Ge}from"./index-f915b394.js";import{B as Ue}from"./BackButton-11ba52b2.js";import"./TimeSlots-683a5ba6.js";import{A as Je}from"./AddPlayers-47490847.js";import{a as K}from"./ReservationSummary-4650f2bc.js";import{g as Ze}from"./customThresholdUtils-f40b07d5.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-tooltip-7a26650a.js";import"./index.esm-09a3a6b8.js";let S=new Pe,R=new Be;const $t=({isOpen:Me=!0,onClose:We=()=>{},surfaces:Ke=[]})=>{var O,Y,$,V,H,q,G,U,J,Z;const[T,z]=n.useState(1);n.useState(null),n.useState(null),n.useState(null),n.useState(null);const[N,Q]=n.useState([]),[X,ee]=n.useState([]);n.useState(!1),n.useState({from:null,until:null});const[te,ze]=n.useState("players"),[l,C]=n.useState([]),[P,se]=n.useState(!1),[ae,B]=n.useState(1),[ne,F]=n.useState(!1);n.useState(null);const[re,Qe]=n.useState(null),[ie,oe]=n.useState(null),[le,ce]=n.useState([]),[c,E]=n.useState(null),[I,de]=n.useState(null),[me,pe]=n.useState(null),[ue,xe]=n.useState(null),[he,L]=n.useState(!1),d=be(),{id:b}=ke(),{club:a,sports:k,user_subscription:x,user_permissions:p,club_membership:_}=Fe(),[t,ge]=n.useState(null);n.useContext(Ee),n.useContext(Ie);const ye=Re(),r=n.useMemo(()=>!(x!=null&&x.planId)||!(_!=null&&_.length)?null:_.find(s=>s.plan_id===x.planId),[x,_]),fe=n.useMemo(()=>{var o,w;if(((o=r==null?void 0:r.advance_booking_enabled)==null?void 0:o.clinic)===!1){const m=new Date;return m.setFullYear(m.getFullYear()+10),m}const s=((w=r==null?void 0:r.advance_booking_days)==null?void 0:w.clinic)||10,i=new Date,h=new Date;return h.setDate(i.getDate()+s),h},[r]),v=localStorage.getItem("user"),[y,f]=n.useState({isOpen:!1,title:"",message:"",actionButtonText:"",actionButtonLink:"",type:"warning"}),ve=async()=>{try{const s=await R.getOne("clinics",b,{});ge(s.model)}catch(s){console.error(s)}},D=async()=>{try{const s=await R.getList("user",{filter:["role,cs,user",`club_id,cs,${a==null?void 0:a.id}`]});Q(s.list)}catch(s){console.error(s)}},je=async()=>{try{const s=await S.callRawAPI("/v3/api/custom/courtmatchup/user/groups",{},"GET");ee(s.groups)}catch(s){console.error(s)}},we=async()=>{try{const s=await R.getList("user",{filter:[`guardian,eq,${v}`,"role,cs,user"]});ce(s.list)}catch(s){console.error("Error fetching family members:",s)}};n.useEffect(()=>{(async()=>(F(!0),await ve(),await D(),await je(),await we(),F(!1)))()},[]),n.useEffect(()=>{a!=null&&a.id&&D()},[a==null?void 0:a.id]);const Ne=s=>{C(i=>i.some(o=>o.id===s.id)?i.filter(o=>o.id!==s.id):[...i,s])},_e=s=>{const i=s.value||s;(i==null?void 0:i.id)!==(c==null?void 0:c.id)&&(E(i),C(h=>{const o=h.filter(m=>m.id!==(c==null?void 0:c.id));if(o.some(m=>m.id===i.id)){const m=o.filter(M=>M.id!==i.id);return[i,...m]}else return[i,...o]}))},j=Ze(a==null?void 0:a.custom_request_threshold,t==null?void 0:t.sport_id,t==null?void 0:t.type,t==null?void 0:t.sub_type,4,k);n.useEffect(()=>{l.length>j&&(console.log(`Clearing selected players: current ${l.length} exceeds new threshold ${j}`),C([]),B(1),f({isOpen:!0,title:"Player Selection Cleared",message:`Player selection cleared. New maximum is ${j} players. Please select players again.`,type:"warning"}))},[j]),n.useEffect(()=>{if(N.length>0&&!c){const s=N.find(i=>i.id===parseInt(v));s&&E(s)}},[N,c,v]);const u=Le({costPerHead:t==null?void 0:t.cost_per_head,playerCount:l==null?void 0:l.length,feeSettings:a==null?void 0:a.fee_settings}),Se=async()=>{var i,h;if(!(x!=null&&x.planId)){f({isOpen:!0,title:"Subscription Required",message:"Please subscribe to a membership plan to join clinics",actionButtonText:"View Membership Plans",actionButtonLink:"/user/membership/buy",type:"warning"});return}if(!(p!=null&&p.allowClinic)){f({isOpen:!0,title:"Plan Upgrade Required",message:`Your current plan (${p==null?void 0:p.planName}) does not include clinic bookings. Please upgrade your plan.`,actionButtonText:"Upgrade Plan",actionButtonLink:"/user/membership/buy",type:"error"});return}if(new Date(t==null?void 0:t.date)>fe&&((i=r==null?void 0:r.advance_booking_enabled)==null?void 0:i.clinic)!==!1){const o=((h=r==null?void 0:r.advance_booking_days)==null?void 0:h.clinic)||10;f({isOpen:!0,title:"Date Selection Error",message:`Your membership plan only allows booking clinics ${o} days in advance. Please select a clinic within your allowed date range.`,type:"warning"});return}if(!l.length){f({isOpen:!0,title:"Players Required",message:"Please select at least one player",type:"warning"});return}L(!0);try{const o=await S.callRawAPI("/v3/api/custom/courtmatchup/user/reservations/payment-intent/create",{amount:u.total},"POST"),w={sport_id:t==null?void 0:t.sport_id,type:t==null?void 0:t.type,sub_type:t==null?void 0:t.sub_type,date:t==null?void 0:t.date,start_time:t==null?void 0:t.start_time,end_time:t==null?void 0:t.end_time,duration:t==null?void 0:t.duration,price:u.total,clinic_fee:u.clinicFee,service_fee:u.serviceFee,player_ids:l.map(Ce=>Ce.id),primary_player_id:(c==null?void 0:c.id)||parseInt(v),buddy_details:null,payment_status:0,payment_intent:o.payment_intent,reservation_type:He.clinic,clinic_id:b},m=await S.callRawAPI("/v3/api/custom/courtmatchup/user/reservations",w,"POST");S.setTable("activity_logs");const M=await S.callRestAPI({activity_type:qe.clinic,user_id:v,club_id:a==null?void 0:a.id,action_type:Ge.CREATE,data:JSON.stringify(w),description:"Created a clinic reservation"},"POST");pe(o.client_secret),xe(o.payment_intent),de(m.reservation_id),oe(m.booking_id),z(2)}catch(o){console.error(o),f({isOpen:!0,title:"Reservation Error",message:o.message||"Error creating clinic reservation",type:"error"})}finally{L(!1)}};Te.useEffect(()=>{De({path:`/user/clinic-booking/${b}`,clubName:a==null?void 0:a.name,favicon:a==null?void 0:a.club_logo,description:`Join ${t==null?void 0:t.name}`})},[a==null?void 0:a.club_logo]);const A=a!=null&&a.clinic_description?JSON.parse(a==null?void 0:a.clinic_description):{reservation_description:"",payment_description:""};return p&&!p.allowClinic?e.jsx(Ae,{message:`Your current plan (${p==null?void 0:p.planName}) does not include clinic bookings. Please upgrade your plan to access this feature.`}):e.jsxs("div",{className:"h-full",children:[e.jsx(Oe,{isOpen:y.isOpen,onClose:()=>f({...y,isOpen:!1}),title:y.title,message:y.message,actionButtonText:y.actionButtonText,actionButtonLink:y.actionButtonLink,type:y.type}),ne&&e.jsx(Ye,{}),e.jsxs("div",{className:"",children:[e.jsx("div",{className:"flex items-center justify-center bg-white p-4",children:te==="players"&&e.jsx("div",{className:" ",children:"Clinic Booking"})}),e.jsxs("div",{className:"p-4",children:[e.jsx(Ue,{onBack:()=>{ye(-1)}}),T===1&&e.jsxs("div",{className:"mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3",children:[e.jsxs("div",{className:"space-y-4",children:[((O=r==null?void 0:r.advance_booking_enabled)==null?void 0:O.clinic)===!1?e.jsx("div",{className:"rounded-lg bg-blue-50 p-3 text-sm text-blue-700",children:"You can sign up for a clinic for any future date."}):((Y=r==null?void 0:r.advance_booking_days)==null?void 0:Y.clinic)!==void 0&&e.jsxs("div",{className:"rounded-lg bg-blue-50 p-3 text-sm text-blue-700",children:["You can sign up for a clinic up to"," ",($=r==null?void 0:r.advance_booking_days)==null?void 0:$.clinic," ",((V=r==null?void 0:r.advance_booking_days)==null?void 0:V.clinic)===1?"day":"days"," ","in advance."]}),e.jsx(K,{selectedSport:t==null?void 0:t.sport_id,sports:k,selectedType:t==null?void 0:t.type,selectedSubType:t==null?void 0:t.sub_type,selectedDate:t==null?void 0:t.date,selectedTimes:t==null?void 0:t.start_time,clinic:{...t,...(H=d==null?void 0:d.state)==null?void 0:H.clinic}})]}),e.jsx("div",{className:"space-y-4",children:e.jsx(Je,{players:N,groups:X,selectedPlayers:l,onPlayerToggle:Ne,isFindBuddyEnabled:P,onFindBuddyToggle:()=>se(!P),playersNeeded:ae,onPlayersNeededChange:B,showPlayersNeeded:!1,showCurrentGroup:!1,setSelectedPlayers:C,maximumPlayers:Math.min(j,(G=(q=d==null?void 0:d.state)==null?void 0:q.clinic)!=null&&G.slots_remaining?parseInt((J=(U=d==null?void 0:d.state)==null?void 0:U.clinic)==null?void 0:J.slots_remaining):j),familyMembers:le,currentUser:c,onCurrentUserChange:_e,userProfile:N.find(s=>s.id===parseInt(v))})}),e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:e.jsx("h2",{className:"text-base font-medium",children:"Reserving details"})}),e.jsx("div",{className:"p-4",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs("h3",{className:"text-sm text-gray-500",children:["PLAYERS (",l.length,")"]}),e.jsx("div",{className:"mt-1",children:l.map(s=>e.jsxs("div",{className:"text-sm",children:[s.first_name," ",s.last_name]},s.id))})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm text-gray-500",children:"FEES"}),e.jsxs("div",{className:"mt-2 flex items-center justify-between",children:[e.jsxs("span",{className:"flex items-center gap-1",children:["Clinic Fee",e.jsxs("span",{className:"text-xs text-gray-500",children:["(",g(t==null?void 0:t.cost_per_head)," ×"," ",l.length," players)"]})]}),e.jsx("span",{children:g(u.clinicFee)})]}),e.jsxs("div",{className:"mt-2 flex items-center justify-between",children:[e.jsx("span",{children:"Service Fee"}),e.jsx("span",{children:g(u.serviceFee)})]})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{children:"Total"}),e.jsx("span",{className:"font-medium",children:g(u.total)})]}),e.jsx("div",{className:"rounded-lg bg-[#F17B2C] p-3 text-sm text-white",children:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.49364 2.62152L13.9236 12.1351C13.9737 12.2227 14 12.3221 14 12.4233C14 12.5245 13.9737 12.624 13.9236 12.7116C13.8736 12.7993 13.8017 12.8721 13.715 12.9227C13.6283 12.9733 13.5301 12.9999 13.43 12.9999H2.57C2.46995 12.9999 2.37165 12.9733 2.285 12.9227C2.19835 12.8721 2.12639 12.7993 2.07636 12.7116C2.02634 12.624 2 12.5245 2 12.4233C2 12.3221 2.02634 12.2227 2.07637 12.1351L7.50636 2.62152C7.5564 2.53387 7.62835 2.46109 7.715 2.41049C7.80165 2.35989 7.89995 2.33325 7.99995 2.33325C8.09995 2.33325 8.19835 2.35989 8.285 2.41049C8.37165 2.46109 8.4436 2.53387 8.49364 2.62152ZM7.42998 10.117V11.2702H8.57002V10.117H7.42998ZM7.42998 6.08098V8.96387H8.57002V6.08098H7.42998Z",fill:"white"})}),e.jsx("span",{children:"After reserving, you will have 15 minutes to make the payment."})]})}),e.jsx($e,{loading:he,onClick:Se,className:"w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90",children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("span",{children:"Reserve Now"}),e.jsx("span",{className:"text-sm opacity-80",children:"and continue to payment"})]})}),e.jsx("p",{className:"text-center text-sm text-gray-500",children:A.reservation_description}),e.jsxs("div",{className:"space-y-2 text-center text-sm text-gray-500",children:[e.jsx("p",{children:"(You will not be charged yet)"}),e.jsxs("p",{children:["For any issues, please contact our support team at"," ",e.jsx("a",{href:"mailto:<EMAIL>",className:"font-medium underline",children:"<EMAIL>"})]})]})]})})]})]}),T===2&&e.jsxs("div",{className:"mx-auto max-w-6xl",children:[e.jsx("div",{className:"rounded-xl bg-[#F17B2C] px-4 py-3 text-white",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{className:"!text-sm",children:"Your session is reserved. You have 15 minutes to complete the payment, otherwise the reservation will be canceled."})]})}),e.jsxs("div",{className:"mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsx("div",{className:"space-y-6",children:e.jsx(K,{selectedSport:t==null?void 0:t.sport_id,sports:k,selectedType:t==null?void 0:t.type,selectedSubType:t==null?void 0:t.sub_type,selectedDate:t==null?void 0:t.date,selectedTimes:t==null?void 0:t.start_time,clinic:{...t,...(Z=d==null?void 0:d.state)==null?void 0:Z.clinic}})}),e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"rounded-xl bg-white p-6 shadow-5",children:[e.jsx("h2",{className:"mb-4 text-center text-lg font-medium",children:"Payment details"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsxs("h3",{className:"text-sm text-gray-500",children:["PLAYERS (",l.length,")"]}),e.jsx("div",{className:"mt-1",children:l.map(s=>e.jsxs("div",{className:"text-sm",children:[s.first_name," ",s.last_name]},s.id))})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm text-gray-500",children:"FEES"}),e.jsxs("div",{className:"mt-2 flex items-center justify-between",children:[e.jsxs("span",{className:"flex items-center gap-1",children:["Clinic Fee",e.jsxs("span",{className:"text-xs text-gray-500",children:["(",g(t==null?void 0:t.cost_per_head)," ×"," ",l.length," players)"]})]}),e.jsx("span",{children:g(u.clinicFee)})]}),e.jsxs("div",{className:"mt-2 flex items-center justify-between",children:[e.jsx("span",{children:"Service Fee"}),e.jsx("span",{children:g(u.serviceFee)})]})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{children:"Total"}),e.jsx("span",{className:"font-medium",children:g(u.total)})]}),e.jsxs("div",{children:[e.jsx(Ve,{user:re,bookingId:ie,reservationId:I,clientSecret:me,paymentIntent:ue,navigateRoute:`/user/payment-success/${I}?type=clinic`}),e.jsx("p",{className:"mt-5 text-center text-sm text-gray-500",children:A.payment_description})]}),e.jsxs("div",{className:"space-y-4 text-sm text-gray-500",children:[e.jsxs("p",{children:['By clicking "Pay now" you agree to our'," ",e.jsx(W,{to:"/terms-and-conditions",target:"_blank",className:"font-medium underline",children:"Terms and Conditions"})," ","and"," ",e.jsx(W,{to:"/privacy-policy",target:"_blank",className:"font-medium underline",children:"Privacy Policy"}),". All sales are final unless stated otherwise."]}),e.jsxs("p",{children:["For any issues, please contact our support team at"," ",e.jsx("a",{href:"mailto:<EMAIL>",className:"font-medium underline",children:"<EMAIL>"})]})]})]})]})})]})]})]})]})]})};export{$t as default};

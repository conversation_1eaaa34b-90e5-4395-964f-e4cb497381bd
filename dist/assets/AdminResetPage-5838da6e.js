import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import{b as q,r as B,f as D,L as M}from"./vendor-851db8c1.js";import{u as O}from"./react-hook-form-687afde5.js";import{o as T}from"./yup-2824f222.js";import{c as I,a as i,d as K}from"./yup-54691517.js";import{A as U,d as Y,M as z,b as G,t as H}from"./index-f915b394.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const Re=()=>{var l,c,p,u,x,g;const{dispatch:n}=q.useContext(U),[m,a]=B.useState(!1),E=window.location.search,F=new URLSearchParams(E).get("token"),L=I({code:i().required(),password:i().required(),confirmPassword:i().oneOf([K("password"),null],"Passwords must match")}).required(),R=D(),{register:o,handleSubmit:$,setError:d,formState:{errors:t}}=O({resolver:T(L)}),A=async h=>{var b,f,w,y,j,N,k,v;let C=new z;try{a(!0);const e=await C.reset(F,h.code,h.password);if(!e.error)G(n,"Password Reset"),setTimeout(()=>{R("/admin/login")},2e3);else if(e.validation){const P=Object.keys(e.validation);for(let r=0;r<P.length;r++){const S=P[r];d(S,{type:"manual",message:e.validation[S]})}}a(!1)}catch(e){a(!1),console.log("Error",e),d("code",{type:"manual",message:(f=(b=e==null?void 0:e.response)==null?void 0:b.data)!=null&&f.message?(y=(w=e==null?void 0:e.response)==null?void 0:w.data)==null?void 0:y.message:e==null?void 0:e.message}),H(n,(N=(j=e==null?void 0:e.response)==null?void 0:j.data)!=null&&N.message?(v=(k=e==null?void 0:e.response)==null?void 0:k.data)==null?void 0:v.message:e==null?void 0:e.message)}};return s.jsx(s.Fragment,{children:s.jsxs("div",{className:"w-full max-w-xs mx-auto",children:[s.jsxs("form",{onSubmit:$(A),className:"bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4 mt-8 ",children:[s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"code",children:"Code"}),s.jsx("input",{type:"text",placeholder:"Enter code sent to your email",...o("code"),className:`"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(l=t.code)!=null&&l.message?"border-red-500":""}`}),s.jsx("p",{className:"text-red-500 text-xs italic",children:(c=t.code)==null?void 0:c.message})]}),s.jsxs("div",{className:"mb-6",children:[s.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"password",children:"Password"}),s.jsx("input",{type:"password",placeholder:"******************",...o("password"),className:`shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline ${(p=t.password)!=null&&p.message?"border-red-500":""}`}),s.jsx("p",{className:"text-red-500 text-xs italic",children:(u=t.password)==null?void 0:u.message})]}),s.jsxs("div",{className:"mb-6",children:[s.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"confirmPassword",children:"Confirm Password"}),s.jsx("input",{type:"password",placeholder:"******************",...o("confirmPassword"),className:`shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline ${(x=t.confirmPassword)!=null&&x.message?"border-red-500":""}`}),s.jsx("p",{className:"text-red-500 text-xs italic",children:(g=t.confirmPassword)==null?void 0:g.message})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(Y,{className:"bg-primaryBlue disabled:cursor-not-allowed text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",type:"submit",loading:m,disabled:m,children:"Reset Password"}),s.jsx(M,{className:"inline-block align-baseline font-bold text-sm text-primaryBlue",to:"/admin/login",children:"Login?"})]})]}),s.jsxs("p",{className:"text-center text-gray-500 text-xs",children:["© ",new Date().getFullYear()," manaknightdigital inc. All rights reserved."]})]})})};export{Re as default};

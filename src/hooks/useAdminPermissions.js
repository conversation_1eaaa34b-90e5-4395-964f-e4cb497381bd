import { useState, useEffect } from "react";
import MkdSDK from "Utils/MkdSDK";

const sdk = new MkdSDK();

/**
 * Custom hook to fetch and manage admin staff permissions
 * Admin staff permissions are stored with club_id = 0 (global level)
 */
export const useAdminPermissions = () => {
  const [permissions, setPermissions] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchPermissions = async () => {
    setLoading(true);
    setError(null);
    try {
      sdk.setTable("club_permissions");
      const result = await sdk.callRestAPI(
        {
          filter: [`club_id,eq,0`], // Admin staff operates globally with club_id 0
        },
        "GETALL"
      );

      if (result.list.length > 0) {
        const parsedPermissions = JSON.parse(result.list[0].permission);
        console.log("parsedPermissions", parsedPermissions);
        setPermissions(parsedPermissions);
      } else {
        // No permissions found, use default (all disabled except staff module disabled by default)
        setPermissions({
          staff: {
            dashboard: true,
            customer_support: true,
            daily_scheduler: true,
            court_management: true,
            availability_dashboard: true,
            availability_dashboard_all: true,
            club_ui: true,
            lessons: true,
            clinics: true,
            custom_requests: true,
            find_a_buddy: true,
            coach: true,
            staff: false, // Admin staff module disabled by default
            user: true,
            fee_settings: true,
            email: true,
            invoicing: true,
            invoicing_bank: true,
            history: true,
            court_reservation: true,
            general_faqs: true,
          },
        });
      }
    } catch (err) {
      console.error("Error fetching admin permissions:", err);
      setError(err);
      setPermissions(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPermissions();
  }, []);

  /**
   * Check if a specific module is enabled for admin staff
   * @param {string} module - The module name to check
   * @returns {boolean} - Whether the module is enabled
   */
  const hasPermission = (module) => {
    if (!permissions || !permissions.staff) {
      return false;
    }
    return permissions.staff[module] === true;
  };

  /**
   * Check if admin staff module itself is enabled
   * This controls access to admin staff management pages
   * @returns {boolean} - Whether admin staff module is enabled
   */
  const hasAdminStaffAccess = () => {
    return hasPermission("staff");
  };

  return {
    permissions,
    loading,
    error,
    hasPermission,
    hasAdminStaffAccess,
    refetch: fetchPermissions,
  };
};

export default useAdminPermissions;

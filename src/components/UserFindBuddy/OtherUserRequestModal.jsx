import React, { useState, useContext, useEffect } from "react";
import RightSideModal from "../RightSideModal";
import { convertTo12Hour } from "Utils/utils";
import { useNavigate } from "react-router-dom";
import { FaUser } from "react-icons/fa";
import { IoChevronDown } from "react-icons/io5";
import MkdSDK from "Utils/MkdSDK";
import { getManyByIds, GlobalContext, showToast } from "Context/Global";
import { AuthContext } from "Context/Auth";
import {
  CalendarIcon,
  ChevronRightIcon,
  XMarkIcon,
} from "@heroicons/react/24/solid";
import TimeSlots from "Components/TimeSlots/TimeSlots";
import { GroupFullStatus } from "Components/ReservationStatus";
import { InteractiveButton } from "Components/InteractiveButton";

let sdk = new MkdSDK();
export default function BuddyDetailsModal({
  isOpen,
  onClose,
  buddy,
  onRequestJoin,
  onReserveCourt,
  fetchData,
  setSelectedBuddy,
  clubSports,
}) {
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const { dispatch: authDispatch } = useContext(AuthContext);
  // const [players, setPlayers] = useState([]);
  const navigate = useNavigate();
  const [isOthersExpanded, setIsOthersExpanded] = useState(false);
  const [activeTab, setActiveTab] = useState("details");
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState(buddy?.date);
  const [statusLoading, setStatusLoading] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [pendingStatusChange, setPendingStatusChange] = useState(null);
  const [timeRange, setTimeRange] = useState([
    {
      from: buddy?.start_time,
      until: buddy?.end_time,
    },
  ]);

  const handleDateChange = async () => {
    try {
      const response = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/user/buddy/${buddy.buddy_id || buddy.id}`,
        "PUT",
        {
          date: selectedDate,
          start_time: timeRange.from,
          end_time: timeRange.until,
        }
      );
      showToast(globalDispatch, "Date updated successfully", 3000, "success");
      fetchData();
    } catch (error) {
      console.error("Error updating date:", error);
      showToast(globalDispatch, "Error updating date", 3000, "error");
    } finally {
      setStatusLoading(false);
    }
  };

  const handleTimeClick = (time) => {
    // Add the new time range to selectedTimes
    setTimeRange([
      {
        from: time.from,
        until: time.until,
      },
    ]);
  };

  const handleTimeRangeComplete = (range) => {
    setTimeRange(range);
  };

  // async function getBuddyPlayers(buddy) {
  //   try {
  //     const players = await getManyByIds(
  //       globalDispatch,
  //       authDispatch,
  //       "user",
  //       JSON.parse(buddy?.player_ids),
  //       "user|user_id"
  //     );
  //     setPlayers(players.list);
  //   } catch (error) {
  //     console.error(error);
  //   }
  // }

  // useEffect(() => {
  //   getBuddyPlayers(buddy);
  // }, [buddy]);

  if (!buddy) return null;

  const pendingRequests = buddy?.requests_to_join?.filter(
    (request) => request.request_status == 0 || request.request_status == 4
  );

  const acceptedRequests = buddy?.requests_to_join?.filter(
    (request) => request.request_status == 1
  );

  const declinedRequests = buddy?.requests_to_join?.filter(
    (request) => request.request_status == 2
  );

  const undoRequests = buddy?.requests_to_join?.filter(
    (request) => request.request_status == 4
  );

  const handleRequestStatusChange = async (requestId, status) => {
    const request = buddy.requests_to_join.find(
      (r) => r.request_id === requestId
    );
    if (status === 0) {
      setPendingStatusChange({
        requestId,
        status,
        userName: `${request.first_name} ${request.last_name}`,
      });
      setShowConfirmModal(true);
      return;
    }

    await updateRequestStatus(requestId, status);
  };

  const updateRequestStatus = async (requestId, status) => {
    setStatusLoading(true);
    try {
      const response = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/user/buddy/update-request`,
        {
          request_id: requestId,
          status: status,
        },
        "POST"
      );

      if (!response.error) {
        fetchData();
        showToast(
          globalDispatch,
          "Request status updated successfully",
          3000,
          "success"
        );

        // Update the request status in the selectedBuddy state
        const updatedRequests = buddy.requests_to_join.map((request) => {
          if (request.request_id === requestId) {
            return { ...request, request_status: status };
          }
          return request;
        });

        setSelectedBuddy({
          ...buddy,
          requests_to_join: updatedRequests,
        });
      }
    } catch (error) {
      console.error(error);
      showToast(globalDispatch, error.message, 3000, "error");
    } finally {
      setStatusLoading(false);
      setShowConfirmModal(false);
      setPendingStatusChange(null);
    }
  };

  return (
    <>
      <RightSideModal
        isOpen={isOpen}
        onClose={onClose}
        title="Request details"
        showFooter={false}
        className={`!p-0`}
      >
        <div className="space-y-6">
          <div className="px-5 pt-5">
            <div className="flex w-full justify-between gap-2 rounded-xl bg-gray-100 p-1">
              <button
                onClick={() => setActiveTab("details")}
                className={`flex w-full items-center justify-center rounded-lg border border-transparent bg-transparent px-4 py-2 text-sm font-medium ${
                  activeTab === "details" ? "border-gray-200 bg-white" : ""
                }`}
              >
                Details
              </button>
              <button
                onClick={() => setActiveTab("requests")}
                className={`flex w-full items-center justify-center rounded-lg border border-transparent bg-transparent px-4 py-2 text-sm font-medium ${
                  activeTab === "requests" ? "border-gray-200 bg-white" : ""
                }`}
              >
                Requests ({buddy?.requests_to_join?.length})
              </button>
            </div>
          </div>

          {activeTab === "details" ? (
            <>
              <div className="px-5">
                <div className="mb-4">
                  <p className="text-sm text-gray-500">REQUEST MADE</p>
                  <p className="mt-1 font-medium">
                    {new Date(buddy?.create_at).toLocaleDateString("en-US", {
                      weekday: "long",
                      month: "short",
                      day: "numeric",
                    })}
                    {" • "}
                    {convertTo12Hour(buddy?.start_time)} -{" "}
                    {convertTo12Hour(buddy?.end_time)}
                  </p>
                </div>
              </div>

              <div className="px-5">
                <div className="space-y-4 divide-y">
                  <div className="py-4">
                    <div className="mb-2 flex items-center justify-between">
                      <p className="text-sm text-gray-500">DATE & TIME</p>
                      <button
                        onClick={() => setShowDatePicker(true)}
                        className="flex items-center gap-2 rounded-lg border px-2 py-1 text-sm text-gray-500 hover:bg-gray-50"
                      >
                        <CalendarIcon className="h-5 w-5" />
                        Select new date
                      </button>

                      {showDatePicker && (
                        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
                          <div className="w-full max-w-md rounded-xl bg-white p-4">
                            <div className="mb-4 flex items-center justify-between">
                              <h3 className="text-lg font-medium">
                                Select new date and time
                              </h3>
                              <button
                                onClick={() => setShowDatePicker(false)}
                                className="rounded-lg p-1 hover:bg-gray-100"
                              >
                                <XMarkIcon className="h-5 w-5 text-gray-500" />
                              </button>
                            </div>

                            <div className="space-y-4">
                              <div>
                                <label className="block text-sm font-medium text-gray-700">
                                  Date
                                </label>
                                <input
                                  type="date"
                                  value={selectedDate}
                                  onChange={(e) =>
                                    setSelectedDate(e.target.value)
                                  }
                                  className="mt-1 block w-full rounded-lg border border-gray-300 px-3 py-2 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                                />
                              </div>

                              <div>
                                <label className="block text-sm font-medium text-gray-700">
                                  Time slot
                                </label>
                                <div className="mt-2">
                                  <TimeSlots
                                    selectedDate={selectedDate}
                                    timeRange={timeRange}
                                    onTimeClick={handleTimeClick}
                                    isTimeSlotAvailable={(timeObj) => {
                                      // Add your availability logic here if needed
                                      return true;
                                    }}
                                    // onTimeSlotsChange={handleTimeRangeComplete}
                                    startHour={0}
                                    endHour={23}
                                    interval={30}
                                  />
                                </div>
                              </div>

                              <div className="flex justify-end gap-2 pt-4">
                                <button
                                  onClick={() => setShowDatePicker(false)}
                                  className="rounded-lg border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
                                >
                                  Cancel
                                </button>
                                <button
                                  onClick={handleDateChange}
                                  className="rounded-lg bg-[#1B254B] px-4 py-2 text-sm font-medium text-white hover:bg-blue-900"
                                >
                                  Save changes
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                    <p className="mt-1 font-medium">
                      {new Date(buddy?.date).toLocaleDateString("en-US", {
                        weekday: "long",
                        month: "short",
                        day: "numeric",
                      })}
                      {" • "}
                      {convertTo12Hour(buddy?.start_time)} -{" "}
                      {convertTo12Hour(buddy?.end_time)}
                    </p>
                  </div>

                  <div className="py-4">
                    <p className="text-sm text-gray-500">SPORT & TYPE</p>
                    <p className="mt-1 font-medium">
                      {buddy?.sport_name ||
                        clubSports?.find(
                          (sport) => sport.id === buddy?.sport_id
                        )?.name}{" "}
                      {buddy?.type && `• ${buddy?.type}`}{" "}
                      {buddy?.sub_type && `• ${buddy?.sub_type}`}
                    </p>
                  </div>

                  <div className="py-4">
                    <p className="text-sm text-gray-500">NTRP</p>
                    <p className="mt-1 font-medium">
                      {buddy?.ntrp}
                      {buddy?.max_ntrp ? ` - ${buddy?.max_ntrp}` : ""}
                    </p>
                  </div>

                  <div className="py-4">
                    <p className="text-sm text-gray-500">LOOKING FOR PLAYERS</p>
                    <p className="mt-1 font-medium">
                      {buddy?.num_players}/{buddy?.num_needed}
                    </p>
                  </div>

                  <div className="py-4">
                    <p className="text-sm text-gray-500">GROUP BIO</p>
                    <p className="mt-1 text-sm">
                      {buddy?.notes ? buddy?.notes : "No bio provided"}
                    </p>
                  </div>
                </div>
              </div>
            </>
          ) : (
            <div className="px-5">
              <div className="space-y-6">
                {pendingRequests.length > 0 ? (
                  <div>
                    <p className="font-medium">Pending</p>
                    <div className="mt-4 space-y-4">
                      {pendingRequests.map((request, index) => (
                        <div key={index} className="rounded-lg bg-gray-50 p-4">
                          <div className="flex items-center gap-3">
                            <div className="h-10 w-10 overflow-hidden rounded-full bg-gray-200">
                              <img
                                src={request?.photo}
                                alt="Arthur Taylor"
                                className="h-full w-full object-cover"
                              />
                            </div>
                            <div className="flex-1">
                              <p className="font-medium">
                                {request?.first_name} {request?.last_name}
                              </p>
                              <p className="text-sm text-gray-500">
                                {request?.email}
                              </p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-500">
                                NTRP: {request?.ntrp}
                              </p>
                            </div>
                          </div>
                          <div className="mt-3 flex gap-2">
                            <InteractiveButton
                              onClick={() => {
                                handleRequestStatusChange(
                                  request?.request_id,
                                  1
                                );
                              }}
                              loading={statusLoading}
                              className="flex-1 rounded-lg border border-green-500 bg-green-50 py-2 text-sm font-medium text-green-600"
                            >
                              <div className="flex items-center gap-1">
                                <svg
                                  width="17"
                                  height="16"
                                  viewBox="0 0 17 16"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    d="M8.08716 14C4.77336 14 2.08716 11.3138 2.08716 8C2.08716 4.6862 4.77336 2 8.08716 2C11.401 2 14.0872 4.6862 14.0872 8C14.0872 11.3138 11.401 14 8.08716 14ZM7.48896 10.4L11.731 6.1574L10.8826 5.309L7.48896 8.7032L5.79156 7.0058L4.94316 7.8542L7.48896 10.4Z"
                                    fill="#38C793"
                                  />
                                </svg>
                                <span>Accept</span>
                              </div>
                            </InteractiveButton>
                            <InteractiveButton
                              onClick={() => {
                                handleRequestStatusChange(
                                  request?.request_id,
                                  2
                                );
                              }}
                              loading={statusLoading}
                              className="flex-1 rounded-lg border border-red-500 bg-red-50 py-2 text-sm font-medium text-red-600"
                            >
                              <div className="flex items-center gap-1">
                                <svg
                                  width="17"
                                  height="16"
                                  viewBox="0 0 17 16"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    fillRule="evenodd"
                                    clipRule="evenodd"
                                    d="M2.28491 8.00065C2.28491 4.31875 5.26968 1.33398 8.95158 1.33398C12.6335 1.33398 15.6182 4.31875 15.6182 8.00065C15.6182 11.6825 12.6335 14.6673 8.95158 14.6673C5.26968 14.6673 2.28491 11.6825 2.28491 8.00065ZM7.30513 5.6471C7.10987 5.45184 6.79329 5.45184 6.59803 5.6471C6.40276 5.84236 6.40276 6.15894 6.59803 6.3542L8.24447 8.00065L6.59803 9.6471C6.40276 9.84236 6.40276 10.1589 6.59803 10.3542C6.79329 10.5495 7.10987 10.5495 7.30513 10.3542L8.95158 8.70776L10.598 10.3542C10.7933 10.5495 11.1099 10.5495 11.3051 10.3542C11.5004 10.1589 11.5004 9.84236 11.3051 9.6471L9.65869 8.00065L11.3051 6.3542C11.5004 6.15894 11.5004 5.84236 11.3051 5.6471C11.1099 5.45184 10.7933 5.45184 10.598 5.6471L8.95158 7.29354L7.30513 5.6471Z"
                                    fill="#DF1C41"
                                  />
                                </svg>
                                <span>Decline</span>
                              </div>
                            </InteractiveButton>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : null}

                {acceptedRequests.length > 0 && (
                  <div>
                    <p className="font-medium">Accepted</p>
                    <div className="mt-4 space-y-4">
                      {acceptedRequests.map((request, index) => (
                        <div key={index} className="rounded-lg bg-gray-50 p-4">
                          <div className="flex items-center gap-3">
                            <div className="h-10 w-10 overflow-hidden rounded-full bg-gray-200">
                              <img
                                src={request?.photo || "/default-avatar.png"}
                                alt={`${request?.first_name} ${request?.last_name}`}
                                className="h-full w-full object-cover"
                              />
                            </div>
                            <div className="flex-1">
                              <p className="font-medium">
                                {request?.first_name} {request?.last_name}
                              </p>
                              <p className="text-sm text-gray-500">
                                {request?.email}
                              </p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-500">
                                NTRP: {request?.ntrp}
                              </p>
                            </div>
                          </div>
                          <div className="mt-3 flex items-center gap-3">
                            <span className="flex items-center gap-1 rounded-lg border border-gray-200 bg-white px-2 py-1 text-sm text-gray-500">
                              <svg
                                width="16"
                                height="16"
                                viewBox="0 0 16 16"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  fillRule="evenodd"
                                  clipRule="evenodd"
                                  d="M8.00004 1.33398C4.31814 1.33398 1.33337 4.31875 1.33337 8.00065C1.33337 11.6825 4.31814 14.6673 8.00004 14.6673C11.6819 14.6673 14.6667 11.6825 14.6667 8.00065C14.6667 4.31875 11.6819 1.33398 8.00004 1.33398ZM10.387 6.6506C10.5619 6.43688 10.5304 6.12187 10.3167 5.94701C10.1029 5.77214 9.78793 5.80364 9.61306 6.01737L6.96292 9.25643L6.02026 8.31376C5.825 8.1185 5.50842 8.1185 5.31315 8.31376C5.11789 8.50903 5.11789 8.82561 5.31315 9.02087L6.64649 10.3542C6.74638 10.4541 6.88386 10.5071 7.02495 10.5C7.16604 10.493 7.29757 10.4266 7.38702 10.3173L10.387 6.6506Z"
                                  fill="#868C98"
                                />
                              </svg>
                              Accepted
                            </span>
                            <button
                              onClick={() => {
                                handleRequestStatusChange(
                                  request?.request_id,
                                  0
                                );
                              }}
                              className="text-sm text-gray-500 hover:text-gray-900"
                            >
                              Undo
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {declinedRequests.length > 0 && (
                  <div>
                    <p className="font-medium">Declined</p>
                    <div className="mt-4 space-y-4">
                      {declinedRequests.map((request, index) => (
                        <div key={index} className="rounded-lg bg-gray-50 p-4">
                          <div className="flex items-center gap-3">
                            <div className="h-10 w-10 overflow-hidden rounded-full bg-gray-200">
                              <img
                                src={request?.photo || "/default-avatar.png"}
                                alt={`${request?.first_name} ${request?.last_name}`}
                                className="h-full w-full object-cover"
                              />
                            </div>
                            <div className="flex-1">
                              <p className="font-medium">
                                {request?.first_name} {request?.last_name}
                              </p>
                              <p className="text-sm text-gray-500">
                                {request?.email}
                              </p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-500">
                                NTRP: {request?.ntrp}
                              </p>
                            </div>
                          </div>
                          <div className="mt-3 flex items-center gap-3">
                            <span className="text-sm text-gray-500">
                              <span className="mr-1 inline-block h-2 w-2 rounded-full bg-gray-400"></span>
                              Declined
                            </span>
                            <button
                              onClick={() => {
                                handleRequestStatusChange(
                                  request?.request_id,
                                  4
                                );
                              }}
                              className="text-sm text-gray-500 hover:text-gray-900"
                            >
                              Undo
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
        <div className="fixed bottom-0 w-full border-t border-gray-200 bg-white px-5 py-4">
          <button
            onClick={() => onReserveCourt(buddy)}
            className="w-full rounded-xl bg-[#1B254B] py-3 text-center font-medium text-white hover:bg-blue-900"
          >
            Reserve court
          </button>
        </div>
      </RightSideModal>

      {/* Confirmation Modal */}
      {showConfirmModal && pendingStatusChange && (
        <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50">
          <div className="w-full max-w-md rounded-xl bg-white p-6">
            <h3 className="mb-4 text-xl font-semibold">Undo Declined</h3>
            <p className="mb-6 text-gray-600">
              Are you sure you want to undo decline for request from{" "}
              {pendingStatusChange.userName}?
            </p>
            <p className="mb-6 text-gray-600">
              It will go back to Pending status.
            </p>
            <div className="flex justify-end gap-3">
              <button
                onClick={() => {
                  setShowConfirmModal(false);
                  setPendingStatusChange(null);
                }}
                className="rounded-lg border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() =>
                  updateRequestStatus(
                    pendingStatusChange.requestId,
                    pendingStatusChange.status
                  )
                }
                className="rounded-lg bg-[#1B254B] px-4 py-2 text-sm font-medium text-white hover:bg-blue-900"
              >
                Yes, undo decline
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

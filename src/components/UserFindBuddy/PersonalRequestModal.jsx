import React, { useState, useContext, useEffect } from "react";
import RightSideModal from "../RightSideModal";
import { convertTo12Hour } from "Utils/utils";
import { useNavigate } from "react-router-dom";
import { FaUser } from "react-icons/fa";
import { IoChevronDown } from "react-icons/io5";
import MkdSDK from "Utils/MkdSDK";
import { getManyByIds, GlobalContext } from "Context/Global";
import { AuthContext } from "Context/Auth";
import { ChevronRightIcon } from "@heroicons/react/24/solid";

let sdk = new MkdSDK();
export default function AllRequestDetailModal({
  isOpen,
  onClose,
  buddy,
  onRequestJoin,
  onReserveCourt,
  clubSports,
  fetchData,
}) {
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const { dispatch: authDispatch } = useContext(AuthContext);
  const [players, setPlayers] = useState([]);
  const navigate = useNavigate();
  const [isOthersExpanded, setIsOthersExpanded] = useState(false);
  const [activeTab, setActiveTab] = useState("details");

  async function getBuddyPlayers(buddy) {
    try {
      // Handle both API response formats
      let playerIds = [];
      if (buddy?.player_ids) {
        playerIds =
          typeof buddy.player_ids === "string"
            ? JSON.parse(buddy.player_ids)
            : buddy.player_ids;
      }

      if (playerIds.length > 0) {
        const players = await getManyByIds(
          globalDispatch,
          authDispatch,
          "user",
          playerIds,
          "user|user_id"
        );
        setPlayers(players.list);
      } else if (buddy?.player_details) {
        // Use player_details from API if available
        setPlayers(buddy.player_details);
      }
    } catch (error) {
      console.error(error);
    }
  }

  useEffect(() => {
    getBuddyPlayers(buddy);
  }, [buddy]);

  if (!buddy) return null;

  console.log(" personal request buddy", buddy);

  return (
    <RightSideModal
      isOpen={isOpen}
      onClose={onClose}
      title="Request details"
      showFooter={false}
      className={`!p-0`}
    >
      <div className="space-y-6">
        <div className=" px-5 pt-5">
          <div className="flex w-full justify-between gap-2 rounded-xl bg-gray-100 p-1">
            <button
              onClick={() => setActiveTab("details")}
              className={`flex w-full items-center justify-center rounded-lg border border-transparent bg-transparent  px-4 py-2 text-sm font-medium ${
                activeTab === "details" ? "border-gray-200 bg-white" : ""
              }`}
            >
              Details
            </button>
            {/* <button
              onClick={() => setActiveTab("reachouts")}
              className={`flex w-full items-center justify-center rounded-lg border border-transparent bg-transparent px-4 py-2 text-sm font-medium ${
                activeTab === "reachouts" ? "border-gray-200 bg-white" : ""
              }`}
            >
              Reach outs (0)
            </button> */}
          </div>
        </div>
        <div className="bg-gray-100 px-5 py-1 ">
          <p className="text-sm text-gray-500">REQUESTED BY</p>
        </div>
        <div className="px-5 py-1">
          <div className="flex items-center gap-4">
            <div className="h-10 w-10 overflow-hidden rounded-full">
              <img
                src={buddy?.owner_photo || "/default-avatar.png"}
                alt={`${buddy?.owner_first_name} ${buddy?.owner_last_name}`}
                className="h-full w-full object-cover"
              />
            </div>
            <div>
              <p className="font-medium capitalize">
                {buddy?.owner_first_name} {buddy?.owner_last_name}
              </p>
              <p className="text-sm text-gray-500">NTRP: {buddy?.ntrp}</p>
            </div>
          </div>

          {/* Others Section */}
          <div className="mt-4 rounded-xl bg-gray-100">
            <button
              onClick={() => setIsOthersExpanded(!isOthersExpanded)}
              className="flex w-full items-center gap-2 px-4 py-3"
            >
              <ChevronRightIcon
                className={`h-5 w-5 transform transition-transform ${
                  isOthersExpanded ? "rotate-90" : ""
                }`}
              />
              <span className="text-sm font-medium">
                Others({players.length})
              </span>
            </button>

            {isOthersExpanded && (
              <div className="px-4 pb-3">
                {players.map((player, index) => (
                  <div key={index} className="mb-3 flex items-center gap-4">
                    <div className="h-10 w-10 overflow-hidden rounded-full">
                      <img
                        src={player?.photo || "/default-avatar.png"}
                        alt={`${player?.first_name} ${player?.last_name}`}
                        className="h-full w-full object-cover"
                      />
                    </div>
                    <div>
                      <p className="">
                        {player?.first_name} {player?.last_name}
                      </p>
                      <p className="text-sm text-gray-600">
                        NTRP: {player?.ntrp}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
        <div className="bg-gray-100 px-5 py-1 ">
          <p className="text-sm text-gray-500"> DETAILS</p>
        </div>
        <div className="space-y-0 divide-y px-5 py-1">
          <div className="py-4">
            <p className="text-sm text-gray-500">DATE & TIME</p>
            <p className="mt-1 font-medium">
              {new Date(buddy?.date).toLocaleDateString("en-US", {
                weekday: "long",
                month: "long",
                day: "numeric",
              })}
              {" • "}
              {convertTo12Hour(buddy?.start_time)} -{" "}
              {convertTo12Hour(buddy?.end_time)}
            </p>
          </div>

          <div className="py-4">
            <p className="text-sm text-gray-500">SPORT & TYPE</p>
            <p className="mt-1 font-medium capitalize">
              {buddy?.sport_name ||
                clubSports?.find((sport) => sport.id === buddy?.sport_id)
                  ?.name}{" "}
              {buddy?.type && `• ${buddy?.type}`}{" "}
              {buddy?.sub_type && `• ${buddy?.sub_type}`}
            </p>
          </div>

          <div className="py-4">
            <p className="text-sm text-gray-500">LOOKING FOR PLAYERS</p>
            <p className="mt-1 font-medium">
              {buddy?.num_players}/{buddy?.num_needed}
            </p>
          </div>

          <div className="py-4">
            <p className="text-sm text-gray-500">NTRP Range</p>
            <p className="mt-1 font-medium">
              {buddy?.ntrp}
              {buddy?.max_ntrp ? ` - ${buddy?.max_ntrp}` : ""}
            </p>
          </div>

          <div className="py-4">
            <p className="text-sm text-gray-500">GROUP INFO</p>
            <p className="mt-1 text-sm">
              {buddy?.notes || "No description provided"}
            </p>
          </div>
        </div>

        {/* Reserve court button */}
        <div className="fixed bottom-0 w-full border-t border-gray-200 bg-white px-10 py-5">
          <button
            onClick={() => {
              onReserveCourt(buddy);
            }}
            className="flex w-full items-center justify-center gap-2 rounded-xl bg-[#1B254B] py-3 text-center text-white hover:bg-blue-900"
          >
            <span>Reserve court</span>
          </button>
        </div>
      </div>
    </RightSideModal>
  );
}

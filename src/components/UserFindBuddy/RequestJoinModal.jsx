import React, { useState } from "react";
import { InteractiveButton } from "Components/InteractiveButton";
import { useClub } from "Context/Club";
import WarningModal from "Components/Modals/WarningModal";

export const RequestJoinModal = ({
  isOpen,
  onClose,
  onRequestJoin,
  loading = false,
}) => {
  const { user_subscription, user_permissions } = useClub();

  // Add warning modal state
  const [warningModal, setWarningModal] = useState({
    isOpen: false,
    title: "",
    message: "",
    actionButtonText: "",
    actionButtonLink: "",
    type: "warning",
  });

  const handleRequestJoin = () => {
    // Check if user has a subscription
    if (!user_subscription?.planId) {
      setWarningModal({
        isOpen: true,
        title: "Subscription Required",
        message: "Please subscribe to a membership plan to join buddy requests",
        actionButtonText: "View Membership Plans",
        actionButtonLink: "/user/membership/buy",
        type: "warning",
      });
      return;
    }

    // Check if user's plan allows buddy feature
    if (!user_permissions?.allowBuddy) {
      setWarningModal({
        isOpen: true,
        title: "Plan Upgrade Required",
        message: `Your current plan (${user_permissions?.planName}) does not include the Find a Buddy feature. Please upgrade your plan.`,
        actionButtonText: "Upgrade Plan",
        actionButtonLink: "/user/membership/buy",
        type: "error",
      });
      return;
    }

    // If all checks pass, proceed with the request
    onRequestJoin();
  };

  return (
    <>
      <WarningModal
        isOpen={warningModal.isOpen}
        onClose={() => setWarningModal({ ...warningModal, isOpen: false })}
        title={warningModal.title}
        message={warningModal.message}
        actionButtonText={warningModal.actionButtonText}
        actionButtonLink={warningModal.actionButtonLink}
        type={warningModal.type}
      />

      <div
        className={`fixed inset-0 z-50 flex items-center justify-center ${
          isOpen ? "" : "hidden"
        }`}
      >
        <div className="fixed inset-0 bg-black opacity-50"></div>
        <div className="relative z-50 w-full max-w-md rounded-3xl bg-white">
          <div className="p-6">
            <h2 className="mb-4 text-xl font-medium">Request to join</h2>
            <div className="flex items-start justify-center rounded-xl bg-[#F17B2C] p-3">
              <div className="mr-2">
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M8.49364 2.62127L13.9236 12.1348C13.9737 12.2225 14 12.3219 14 12.4231C14 12.5243 13.9737 12.6237 13.9236 12.7114C13.8736 12.799 13.8017 12.8718 13.715 12.9224C13.6283 12.973 13.5301 12.9997 13.43 12.9997H2.57C2.46995 12.9997 2.37165 12.973 2.285 12.9224C2.19835 12.8718 2.12639 12.799 2.07636 12.7114C2.02634 12.6237 2 12.5243 2 12.4231C2 12.3219 2.02634 12.2225 2.07637 12.1348L7.50636 2.62127C7.5564 2.53363 7.62835 2.46085 7.715 2.41025C7.80165 2.35965 7.89995 2.33301 8 2.33301C8.10005 2.33301 8.19835 2.35965 8.285 2.41025C8.37165 2.46085 8.4436 2.53363 8.49364 2.62127ZM7.42998 10.1168V11.2699H8.57002V10.1168H7.42998ZM7.42998 6.08074V8.96363H8.57002V6.08074H7.42998Z"
                    fill="white"
                  />
                </svg>
              </div>
              <div>
                <div className="text-sm font-medium text-white">IMPORTANT</div>
                <p className="text-sm text-white">
                  By sending request you agree to share your contact information
                  so the request owner can get in touch with you.
                </p>
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-3 border-t px-6 py-4">
            <button
              onClick={onClose}
              className="rounded-xl border border-gray-300 px-4 py-2 hover:bg-gray-50"
            >
              Cancel
            </button>
            <InteractiveButton
              onClick={handleRequestJoin}
              className={`rounded-xl bg-green-800 px-4 py-2 text-white hover:bg-green-700`}
              loading={loading}
            >
              Send request
            </InteractiveButton>
          </div>
        </div>
      </div>
    </>
  );
};

export const SuccessRequestModal = ({
  isOpen,
  onClose,
  onDelete,
  loading = false,
}) => {
  return (
    <div
      className={`fixed inset-0 z-50 flex items-center justify-center ${
        isOpen ? "" : "hidden"
      }`}
    >
      <div className="fixed inset-0 bg-black opacity-50"></div>
      <div className="relative z-50 w-full max-w-md rounded-3xl bg-white">
        <div className="p-6">
          <h2 className="mb-4 text-xl font-medium">Request to join</h2>
          <div className="flex items-start justify-center rounded-xl bg-[#F17B2C] p-3">
            <div className="mr-2">
              <svg
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M8.49364 2.62127L13.9236 12.1348C13.9737 12.2225 14 12.3219 14 12.4231C14 12.5243 13.9737 12.6237 13.9236 12.7114C13.8736 12.799 13.8017 12.8718 13.715 12.9224C13.6283 12.973 13.5301 12.9997 13.43 12.9997H2.57C2.46995 12.9997 2.37165 12.973 2.285 12.9224C2.19835 12.8718 2.12639 12.799 2.07636 12.7114C2.02634 12.6237 2 12.5243 2 12.4231C2 12.3219 2.02634 12.2225 2.07637 12.1348L7.50636 2.62127C7.5564 2.53363 7.62835 2.46085 7.715 2.41025C7.80165 2.35965 7.89995 2.33301 8 2.33301C8.10005 2.33301 8.19835 2.35965 8.285 2.41025C8.37165 2.46085 8.4436 2.53363 8.49364 2.62127ZM7.42998 10.1168V11.2699H8.57002V10.1168H7.42998ZM7.42998 6.08074V8.96363H8.57002V6.08074H7.42998Z"
                  fill="white"
                />
              </svg>
            </div>
            <div>
              <div className="text-sm font-medium text-white">IMPORTANT</div>
              <p className="text-sm text-white">
                By sending request you agree to share your contact information
                so the request owner can get in touch with you.
              </p>
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-3 border-t px-6 py-4">
          <button
            onClick={onClose}
            className="rounded-xl border border-gray-300 px-4 py-2 hover:bg-gray-50"
          >
            Cancel
          </button>
          <InteractiveButton
            onClick={onDelete}
            className={`rounded-xl bg-green-800 px-4 py-2 text-white hover:bg-green-700`}
            loading={loading}
            // disabled={requireConfirmation && !isConfirmed}
          >
            Send request
          </InteractiveButton>
        </div>
      </div>
    </div>
  );
};
export const CancelRequestModal = ({
  isOpen,
  onClose,
  onDelete,
  loading = false,
}) => {
  return (
    <div
      className={`fixed inset-0 z-50 flex items-center justify-center ${
        isOpen ? "" : "hidden"
      }`}
    >
      <div className="fixed inset-0 bg-black opacity-50"></div>
      <div className="relative z-50 w-full max-w-md rounded-3xl bg-white">
        <div className="p-6">
          <h2 className="mb-4 text-xl font-medium">Cancel request</h2>
          <div className="flex items-start justify-center rounded-xl bg-[#F17B2C] p-3">
            <div>
              <div className="text-sm font-medium text-white">IMPORTANT</div>
              <p className="text-sm text-white">
                Are you sure you want to cancel this request?
              </p>
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-3 border-t px-6 py-4">
          <button
            onClick={onClose}
            className="rounded-xl border border-gray-300 px-4 py-2 hover:bg-gray-50"
          >
            Cancel
          </button>
          <InteractiveButton
            onClick={onDelete}
            className={`rounded-xl bg-green-800 px-4 py-2 text-white hover:bg-green-700`}
            loading={loading}
            // disabled={requireConfirmation && !isConfirmed}
          >
            Cancel request
          </InteractiveButton>
        </div>
      </div>
    </div>
  );
};

export default RequestJoinModal;

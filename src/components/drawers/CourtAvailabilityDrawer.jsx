import React, { useState, useEffect } from "react";
import { showToast } from "../../Context/Global";
import MkdSDK from "../../Utils/MkdSDK";
import TimeSlotGrid from "../TimeSlotGrid";
import BottomDrawer from "../Drawers/BottomDrawer";

const days = [
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
  "Sunday",
];

// Utility functions for club hours and exceptions
const isTimeInClubHours = (time, clubTimes) => {
  if (!clubTimes || clubTimes.length === 0) return true;

  const [hours, minutes] = time.split(":");
  const timeInMinutes = parseInt(hours) * 60 + parseInt(minutes);

  return clubTimes.some((slot) => {
    const [fromHours, fromMinutes] = slot.from.split(":");
    const [untilHours, untilMinutes] = slot.until.split(":");

    const fromInMinutes = parseInt(fromHours) * 60 + parseInt(fromMinutes);
    const untilInMinutes = parseInt(untilHours) * 60 + parseInt(untilMinutes);

    return timeInMinutes >= fromInMinutes && timeInMinutes < untilInMinutes;
  });
};

const isClubDayOff = (day, daysOff) => {
  if (!daysOff || daysOff.length === 0) return false;
  return daysOff.includes(day);
};

const getDayExceptions = (day, exceptions) => {
  if (!exceptions || exceptions.length === 0) return [];

  const dayName = day.toLowerCase();

  return exceptions.reduce((acc, exception) => {
    const dayException = exception.days.find((d) => d.day === dayName);
    if (dayException) {
      acc.push({
        name: exception.name,
        timeslots: dayException.timeslots,
      });
    }
    return acc;
  }, []);
};

const isExceptionTime = (time, dayExceptions) => {
  if (!dayExceptions || dayExceptions.length === 0) return false;

  const timeToCheck = time.includes(":00", 5) ? time : `${time}:00`;

  return dayExceptions.some((exception) =>
    exception.timeslots.includes(timeToCheck)
  );
};

const getExceptionName = (time, dayExceptions) => {
  if (!dayExceptions || dayExceptions.length === 0) return null;

  const timeToCheck = time.includes(":00", 5) ? time : `${time}:00`;

  const exception = dayExceptions.find((exception) =>
    exception.timeslots.includes(timeToCheck)
  );

  return exception ? exception.name : null;
};

const CourtAvailabilityDrawer = ({
  isOpen,
  onClose,
  court,
  club,
  globalDispatch,
  onSave,
  edit_api,
}) => {
  const [selectedTimeSlot, setSelectedTimeSlot] = useState([]);
  const [originalAvailability, setOriginalAvailability] = useState(null);
  const [isSaving, setIsSaving] = useState(false);
  const [clubData, setClubData] = useState({
    times: [],
    daysOff: [],
    exceptions: [],
  });
  const sdk = new MkdSDK();

  // Initialize availability data from court prop
  useEffect(() => {
    if (isOpen && court) {
      initializeCourtAvailability();
    }
  }, [isOpen, court]);

  // Parse club data for hours, days off, and exceptions
  useEffect(() => {
    if (club) {
      const times = club.times ? JSON.parse(club.times) : [];
      const daysOff = club.days_off ? JSON.parse(club.days_off) : [];
      const exceptions = club.exceptions ? JSON.parse(club.exceptions) : [];
      setClubData({ times, daysOff, exceptions });
    }
  }, [club]);

  const initializeCourtAvailability = () => {
    try {
      let availability = [];
      if (court.availability) {
        availability = JSON.parse(court.availability);
      }

      // Initialize with all days having empty timeslots
      const initialAvailability = days.map((day) => ({
        day: day.toLowerCase(),
        timeslots: [],
      }));

      // If there's existing availability, update the corresponding days
      if (
        availability &&
        Array.isArray(availability) &&
        availability.length > 0
      ) {
        availability.forEach((dayData) => {
          const index = initialAvailability.findIndex(
            (day) => day.day === dayData.day.toLowerCase()
          );
          if (index !== -1) {
            initialAvailability[index].timeslots = dayData.timeslots;
          }
        });
      }

      setSelectedTimeSlot(initialAvailability);
      setOriginalAvailability(availability || []);
    } catch (error) {
      console.error("Error parsing court availability:", error);
      showToast(
        globalDispatch,
        "Error loading court availability",
        3000,
        "error"
      );
    }
  };

  // Check if there are changes to save
  const hasChanges = () => {
    if (!selectedTimeSlot || !originalAvailability) return false;

    const currentSelection = formatSelectedTimes();
    const originalFiltered = Array.isArray(originalAvailability)
      ? originalAvailability.filter(
          (day) => day.timeslots && day.timeslots.length > 0
        )
      : [];

    if (currentSelection.length === 0 && originalFiltered.length > 0)
      return true;
    if (currentSelection.length > 0 && originalFiltered.length === 0)
      return true;
    if (currentSelection.length !== originalFiltered.length) return true;

    const originalDayMap = {};
    originalFiltered.forEach((day) => {
      originalDayMap[day.day.toLowerCase()] = [...day.timeslots].sort();
    });

    for (const currentDay of currentSelection) {
      const dayName = currentDay.day.toLowerCase();
      const originalTimeslots = originalDayMap[dayName];

      if (!originalTimeslots) return true;
      if (currentDay.timeslots.length !== originalTimeslots.length) return true;

      const sortedCurrentTimeslots = [...currentDay.timeslots].sort();
      for (let i = 0; i < sortedCurrentTimeslots.length; i++) {
        const currentTime = sortedCurrentTimeslots[i].replace(/:00$/, "");
        const originalTime = originalTimeslots[i].replace(/:00$/, "");
        if (currentTime !== originalTime) return true;
      }
    }

    return false;
  };

  const formatSelectedTimes = () => {
    if (!selectedTimeSlot) return [];
    return selectedTimeSlot.filter((daySlot) => daySlot.timeslots.length > 0);
  };

  const handleTimeSelect = (time, day) => {
    // Check if the day is a club day off
    if (isClubDayOff(day, clubData.daysOff)) {
      showToast(globalDispatch, `${day} is a club day off`, 3000, "error");
      return;
    }

    // Check if the time is within club hours
    if (!isTimeInClubHours(time, clubData.times)) {
      showToast(
        globalDispatch,
        "This time is outside club hours",
        3000,
        "error"
      );
      return;
    }

    // Check if the time is an exception time
    const dayExceptions = getDayExceptions(day, clubData.exceptions);
    if (isExceptionTime(time, dayExceptions)) {
      const exceptionName = getExceptionName(time, dayExceptions);
      showToast(
        globalDispatch,
        `This time is marked as "${exceptionName || "Exception"}"`,
        3000,
        "warning"
      );
      // We still allow selection but show a warning
    }

    setSelectedTimeSlot((prev) => {
      const updatedSlots = prev.map((daySlot) => {
        if (daySlot.day === day.toLowerCase()) {
          const timeWithoutSeconds = time.replace(":00", "");
          const timeExists = daySlot.timeslots.some(
            (t) => t === time || t === timeWithoutSeconds
          );
          if (!timeExists) {
            return {
              ...daySlot,
              timeslots: [...daySlot.timeslots, time].sort(),
            };
          }
        }
        return daySlot;
      });

      const dayExists = updatedSlots.some(
        (slot) => slot.day === day.toLowerCase()
      );
      if (!dayExists) {
        updatedSlots.push({
          day: day.toLowerCase(),
          timeslots: [time],
        });
      }

      return updatedSlots;
    });
  };

  const handleDeleteTime = (time, day) => {
    setSelectedTimeSlot((prev) => {
      return prev
        .map((daySlot) => {
          if (daySlot.day === day.toLowerCase()) {
            const updatedTimeslots = daySlot.timeslots.filter(
              (t) => t !== time && t !== time.replace(":00", "")
            );

            if (updatedTimeslots.length === 0) {
              return null;
            }

            return {
              ...daySlot,
              timeslots: updatedTimeslots,
            };
          }
          return daySlot;
        })
        .filter(Boolean);
    });
  };

  const isSelected = (time, day) => {
    const daySlot = selectedTimeSlot?.find(
      (daySlot) => daySlot.day === day.toLowerCase()
    );
    if (!daySlot) return false;

    const timeWithoutSeconds = time.replace(":00", "");
    return daySlot.timeslots.some(
      (t) => t === time || t.replace(":00", "") === timeWithoutSeconds
    );
  };

  const onSaveChanges = async () => {
    try {
      setIsSaving(true);
      const formattedData = formatSelectedTimes();

      // Use the profile edit API to save court availability
      await sdk.callRawAPI(
        edit_api,
        {
          courts: [
            {
              court_id: court.id,
              availability: formattedData,
            },
          ],
        },
        "POST"
      );

      showToast(
        globalDispatch,
        "Court availability updated successfully",
        3000,
        "success"
      );

      setOriginalAvailability(formattedData);
      if (onSave) {
        onSave();
      }
      onClose();
    } catch (error) {
      console.error("Error saving court availability:", error);
      showToast(
        globalDispatch,
        "Failed to update court availability",
        3000,
        "error"
      );
    } finally {
      setIsSaving(false);
    }
  };

  const handleDiscard = () => {
    // Reset to original availability
    initializeCourtAvailability();
    onClose();
  };

  if (!court) return null;

  return (
    <BottomDrawer
      isOpen={isOpen}
      onClose={onClose}
      title={`Court Availability - ${court.name}`}
      onDiscard={handleDiscard}
      discardLabel="Discard"
      showActions={hasChanges()}
      saveLabel="Save changes"
      onSave={onSaveChanges}
      isSubmitting={isSaving}
    >
      <div className="p-4">
        <div className="mb-4">
          <p className="text-sm text-gray-600">
            Set the available time slots for this court. Users will only be able
            to book during these times.
          </p>
        </div>

        <div className="overflow-x-auto pb-4">
          <TimeSlotGrid
            days={days}
            isSelected={isSelected}
            handleTimeSelect={handleTimeSelect}
            handleDeleteTime={handleDeleteTime}
            renderTimeSlotContent={(timeSlot, day) => {
              // Check if day is a club day off
              if (isClubDayOff(day, clubData.daysOff)) {
                return (
                  <div className="absolute inset-0 flex items-center justify-center bg-red-50 text-xs font-medium text-red-500">
                    Club Closed
                  </div>
                );
              }

              // Check if time is outside club hours
              if (!isTimeInClubHours(timeSlot.value, clubData.times)) {
                return (
                  <div className="absolute inset-0 flex items-center justify-center bg-red-50 text-xs font-medium text-red-500">
                    Club Closed
                  </div>
                );
              }

              // Check if time is an exception
              const dayExceptions = getDayExceptions(day, clubData.exceptions);
              if (isExceptionTime(timeSlot.value, dayExceptions)) {
                const exceptionName = getExceptionName(
                  timeSlot.value,
                  dayExceptions
                );
                return (
                  <div className="absolute inset-0 flex items-center justify-center bg-orange-50 text-xs font-medium text-orange-500">
                    <span className="truncate">
                      {exceptionName || "Exception"}
                    </span>
                  </div>
                );
              }

              return null;
            }}
            disableTimeSlot={(timeSlot, day) => {
              // Disable if day is a club day off
              if (isClubDayOff(day, clubData.daysOff)) {
                return true;
              }

              // Disable if time is outside club hours
              if (!isTimeInClubHours(timeSlot.value, clubData.times)) {
                return true;
              }

              // We don't disable exception times, just show a warning when selected
              return false;
            }}
          />
        </div>
      </div>
    </BottomDrawer>
  );
};

export default CourtAvailabilityDrawer;

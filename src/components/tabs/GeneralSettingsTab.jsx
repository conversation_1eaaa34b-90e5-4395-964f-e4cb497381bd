import React, { useState, useEffect } from "react";
import { EllipsisVerticalIcon } from "@heroicons/react/24/outline";
import { convertTo12Hour } from "../../Utils/utils";
import MkdSDK from "../../Utils/MkdSDK";
import { showToast } from "../../Context/Global";
import { actionLogTypes, logActivity } from "../../Utils/utils";
import CourtAvailabilityDrawer from "../drawers/CourtAvailabilityDrawer";

const GeneralSettingsTab = ({
  club,
  courts,
  exceptions,
  setShowEditModal,
  searchQuery,
  setSearchQuery,
  filteredCourts,
  activeDropdown,
  setActiveDropdown,
  dropdownPosition,
  setDropdownPosition,
  setSelectedCourtForEdit,
  setShowEditCourtModal,
  setSelectedCourtForDelete,
  setShowDeleteCourtModal,
  setShowAddCourtModal,
  sports,
  globalDispatch,
  edit_api,
  fetchSettings,
}) => {
  const [allowUserCourtSelection, setAllowUserCourtSelection] = useState(
    club?.allow_user_court_selection === 1
  );
  const [isSaving, setIsSaving] = useState(false);
  const [courtSettings, setCourtSettings] = useState([]);
  const [isCourtSettingsSaving, setIsCourtSettingsSaving] = useState(false);
  const [showAvailabilityModal, setShowAvailabilityModal] = useState(false);
  const [selectedCourtForAvailability, setSelectedCourtForAvailability] =
    useState(null);
  const sdk = new MkdSDK();
  const user_id = localStorage.getItem("user");

  // Initialize court settings with existing data or defaults
  useEffect(() => {
    if (courts && courts.length > 0) {
      const initialSettings = courts.map((court) => ({
        id: court.id,
        name: court.name,
        sport_id: court.sport_id,
        type: court.type || "",
        sub_type: court.sub_type || "",
        allow_reservation: court.allow_reservation !== false, // Default to true if not set
        allow_lesson: court.allow_lesson !== false, // Default to true if not set
        allow_clinic: court.allow_clinic !== false, // Default to true if not set
        allow_buddy: court.allow_buddy !== false, // Default to true if not set
      }));
      setAllowUserCourtSelection(club?.allow_user_court_selection === 1);
      setCourtSettings(initialSettings);
    }
  }, [courts]);

  const handleToggleUserCourtSelection = async () => {
    const newValue = !allowUserCourtSelection;
    setIsSaving(true);

    try {
      // Call API to update the setting
      await sdk.callRawAPI(
        edit_api,
        { allow_user_court_selection: newValue ? 1 : 0 },
        "POST"
      );

      // Log the activity
      await logActivity(sdk, {
        user_id,
        activity_type: actionLogTypes.court_management,
        action_type: actionLogTypes.UPDATE,
        data: { allow_user_court_selection: newValue ? 1 : 0 },
        club_id: club?.id,
        description: `${
          newValue ? "Enabled" : "Disabled"
        } user court selection`,
      });

      setAllowUserCourtSelection(newValue);
      showToast(
        globalDispatch,
        `User court selection ${
          newValue ? "enabled" : "disabled"
        } successfully`,
        3000,
        "success"
      );

      // Refresh the data
      if (fetchSettings) {
        fetchSettings();
      }
    } catch (error) {
      console.error("Error updating user court selection setting:", error);
      showToast(globalDispatch, "Error updating setting", 3000, "error");
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveCourtSettings = async () => {
    setIsCourtSettingsSaving(true);
    try {
      // Format the data for the API
      const formattedCourts = courtSettings.map((court) => ({
        id: court.id,
        allow_reservation: court.allow_reservation,
        allow_lesson: court.allow_lesson,
        allow_clinic: court.allow_clinic,
        allow_buddy: court.allow_buddy,
      }));

      // Call the API to update court settings
      await sdk.callRawAPI(
        edit_api,
        { court_settings: formattedCourts },
        "POST"
      );

      // Log the activity
      await logActivity(sdk, {
        user_id,
        activity_type: actionLogTypes.court_management,
        action_type: actionLogTypes.UPDATE,
        data: formattedCourts,
        club_id: club?.id,
        description: "Updated court usage settings",
      });

      showToast(
        globalDispatch,
        "Court settings saved successfully",
        3000,
        "success"
      );

      // Refresh the data
      if (fetchSettings) {
        fetchSettings();
      }
    } catch (error) {
      console.error("Error saving court settings:", error);
      showToast(globalDispatch, "Error saving court settings", 3000, "error");
    } finally {
      setIsCourtSettingsSaving(false);
    }
  };

  return (
    <div className="mt-8 grid grid-cols-1 gap-6 lg:grid-cols-2">
      {/* Club Settings Card */}
      <div className="h-fit rounded-lg bg-white shadow">
        <div className="mb-6 flex items-center justify-between rounded-lg bg-[#F6F8FA] px-6 py-3">
          <h2 className="text-lg font-medium">Club settings</h2>
          <button
            className="text-sm font-medium text-gray-600"
            onClick={() => setShowEditModal(true)}
          >
            Edit
          </button>
        </div>

        <div className="flex flex-col divide-y divide-gray-200 p-6">
          <div className="py-3">
            <h3 className="text-sm font-medium text-gray-500">
              GENERAL OPENING HOURS
            </h3>
            <p className="mt-1">
              {club?.times && JSON.parse(club?.times).length > 0
                ? JSON.parse(club?.times)?.map((time) => (
                    <div key={time.from}>
                      {convertTo12Hour(time.from)} -{" "}
                      {convertTo12Hour(time.until)}
                    </div>
                  ))
                : "N/A"}
            </p>
          </div>

          <div className="py-3">
            <h3 className="text-sm font-medium text-gray-500">DAYS OFF</h3>
            <p className="mt-1">
              {club?.days_off ? JSON.parse(club?.days_off)?.join(", ") : "N/A"}
            </p>
          </div>

          <div className="py-3">
            <h3 className="text-sm font-medium text-gray-500">TOTAL COURTS</h3>
            <p className="mt-1">{!courts?.length ? "N/A" : courts?.length}</p>
          </div>

          <div className="py-3">
            <h3 className="text-sm font-medium text-gray-500">
              SCHEDULED EXCEPTIONS
            </h3>
            <div className="flex items-center justify-between">
              <p className="mt-1">{exceptions?.length}</p>
              {/* <button className="text-sm font-medium text-black underline">
                View all
              </button> */}
            </div>
          </div>

          <div className="py-3">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-gray-500">
                ALLOW USERS TO SELECT COURT
              </h3>
              <button
                onClick={handleToggleUserCourtSelection}
                disabled={isSaving}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  allowUserCourtSelection ? "bg-primaryBlue" : "bg-gray-200"
                } ${isSaving ? "cursor-not-allowed opacity-50" : ""}`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    allowUserCourtSelection ? "translate-x-6" : "translate-x-1"
                  }`}
                />
              </button>
            </div>
            <p className="mt-1 text-xs text-gray-500">
              When enabled, users can select their preferred court during
              reservation.
            </p>
          </div>
        </div>
      </div>

      {/* Courts Card */}
      <div className="h-fit rounded-lg bg-white p-6 shadow">
        <div className="mb-6 flex items-center justify-between">
          <h2 className="text-lg font-medium">Courts</h2>
          <button
            className="rounded-xl bg-primaryBlue px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 disabled:bg-gray-400"
            onClick={handleSaveCourtSettings}
            disabled={isCourtSettingsSaving}
          >
            {isCourtSettingsSaving ? "Saving..." : "Save Court Settings"}
          </button>
        </div>

        <div className="mb-4 flex gap-4">
          <div className="relative w-96">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <svg
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M9.25 2.5C12.976 2.5 16 5.524 16 9.25C16 12.976 12.976 16 9.25 16C5.524 16 2.5 12.976 2.5 9.25C2.5 5.524 5.524 2.5 9.25 2.5ZM9.25 14.5C12.1502 14.5 14.5 12.1502 14.5 9.25C14.5 6.349 12.1502 4 9.25 4C6.349 4 4 6.349 4 9.25C4 12.1502 6.349 14.5 9.25 14.5ZM15.6137 14.5532L17.7355 16.6742L16.6742 17.7355L14.5532 15.6137L15.6137 14.5532Z"
                  fill="#525866"
                />
              </svg>
            </div>
            <input
              type="text"
              placeholder="Search by name, sport, type, sub-type..."
              className="w-full rounded-lg border border-gray-300 px-4 py-2 pl-10 text-sm focus:border-blue-500 focus:outline-none"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <div className="overflow-x-auto overflow-y-hidden">
          <table className="min-w-full border-separate border-spacing-y-2">
            <thead>
              <tr className="text-left text-sm text-gray-500">
                <th className="px-4 pb-4">Name</th>
                <th className="px-4 pb-4">Sport</th>
                <th className="px-4 pb-4">Type</th>
                <th className="px-4 pb-4">Sub-type</th>
                <th className="px-4 pb-4"></th>
              </tr>
            </thead>
            {filteredCourts?.length > 0 ? (
              filteredCourts?.map((court) => (
                <tbody key={court.id}>
                  <tr className="overflow-hidden">
                    <td className="rounded-l-xl bg-gray-100 px-4 py-3">
                      {!court.name ? "N/A" : court.name}
                    </td>
                    <td className="bg-gray-100 px-4 py-3">
                      {!court.sport_id
                        ? "N/A"
                        : sports.find((s) => s.id == court.sport_id)?.name}
                    </td>
                    <td className="bg-gray-100 px-4 py-3">
                      {court.type || "--"}
                    </td>
                    <td className="bg-gray-100 px-4 py-3">
                      {court.sub_type || "--"}
                    </td>

                    <td className="rounded-r-xl bg-gray-100 px-4 py-3">
                      <div className="relative">
                        <button
                          onClick={(e) => {
                            const buttonRect =
                              e.currentTarget.getBoundingClientRect();
                            const tableRect = e.currentTarget
                              .closest("table")
                              .getBoundingClientRect();
                            const isLastRow =
                              buttonRect.bottom > tableRect.bottom - 100;
                            setDropdownPosition(isLastRow ? "top" : "bottom");
                            setActiveDropdown(
                              activeDropdown === court.id ? null : court.id
                            );
                          }}
                          className="text-gray-400 hover:text-gray-500"
                        >
                          <EllipsisVerticalIcon className="h-5 w-5" />
                        </button>

                        {activeDropdown === court.id && (
                          <div
                            className={`absolute right-0 z-10 w-36 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 ${
                              dropdownPosition === "top"
                                ? "bottom-full mb-1"
                                : "top-full mt-1"
                            }`}
                          >
                            <div className="py-1" role="menu">
                              <button
                                className="flex w-full items-center gap-1 px-4 py-2 text-sm text-gray-600 hover:bg-gray-100"
                                onClick={() => {
                                  setSelectedCourtForEdit(court);
                                  setShowEditCourtModal(true);
                                  setActiveDropdown(null);
                                }}
                              >
                                <svg
                                  width="20"
                                  height="20"
                                  viewBox="0 0 20 20"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    d="M11.041 5.20756L13.5768 2.67181C13.9022 2.34638 14.4298 2.34637 14.7553 2.67181L17.3268 5.2433C17.6522 5.56874 17.6522 6.09638 17.3268 6.42181L14.791 8.95756M11.041 5.20756L2.53509 13.7135C2.37881 13.8698 2.29102 14.0817 2.29102 14.3027V17.7076H5.69584C5.91685 17.7076 6.12881 17.6198 6.28509 17.4635L14.791 8.95756M11.041 5.20756L14.791 8.95756"
                                    stroke="#868C98"
                                    strokeWidth="1.5"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                  />
                                </svg>
                                Edit
                              </button>

                              <button
                                className="flex w-full items-center gap-1 px-4 py-2 text-sm text-gray-600 hover:bg-gray-100"
                                onClick={() => {
                                  setSelectedCourtForAvailability(court);
                                  setShowAvailabilityModal(true);
                                  setActiveDropdown(null);
                                }}
                              >
                                <svg
                                  width="20"
                                  height="20"
                                  viewBox="0 0 20 20"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    d="M6.66667 1.66667V4.16667M13.3333 1.66667V4.16667M2.5 6.66667H17.5M4.16667 3.33333H15.8333C16.7538 3.33333 17.5 4.07953 17.5 5V16.6667C17.5 17.5871 16.7538 18.3333 15.8333 18.3333H4.16667C3.24619 18.3333 2.5 17.5871 2.5 16.6667V5C2.5 4.07953 3.24619 3.33333 4.16667 3.33333Z"
                                    stroke="#868C98"
                                    strokeWidth="1.5"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                  />
                                </svg>
                                Availability
                              </button>

                              <button
                                className="flex w-full items-center gap-1 px-4 py-2 text-sm text-gray-600 hover:bg-gray-100"
                                onClick={() => {
                                  setSelectedCourtForDelete(court.id);
                                  setShowDeleteCourtModal(true);
                                  setActiveDropdown(null);
                                }}
                              >
                                <svg
                                  width="20"
                                  height="20"
                                  viewBox="0 0 20 20"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    d="M4.79167 4.79102V16.8743C4.79167 17.3346 5.16476 17.7077 5.625 17.7077H14.375C14.8352 17.7077 15.2083 17.3346 15.2083 16.8743V4.79102M4.79167 4.79102H15.2083M4.79167 4.79102H3.125M15.2083 4.79102H16.875M11.6667 8.95768V13.541M8.33333 8.95768V13.541M7.5 4.79102C7.5 3.4103 8.61929 2.29102 10 2.29102C11.3807 2.29102 12.5 3.4103 12.5 4.79102"
                                    stroke="#868C98"
                                    strokeWidth="1.5"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                  />
                                </svg>
                                Delete
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    </td>
                  </tr>
                </tbody>
              ))
            ) : (
              <div className="flex w-full items-center justify-center text-center text-sm text-gray-500">
                No courts found
              </div>
            )}
          </table>
        </div>

        <button
          className="mt-6 rounded-md px-4 py-2 text-sm font-medium text-black underline"
          onClick={() => setShowAddCourtModal(true)}
        >
          + Add court
        </button>
      </div>

      {/* Court Availability Drawer */}
      <CourtAvailabilityDrawer
        isOpen={showAvailabilityModal}
        onClose={() => {
          setShowAvailabilityModal(false);
          setSelectedCourtForAvailability(null);
        }}
        court={selectedCourtForAvailability}
        club={club}
        globalDispatch={globalDispatch}
        edit_api={edit_api}
        onSave={() => {
          // Refresh the data after saving
          if (fetchSettings) {
            fetchSettings();
          }
        }}
      />
    </div>
  );
};

export default GeneralSettingsTab;

import { InteractiveButton } from "../InteractiveButton";

export const CancelReservationModal = ({
  isOpen,
  onClose,
  onCancel,
  loading = false,
}) => {
  return (
    <div
      className={`fixed inset-0 z-[999999] flex items-center justify-center ${
        isOpen ? "" : "hidden"
      }`}
    >
      <div className="fixed inset-0 bg-black opacity-50"></div>
      <div className="relative z-50 w-full max-w-md rounded-3xl bg-white">
        <div className="p-6">
          <h2 className="mb-4 text-xl font-medium">Cancel reservation</h2>
          <div className="flex items-start justify-center rounded-xl bg-[#F17B2C] p-3">
            <div>
              <div className="text-sm font-medium text-white">IMPORTANT</div>
              <p className="text-sm text-white">
                Are you sure you want to cancel this reservation?
              </p>
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-3 border-t px-6 py-4">
          <button
            onClick={onClose}
            className="rounded-xl border border-gray-300 px-4 py-2 hover:bg-gray-50"
          >
            Cancel
          </button>
          <InteractiveButton
            onClick={onCancel}
            className={`rounded-xl bg-green-800 px-4 py-2 text-white hover:bg-green-700`}
            loading={loading}
          >
            Cancel reservation
          </InteractiveButton>
        </div>
      </div>
    </div>
  );
};

export default CancelReservationModal;

import React, { useState, useContext } from "react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { HeartIcon as HeartIconSolid } from "@heroicons/react/24/solid";
import { FiUser, FiHeart } from "react-icons/fi";
import { GlobalContext, showToast } from "Context/Global";
import { Tooltip } from "react-tooltip";
import { ntrpOptions } from "Utils/utils";

const AddPlayers = ({
  players = [],
  groups = [],
  selectedPlayers = [],
  onPlayerToggle,
  isFindBuddyEnabled,
  onFindBuddyToggle,
  playersNeeded,
  onPlayersNeededChange,
  showPlayersNeeded = true,
  showCurrentGroup = true,
  showAddReservationToFindBuddy = true,
  maximumPlayers = 4,
  userProfile = null,
  setSelectedPlayers,
  onNtrpMinChange,
  onNtrpMaxChange,
  onShortBioChange,
  initialNtrpMin = 3.5,
  initialNtrpMax = 3.5,
  initialShortBio = "",
  familyMembers = [],
  currentUser = null,
  onCurrentUserChange,
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedGroup, setSelectedGroup] = useState(null);
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const [shortBio, setShortBio] = useState(initialShortBio);
  const [ntrpMin, setNtrpMin] = useState(initialNtrpMin);
  const [ntrpMax, setNtrpMax] = useState(initialNtrpMax);
  const user_id = parseInt(localStorage.getItem("user"));

  // Helper function to identify if a player is a family member
  const isFamilyMember = (player) => {
    return player.guardian === user_id;
  };

  // Helper function to filter players based on guardian logic
  const filterPlayersByGuardian = (playersList) => {
    const currentUserId = localStorage.getItem("user");
    return playersList.filter((player) => {
      // Filter out other users' family members
      // If player has a guardian and that guardian is not the current user, exclude them
      if (player.guardian && player.guardian !== user_id) {
        return false;
      }
      return true;
    });
  };

  // Helper function to filter group members based on guardian logic
  const filterGroupMembers = (members) => {
    const currentUserId = localStorage.getItem("user");
    return members.filter((member) => {
      // Filter out other users' family members
      // If member has a guardian and that guardian is not the current user, exclude them
      if (member.guardian && member.guardian !== user_id) {
        return false;
      }
      return true;
    });
  };

  // Apply guardian filtering to players
  const guardianFilteredPlayers = filterPlayersByGuardian(players || []);

  // Apply guardian filtering to groups
  const guardianFilteredGroups = groups.map((group) => ({
    ...group,
    members: filterGroupMembers(group.members || []),
  }));

  const filteredPlayers = guardianFilteredPlayers?.filter((player) => {
    const fullName = `${player.first_name} ${player.last_name}`.toLowerCase();
    return fullName.includes(searchQuery.toLowerCase());
  });

  const isSelected = (playerId) => {
    return selectedPlayers.some((player) => player.id === playerId);
  };

  const handleIncrement = () => {
    // Calculate remaining slots based on maximumPlayers and current selected players
    const remainingSlots = maximumPlayers - selectedPlayers.length;
    onPlayersNeededChange(Math.min(playersNeeded + 1, remainingSlots));
  };

  const handleDecrement = () => {
    onPlayersNeededChange(Math.max(playersNeeded - 1, 0));
  };

  const togglePlayer = (player) => {
    const isPlayerSelected = selectedPlayers.some((p) => p.id === player.id);
    // If player is already selected, check if it's the current user before allowing removal
    if (isPlayerSelected) {
      const activeUser = currentUser || userProfile;
      if (player.id === activeUser?.id) {
        showToast(
          globalDispatch,
          "You cannot remove the primary player from the reservation",
          3000,
          "warning"
        );
        return;
      }
      onPlayerToggle(player);
      return;
    }

    // If trying to add a player, check the limit
    if (selectedPlayers.length >= maximumPlayers) {
      showToast(
        globalDispatch,
        `Maximum ${maximumPlayers} players allowed (including yourself)`,
        3000,
        "warning"
      );
      return;
    }

    // If we get here, we can add the player
    onPlayerToggle(player);
  };

  const currentUserId = localStorage.getItem("user");
  React.useEffect(() => {
    if (players.length > 0) {
      const activeUser =
        currentUser ||
        userProfile ||
        players.find((player) => player.id === parseInt(currentUserId));

      if (activeUser) {
        setSelectedPlayers((prev) => {
          // Check if the active user is already in the list
          const isActiveUserInList = prev.some(
            (player) => player.id === activeUser.id
          );

          if (!isActiveUserInList) {
            // Add the active user to the beginning of the list
            return [activeUser, ...prev];
          }

          // If active user is already in the list, don't add them again
          return prev;
        });
      }
    }
  }, [currentUserId, players, currentUser, userProfile]); // Removed selectedPlayers from dependencies to avoid infinite loop

  return (
    <div className="h-fit rounded-lg bg-white shadow-5">
      <div className="rounded-lg bg-gray-50 p-4 text-center">
        <h2 className="text-base font-medium">
          Add players ({selectedPlayers.length}/{maximumPlayers})
        </h2>
      </div>
      <div className="p-4">
        <div className="mb-4">
          <div className="flex items-center gap-1 rounded-lg border border-gray-300 px-2 focus:border-blue-500 focus:outline-none">
            <span className="w-5">
              <svg
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M9.25 2.5C12.976 2.5 16 5.524 16 9.25C16 12.976 12.976 16 9.25 16C5.524 16 2.5 12.976 2.5 9.25C2.5 5.524 5.524 2.5 9.25 2.5ZM9.25 14.5C12.1502 14.5 14.5 12.1502 14.5 9.25C14.5 6.349 12.1502 4 9.25 4C6.349 4 4 6.349 4 9.25C4 12.1502 6.349 14.5 9.25 14.5ZM15.6137 14.5532L17.7355 16.6742L16.6742 17.7355L14.5532 15.6137L15.6137 14.5532Z"
                  fill="#525866"
                />
              </svg>
            </span>
            <input
              type="text"
              placeholder="search by name"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full border-none bg-transparent focus:outline-none focus:ring-0"
            />
          </div>
        </div>

        {/* Current User Dropdown - only show if family members exist */}
        {familyMembers.length > 0 && (
          <div className="mb-4">
            <label className="mb-2 block text-sm font-medium text-gray-700">
              Playing as
            </label>
            <div className="relative">
              <select
                value={currentUser?.id || userProfile?.id || ""}
                onChange={(e) => {
                  const selectedId = parseInt(e.target.value);
                  let selectedUser;

                  if (selectedId === userProfile?.id) {
                    selectedUser = userProfile;
                  } else {
                    selectedUser = familyMembers.find(
                      (member) => member.id === selectedId
                    );
                  }

                  // Only call the change handler if we actually have a different user
                  if (
                    selectedUser &&
                    onCurrentUserChange &&
                    selectedUser.id !== currentUser?.id
                  ) {
                    onCurrentUserChange(selectedUser);
                  }
                }}
                className="w-full appearance-none rounded-lg border border-gray-300 bg-white px-3 py-2 pr-8 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value={userProfile?.id}>
                  {userProfile?.first_name} {userProfile?.last_name} (You)
                </option>
                {familyMembers.map((member) => (
                  <option key={member.id} value={member.id}>
                    {member.first_name} {member.last_name} (
                    {member.family_role || "Family Member"})
                  </option>
                ))}
              </select>
            </div>
            <p className="mt-1 text-xs text-gray-500">
              Select who is playing in this reservation
            </p>
          </div>
        )}

        {selectedPlayers.length > 0 && (
          <div className="mb-4 flex flex-wrap gap-2">
            {selectedPlayers.map((player) => {
              const isFamily = isFamilyMember(player);
              return (
                <div
                  key={player.id}
                  className={`flex items-center gap-2 rounded-full py-1 pl-1 pr-2 ${
                    isFamily
                      ? "border border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50"
                      : "bg-gray-100"
                  }`}
                >
                  <div className="relative h-6 w-6 overflow-hidden rounded-full bg-gray-200">
                    <img
                      src={player?.photo || "/default-avatar.png"}
                      alt={`${player.first_name} ${player.last_name}`}
                      className="h-full w-full object-cover"
                    />
                    {isFamily && (
                      <div className="absolute -bottom-0.5 -right-0.5 flex h-3.5 w-3.5 items-center justify-center rounded-full bg-blue-500 ring-1 ring-white">
                        <HeartIconSolid className="h-2 w-2 text-white" />
                      </div>
                    )}
                  </div>
                  <div className="flex flex-col">
                    <div className="flex items-center gap-1">
                      <span className="text-sm">{`${player.first_name} ${player.last_name}`}</span>
                      {isFamily && (
                        <div className="flex items-center gap-1">
                          <span className="inline-flex items-center gap-0.5 rounded-full bg-blue-100 px-1.5 py-0.5 text-xs font-medium text-blue-700">
                            <HeartIconSolid className="h-2.5 w-2.5" />
                            Family
                          </span>
                          {player.family_role && (
                            <span className="text-xs font-medium capitalize text-blue-600">
                              {player.family_role}
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                  {player.id !== (currentUser?.id || userProfile?.id) && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        togglePlayer(player);
                      }}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <XMarkIcon className="h-4 w-4" />
                    </button>
                  )}
                </div>
              );
            })}
          </div>
        )}
        <div className="flex flex-wrap items-center gap-2 p-2">
          <button
            onClick={() => setSelectedGroup(null)}
            className={`rounded-full border px-2 py-1 text-xs transition-colors ${
              !selectedGroup
                ? "border-primaryBlue bg-primaryBlue/10 text-primaryBlue"
                : "border-gray-300 text-gray-500 hover:bg-gray-50"
            }`}
          >
            All Players
          </button>
          {guardianFilteredGroups.map((group) => (
            <div key={group.group_id} className="space-y-2">
              <button
                onClick={() => setSelectedGroup(group.group_id)}
                className={`rounded-full border px-2 py-1 text-xs transition-colors ${
                  selectedGroup === group.group_id
                    ? "border-primaryBlue bg-primaryBlue/10 text-primaryBlue"
                    : "border-gray-300 text-gray-500 hover:bg-gray-50"
                }`}
              >
                {group.group_name}
              </button>
            </div>
          ))}
        </div>
        <div className="max-h-64 space-y-2 overflow-y-auto rounded-xl bg-gray-50">
          {selectedGroup ? (
            // Show group members
            <div className="p-2">
              {guardianFilteredGroups
                .find((g) => g.group_id === selectedGroup)
                ?.members.map((member) => {
                  const isFamily = isFamilyMember(member);
                  return (
                    <div
                      key={member.id}
                      onClick={() => togglePlayer(member)}
                      className={`flex cursor-pointer items-center space-x-3 rounded-lg p-2 transition-colors hover:bg-white ${
                        isFamily
                          ? "border border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50"
                          : "hover:bg-gray-50"
                      }`}
                    >
                      <input
                        type="checkbox"
                        checked={isSelected(member.id)}
                        onChange={() => {}}
                        className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <div className="relative">
                        <div className="h-8 w-8 overflow-hidden rounded-full bg-gray-200 shadow-sm ring-2 ring-white">
                          {member.photo ? (
                            <img
                              src={member.photo}
                              alt={`${member.first_name} ${member.last_name}`}
                              className="h-full w-full object-cover"
                            />
                          ) : (
                            <div className="flex h-full w-full items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200">
                              <FiUser className="h-6 w-6 text-gray-400" />
                            </div>
                          )}
                        </div>
                        {/* Family member indicator */}
                        {isFamily && (
                          <div className="absolute -bottom-1 -right-1 z-50 flex h-6 w-6 items-center justify-center rounded-full bg-blue-500 ring-2 ring-white">
                            <FiHeart className="h-3 w-3 text-white" />
                          </div>
                        )}
                      </div>
                      <div className="flex flex-col">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-gray-900">
                            {member.first_name} {member.last_name}
                          </span>
                          {isFamily && (
                            <div>
                              <span className="inline-flex items-center gap-1 rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-700">
                                <HeartIconSolid className="h-3 w-3" />
                                Family
                              </span>
                              {member.family_role && (
                                <span className="text-xs font-medium capitalize text-blue-600">
                                  {member.family_role}
                                </span>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
            </div>
          ) : (
            // Show all players
            <div className="p-2">
              {filteredPlayers.length > 0 ? (
                filteredPlayers.map((player) => {
                  const isFamily = isFamilyMember(player);
                  return (
                    <div
                      key={player.id}
                      onClick={() => togglePlayer(player)}
                      className={`flex cursor-pointer items-center space-x-3 rounded-lg p-2 transition-colors hover:bg-white ${
                        isFamily
                          ? "border border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50"
                          : "hover:bg-gray-50"
                      }`}
                    >
                      <input
                        type="checkbox"
                        checked={isSelected(player.id)}
                        onChange={() => {}}
                        className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <div className="relative h-8 w-8 overflow-hidden rounded-full bg-gray-200">
                        <img
                          src={player?.photo || "/default-avatar.png"}
                          alt={`${player.first_name} ${player.last_name}`}
                          className="h-full w-full object-cover"
                        />
                        {isFamily && (
                          <div className="absolute -bottom-0.5 -right-0.5 flex h-3.5 w-3.5 items-center justify-center rounded-full bg-blue-500 ring-1 ring-white">
                            <HeartIconSolid className="h-2 w-2 text-white" />
                          </div>
                        )}
                      </div>
                      <div className="flex flex-col">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-gray-900">
                            {player.first_name} {player.last_name}
                          </span>
                          {isFamily && (
                            <div>
                              <span className="inline-flex items-center gap-1 rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-700">
                                <HeartIconSolid className="h-3 w-3" />
                                Family
                              </span>
                              {player.family_role && (
                                <span className="text-xs font-medium capitalize text-blue-600">
                                  {player.family_role}
                                </span>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })
              ) : (
                <p className="text-center text-sm text-gray-500">
                  No players found
                </p>
              )}
            </div>
          )}
        </div>
        {showCurrentGroup && (
          <div className="flex items-center justify-between">
            <div className="mt-4 space-y-4">
              {selectedGroup && (
                <div className="flex items-center justify-between">
                  <span className="text-base ">Current group</span>
                  <span className="text-base ">
                    {
                      guardianFilteredGroups.find(
                        (g) => g.group_id === selectedGroup
                      )?.group_name
                    }
                  </span>
                </div>
              )}
              {showAddReservationToFindBuddy && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between gap-2">
                    <div className="flex items-center gap-2">
                      <div
                        className="relative inline-flex cursor-pointer"
                        onClick={onFindBuddyToggle}
                      >
                        <div
                          className={`h-6 w-11 rounded-full transition-colors duration-200 ease-in-out ${
                            isFindBuddyEnabled ? "bg-blue-600" : "bg-gray-200"
                          }`}
                        ></div>
                        <div
                          className={`absolute top-1 h-4 w-4 rounded-full bg-white transition-transform duration-200 ease-in-out ${
                            isFindBuddyEnabled ? "left-6" : "left-1"
                          }`}
                        ></div>
                      </div>
                      <span className="text-sm">
                        Add reservation to Find Buddy system
                      </span>
                    </div>
                    <div
                      data-tooltip-id="find-buddy-tooltip"
                      className="rounded-full p-1"
                    >
                      <svg
                        width="20"
                        height="20"
                        viewBox="0 0 20 20"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M8.95829 9.1665H9.99995L9.99996 13.5415M17.7083 9.99984C17.7083 14.257 14.2572 17.7082 9.99996 17.7082C5.74276 17.7082 2.29163 14.257 2.29163 9.99984C2.29163 5.74264 5.74276 2.2915 9.99996 2.2915C14.2572 2.2915 17.7083 5.74264 17.7083 9.99984Z"
                          stroke="#868C98"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M10 6.125C9.70089 6.125 9.45837 6.36751 9.45837 6.66667C9.45837 6.96582 9.70089 7.20833 10 7.20833C10.2992 7.20833 10.5417 6.96582 10.5417 6.66667C10.5417 6.36751 10.2992 6.125 10 6.125Z"
                          fill="#868C98"
                          stroke="#868C98"
                          strokeWidth="0.25"
                        />
                      </svg>
                    </div>
                    <Tooltip
                      id="find-buddy-tooltip"
                      place="top"
                      className="z-50 max-w-md rounded-lg border border-gray-200 bg-white p-4 text-gray-700 shadow-lg"
                      style={{
                        backgroundColor: "#fff",
                        border: "1px solid #e4e4e7",
                        color: "#000",
                        borderRadius: "12px",
                        padding: "12px",
                        maxWidth: "300px",
                      }}
                    >
                      Creates a request for other players on the app to join you
                      incase of shortage of players.
                    </Tooltip>
                  </div>

                  {isFindBuddyEnabled && (
                    <>
                      <div>
                        <label className="mb-3 block text-sm text-gray-500">
                          My group NTRP score
                        </label>
                        <div className="flex gap-4">
                          <div className="flex h-10 min-h-[40px] flex-1 shrink basis-0 items-start justify-center overflow-hidden rounded-xl border border-solid border-zinc-200 bg-white shadow-sm">
                            <div className="flex w-fit items-center justify-center gap-2 self-stretch bg-slate-50 px-2 py-2.5 text-neutral-400">
                              Min
                            </div>
                            <select
                              className="flex flex-1 shrink basis-0 items-center justify-between overflow-hidden border-l border-none border-zinc-200 bg-white px-2 py-2.5 text-gray-950 outline-none focus:outline-none focus:ring-0"
                              value={ntrpMin}
                              onChange={(e) => {
                                const value = e.target.value;
                                setNtrpMin(value);
                                if (onNtrpMinChange) onNtrpMinChange(value);
                              }}
                            >
                              {ntrpOptions.map((option) => (
                                <option key={option} value={option}>
                                  {option}
                                </option>
                              ))}
                            </select>
                          </div>

                          <div className="flex h-10 min-h-[40px] flex-1 shrink basis-0 items-start justify-center overflow-hidden rounded-xl border border-solid border-zinc-200 bg-white shadow-sm">
                            <div className="flex w-fit items-center justify-center gap-2 self-stretch bg-slate-50 px-2 py-2.5 text-neutral-400">
                              Max
                            </div>
                            <select
                              className="flex flex-1 shrink basis-0 items-center justify-between overflow-hidden border-l border-none border-zinc-200 bg-white px-2 py-2.5 text-gray-950 outline-none focus:outline-none focus:ring-0"
                              value={ntrpMax}
                              onChange={(e) => {
                                const value = e.target.value;
                                setNtrpMax(value);
                                if (onNtrpMaxChange) onNtrpMaxChange(value);
                              }}
                            >
                              {ntrpOptions.map((option) => (
                                <option key={option} value={option}>
                                  {option}
                                </option>
                              ))}
                            </select>
                          </div>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium">
                          Short bio{" "}
                          <span className="text-gray-500">(Optional)</span>
                        </label>
                        <textarea
                          value={shortBio}
                          onChange={(e) => {
                            const value = e.target.value;
                            setShortBio(value);
                            if (onShortBioChange) onShortBioChange(value);
                          }}
                          className="mt-2 w-full rounded-xl border border-gray-300 p-2"
                          rows={3}
                        />
                      </div>
                    </>
                  )}
                </div>
              )}
              {showPlayersNeeded && (
                <div className="flex items-center justify-between">
                  <span className="text-sm">Players needed</span>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={handleDecrement}
                      disabled={playersNeeded === 0}
                      className={`flex h-8 w-8 items-center justify-center rounded-lg border border-gray-300 text-gray-500 hover:bg-gray-50 ${
                        playersNeeded === 0
                          ? "cursor-not-allowed opacity-50"
                          : ""
                      }`}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                        className="h-5 w-5"
                      >
                        <path
                          fillRule="evenodd"
                          d="M4 10a.75.75 0 01.75-.75h10.5a.75.75 0 010 1.5H4.75A.75.75 0 014 10z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </button>
                    <span className="w-8 text-center">{playersNeeded}</span>
                    <button
                      onClick={handleIncrement}
                      disabled={
                        playersNeeded >= maximumPlayers - selectedPlayers.length
                      }
                      className={`flex h-8 w-8 items-center justify-center rounded-lg border border-gray-300 text-gray-500 hover:bg-gray-50 ${
                        playersNeeded >= maximumPlayers - selectedPlayers.length
                          ? "cursor-not-allowed opacity-50"
                          : ""
                      }`}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                        className="h-5 w-5"
                      >
                        <path d="M10.75 4.75a.75.75 0 00-1.5 0v4.5h-4.5a.75.75 0 000 1.5h4.5v4.5a.75.75 0 001.5 0v-4.5h4.5a.75.75 0 000-1.5h-4.5v-4.5z" />
                      </svg>
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AddPlayers;

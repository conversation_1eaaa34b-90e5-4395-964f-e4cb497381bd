import React from "react";
import { AuthContext, tokenExpireError } from "Context/Auth";
import MkdSDK from "Utils/MkdSDK";
import { GlobalContext, showToast } from "Context/Global";
import { AiOutlineClose } from "react-icons/ai";
import { PaginationBar } from "Components/PaginationBar";
import _ from "lodash";
import SkeletonLoader from "Components/Skeleton/Skeleton";
import { Link } from "react-router-dom";
import TimeSlotPicker from "Components/TimeslotPicker";
import DeleteModal from "Components/Modals/DeleteModal";
import InvitationCard from "Components/InvitationCard";
import { BiSearch } from "react-icons/bi";
import { isArray } from "lodash";
import HistoryComponent from "Components/HistoryComponent";
import { actionLogTypes, activityLogTypes, logActivity } from "Utils/utils";
import FormattedPhoneNumber from "Components/Shared/FormattedPhoneNumber";
import { formatPhoneNumber } from "Utils/formatPhone";
import DataTable from "Components/Shared/DataTable";
import { useNavigate } from "react-router-dom";

let sdk = new MkdSDK();
const columns = [
  {
    header: "Name",
    accessor: "name",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Email",
    accessor: "email",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
  },
  {
    header: "Phone",
    accessor: "phone",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
  },
  {
    header: "Status",
    accessor: "status",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: true,
    mappings: { 1: "Active", 0: "Inactive" },
  },
  {
    header: "Sport",
    accessor: "sport_id",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
  },
  {
    header: "Action",
    accessor: "",
  },
];

const days = [
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
  "Sunday",
];

const ListCoaches = ({ club, sports }) => {
  const navigate = useNavigate();
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { dispatch } = React.useContext(AuthContext);
  const [data, setCurrentTableData] = React.useState([]);
  const [pageSize, setPageSize] = React.useState(10);
  const [pageCount, setPageCount] = React.useState(0);
  const [currentPage, setPage] = React.useState(1);
  const [canPreviousPage, setCanPreviousPage] = React.useState(false);
  const [canNextPage, setCanNextPage] = React.useState(false);
  const [loading, setLoading] = React.useState(true);
  const [showTimesAvailableModal, setShowTimesAvailableModal] =
    React.useState(false);
  const [selectedTimes, setSelectedTimes] = React.useState([]);
  const [selectedCoach, setSelectedCoach] = React.useState(null);
  const [submittingAvailability, setSubmittingAvailability] =
    React.useState(false);
  const [showDeleteModal, setShowDeleteModal] = React.useState(false);
  const [deletingCoach, setDeletingCoach] = React.useState(false);
  const [activeDeleteId, setActiveDeleteId] = React.useState(null);
  const [showContactInfo, setShowContactInfo] = React.useState(false);
  const [activeContactId, setActiveContactId] = React.useState(null);
  const [showInvitationCard, setShowInvitationCard] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState("");
  const [searchTimeout, setSearchTimeout] = React.useState(null);
  const [filterValues, setFilterValues] = React.useState({
    name: "",
    sport: "",
    status: "",
  });
  const user_id = localStorage.getItem("user");

  function previousPage() {
    getData(currentPage - 1, pageSize);
  }

  function nextPage() {
    getData(currentPage + 1, pageSize);
  }

  async function getData(page, limit, _, filters = {}) {
    setLoading(true);
    try {
      const queryParams = new URLSearchParams();
      const { first_name, last_name, sport_id, status } = filters;
      if (first_name) queryParams.append("first_name", first_name);
      if (last_name) queryParams.append("last_name", last_name);
      if (sport_id) queryParams.append("sport_id", sport_id);
      if (status !== undefined) queryParams.append("status", status);

      // Add pagination params
      queryParams.append("page", page);
      queryParams.append("limit", limit);

      const result = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/reservations/coaches/${
          club?.id
        }?${queryParams.toString()}`,
        {},
        "GET"
      );

      setCurrentTableData(result?.coaches || []);
      // Set pagination data if available in the response
      if (result?.pagination) {
        setPageCount(result.pagination.total_pages || 0);
        setPage(result.pagination.current_page || 1);
        setCanPreviousPage(result.pagination.current_page > 1);
        setCanNextPage(
          result.pagination.current_page < result.pagination.total_pages
        );
      }
    } catch (error) {
      setLoading(false);
      console.log("ERROR", error);
      tokenExpireError(dispatch, error.message);
    } finally {
      setLoading(false);
    }
  }

  // Apply filters from all search fields
  const applyFilters = () => {
    const filters = {};

    if (filterValues.name) {
      filters.first_name = filterValues.name;
    }

    if (filterValues.sport) {
      filters.sport_id = filterValues.sport;
    }

    if (filterValues.status !== "") {
      filters.status = filterValues.status;
    }

    getData(1, pageSize, {}, filters);
  };

  const onStatusSelect = (e) => {
    const value = e.target.value;
    setFilterValues({
      ...filterValues,
      status: value,
    });
  };

  const onSportSelect = (e) => {
    const value = e.target.value;
    setFilterValues({
      ...filterValues,
      sport: value,
    });
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "coaches",
      },
    });

    if (club?.id) {
      getData(currentPage, pageSize, {});
    }
  }, [club?.id]);

  const handleDelete = async ({ coach_id, coach_user_id }) => {
    setDeletingCoach(true);
    try {
      sdk.setTable("user");
      await sdk.callRestAPI({ id: coach_user_id }, "DELETE");
      sdk.setTable("coach");
      await sdk.callRestAPI({ id: coach_id }, "DELETE");

      await logActivity(sdk, {
        user_id,
        activity_type: activityLogTypes.coach_management,
        action_type: actionLogTypes.DELETE,
        data: { coach_id, coach_user_id },
        club_id: club?.id,
        description: "Deleted coach",
      });
      showToast(globalDispatch, "Coach deleted successfully", 3000, "success");
      setShowDeleteModal(false);
      getData(currentPage, pageSize); // Refresh the data
    } catch (error) {
      console.error("Error deleting coach:", error);
      tokenExpireError(dispatch, error.message);
    }
    setDeletingCoach(false);
  };

  const handleAvailabilityClick = (coach) => {
    setSelectedCoach(coach);
    setShowTimesAvailableModal(true);
    if (coach.availability) {
      try {
        const availability = isArray(coach.availability)
          ? coach.availability
          : [];
        // Initialize with all days having empty timeslots
        const initialAvailability = days.map((day) => ({
          day: day.toLowerCase(),
          timeslots: [],
        }));

        // If there's existing availability, update the corresponding days
        if (
          availability &&
          Array.isArray(availability) &&
          availability.length > 0
        ) {
          availability.forEach((dayData) => {
            const index = initialAvailability.findIndex(
              (day) => day.day === dayData.day.toLowerCase()
            );
            if (index !== -1) {
              initialAvailability[index].timeslots = dayData.timeslots;
            }
          });
        }
        setSelectedTimes(initialAvailability);
      } catch (error) {
        console.error("Error parsing availability:", error);
        setSelectedTimes([]);
      }
    } else {
      setSelectedTimes([]);
    }
  };

  React.useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        showContactInfo &&
        !event.target.closest("#contact-info-button") &&
        !event.target.closest(".contact-info-popover")
      ) {
        setShowContactInfo(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [showContactInfo]);

  const handleSearch = (e) => {
    const query = e.target.value;
    setSearchQuery(query);
    setFilterValues({
      ...filterValues,
      name: query,
    });

    // Clear existing timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // Set new timeout for debounced search
    const newTimeout = setTimeout(() => {
      if (query.trim()) {
        getData(currentPage, pageSize, {}, { first_name: query });
      } else {
        getData(currentPage, pageSize);
      }
    }, 500); // 500ms delay

    setSearchTimeout(newTimeout);
  };

  const renderCustomCell = {
    name: (row) => (
      <div className="flex items-center gap-3">
        <img
          src={row.photo || "/default-avatar.png"}
          alt={`${row.first_name} ${row.last_name}`}
          className="!h-10 !w-10 rounded-full object-cover"
          loading="lazy"
        />
        <span className="font-medium capitalize text-gray-900">
          {!row?.first_name || !row?.last_name
            ? "--"
            : `${row?.first_name} ${row?.last_name}`}
        </span>
      </div>
    ),
    email: (row) => <span className="text-gray-600">{row?.email || "--"}</span>,
    phone: (row) => (
      <span className="text-gray-600">
        {row?.phone ? (
          <FormattedPhoneNumber phoneNumber={row.phone} format="US" />
        ) : (
          "--"
        )}
      </span>
    ),
    status: (row) => (
      <span className="text-gray-600">
        {row?.status === 1 ? "Active" : "Inactive"}
      </span>
    ),
    sport_id: (row) => (
      <span className="text-gray-600">
        {row?.sports && row.sports.length > 0
          ? row.sports.map((sport) => sport.name).join(", ")
          : "--"}
      </span>
    ),
    "": (row) => (
      <div className="flex items-center justify-end gap-3">
        <button
          onClick={(e) => {
            e.stopPropagation();
            handleAvailabilityClick(row);
          }}
          className="rounded-full p-2 hover:bg-gray-100"
        >
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M2.46209 9.95722L3.76445 17.3433C3.86035 17.8872 4.37901 18.2503 4.9229 18.1544L10.1162 17.2387M2.46209 9.95722L1.94114 7.0028C1.84524 6.45891 2.20841 5.94025 2.7523 5.84434L14.0776 3.84739C14.6215 3.75149 15.1401 4.11466 15.236 4.65855L15.757 7.61297L2.46209 9.95722ZM16.0002 11.7509V14.0009L18.0002 16.0009M22.2502 14.0009C22.2502 17.4527 19.452 20.2509 16.0002 20.2509C12.5485 20.2509 9.75025 17.4527 9.75025 14.0009C9.75025 10.5491 12.5485 7.75092 16.0002 7.75092C19.452 7.75092 22.2502 10.5491 22.2502 14.0009Z"
              stroke="#868C98"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>
        <div className="relative">
          <button
            id="contact-info-button"
            onClick={(e) => {
              e.stopPropagation();
              setShowContactInfo(!showContactInfo);
              setActiveContactId(row.id);
            }}
            className="rounded-full p-2 hover:bg-gray-100"
          >
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M9.87776 11.0572L19.5007 5.70443M9.47619 11.0523L5.20388 6.53065C4.70178 5.99926 5.07851 5.125 5.8096 5.125H19.0881C19.732 5.125 20.1326 5.82416 19.807 6.3797L12.9883 18.0142C12.6164 18.6488 11.6648 18.5255 11.4668 17.8172L9.67304 11.4003C9.63664 11.27 9.56907 11.1506 9.47619 11.0523Z"
                stroke="#868C98"
                strokeWidth="1.5"
                strokeLinecap="square"
                strokeLinejoin="round"
              />
            </svg>
          </button>

          {showContactInfo && activeContactId === row.id && (
            <div
              className="contact-info-popover absolute right-0 z-50 mt-2 w-[400px] rounded-2xl bg-white shadow-lg"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
              }}
            >
              <div className="p-6">
                <h3 className="mb-6 text-xl font-medium text-gray-900">
                  Contact information
                </h3>

                <div className="space-y-4">
                  <div className="flex items-center justify-between rounded-xl bg-[#F6F8FA] p-4">
                    <div>
                      <p className="mb-2 text-sm font-medium uppercase text-gray-500">
                        EMAIL
                      </p>
                      <div className="flex items-center justify-between gap-4">
                        <p className="text-gray-900 underline">
                          {row?.email || "--"}
                        </p>
                      </div>
                    </div>
                    <button
                      onClick={async (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        await navigator.clipboard.writeText(row?.email || "--");
                        showToast(
                          globalDispatch,
                          "Email copied to clipboard",
                          3000,
                          "success"
                        );
                      }}
                      className="h-10 w-10 rounded-lg bg-white p-2 hover:bg-gray-100"
                    >
                      <svg
                        width="20"
                        height="20"
                        viewBox="0 0 20 20"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M13.75 14.5V16.75C13.75 16.9489 13.671 17.1397 13.5303 17.2803C13.3897 17.421 13.1989 17.5 13 17.5H4C3.80109 17.5 3.61032 17.421 3.46967 17.2803C3.32902 17.1397 3.25 16.9489 3.25 16.75L3.25 6.25C3.25 6.05109 3.32902 5.86032 3.46967 5.71967C3.61032 5.57902 3.80109 5.5 4 5.5H6.25V3.25C6.25 2.836 6.5875 2.5 7.00525 2.5L15.9948 2.5C16.0937 2.49941 16.1917 2.51836 16.2833 2.55578C16.3748 2.5932 16.4581 2.64833 16.5283 2.71803C16.5984 2.78772 16.6542 2.87059 16.6922 2.96189C16.7303 3.05318 16.7499 3.15109 16.75 3.25L16.7477 13.75C16.7477 14.164 16.4102 14.5 15.9925 14.5H13.75ZM15.2478 13L15.25 4L7.75 4L7.75 13H15.2478ZM12.25 14.5L6.25 14.5L6.25 7H4.75L4.75 16L12.25 16V14.5Z"
                          fill="#525866"
                        />
                      </svg>
                    </button>
                  </div>

                  <div className="flex items-center justify-between rounded-xl bg-[#F6F8FA] p-4">
                    <div>
                      <p className="mb-2 text-sm font-medium uppercase text-gray-500">
                        PHONE
                      </p>
                      <div className="flex items-center justify-between gap-4">
                        <p className="text-gray-900 underline">
                          {row?.phone ? (
                            <FormattedPhoneNumber
                              phoneNumber={row.phone}
                              format="US"
                            />
                          ) : (
                            "--"
                          )}
                        </p>
                      </div>
                    </div>
                    <button
                      onClick={async () => {
                        const formattedNumber = row?.phone
                          ? formatPhoneNumber(row.phone, "US")
                          : "--";
                        await navigator.clipboard.writeText(formattedNumber);
                        showToast(
                          globalDispatch,
                          "Phone copied to clipboard",
                          3000,
                          "success"
                        );
                      }}
                      className="h-10 w-10 rounded-lg bg-white p-2 hover:bg-gray-100"
                    >
                      <svg
                        width="20"
                        height="20"
                        viewBox="0 0 20 20"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M13.75 14.5V16.75C13.75 16.9489 13.671 17.1397 13.5303 17.2803C13.3897 17.421 13.1989 17.5 13 17.5H4C3.80109 17.5 3.61032 17.421 3.46967 17.2803C3.32902 17.1397 3.25 16.9489 3.25 16.75L3.25 6.25C3.25 6.05109 3.32902 5.86032 3.46967 5.71967C3.61032 5.57902 3.80109 5.5 4 5.5H6.25V3.25C6.25 2.836 6.5875 2.5 7.00525 2.5L15.9948 2.5C16.0937 2.49941 16.1917 2.51836 16.2833 2.55578C16.3748 2.5932 16.4581 2.64833 16.5283 2.71803C16.5984 2.78772 16.6542 2.87059 16.6922 2.96189C16.7303 3.05318 16.7499 3.15109 16.75 3.25L16.7477 13.75C16.7477 14.164 16.4102 14.5 15.9925 14.5H13.75ZM15.2478 13L15.25 4L7.75 4L7.75 13H15.2478ZM12.25 14.5L6.25 14.5L6.25 7H4.75L4.75 16L12.25 16V14.5Z"
                          fill="#525866"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
              <div className="flex justify-end border-t border-gray-200 p-5">
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    setShowContactInfo(false);
                  }}
                  className="rounded-xl bg-[#176448] px-5 py-3 text-white hover:bg-[#176448]"
                >
                  Close
                </button>
              </div>
            </div>
          )}
        </div>
        <button
          onClick={(e) => {
            e.stopPropagation();
            setShowDeleteModal(true);
            setActiveDeleteId({
              coach_id: row.id,
              coach_user_id: row.user_id,
            });
          }}
          className="rounded-full p-2 hover:bg-gray-100"
        >
          <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M4.74071 16.928L3.99227 16.9763L4.74071 16.928ZM15.258 16.928L16.0064 16.9763L16.0064 16.9763L15.258 16.928ZM2.29102 4.04102C1.8768 4.04102 1.54102 4.3768 1.54102 4.79102C1.54102 5.20523 1.8768 5.54102 2.29102 5.54102V4.04102ZM17.7077 5.54102C18.1219 5.54102 18.4577 5.20523 18.4577 4.79102C18.4577 4.3768 18.1219 4.04102 17.7077 4.04102V5.54102ZM8.87435 8.95768C8.87435 8.54347 8.53856 8.20768 8.12435 8.20768C7.71014 8.20768 7.37435 8.54347 7.37435 8.95768H8.87435ZM7.37435 13.541C7.37435 13.9552 7.71014 14.291 8.12435 14.291C8.53856 14.291 8.87435 13.9552 8.87435 13.541H7.37435ZM12.6243 8.95768C12.6243 8.54347 12.2886 8.20768 11.8743 8.20768C11.4601 8.20768 11.1243 8.54347 11.1243 8.95768H12.6243ZM11.1243 13.541C11.1243 13.9552 11.4601 14.291 11.8743 14.291C12.2886 14.291 12.6243 13.9552 12.6243 13.541H11.1243ZM12.5014 4.97796C12.6046 5.3791 13.0135 5.62059 13.4146 5.51734C13.8158 5.4141 14.0573 5.00521 13.954 4.60407L12.5014 4.97796ZM3.20924 4.8393L3.99227 16.9763L5.48916 16.8797L4.70613 4.74273L3.20924 4.8393ZM5.57232 18.4577H14.4264V16.9577H5.57232V18.4577ZM16.0064 16.9763L16.7895 4.8393L15.2926 4.74273L14.5095 16.8797L16.0064 16.9763ZM16.041 4.04102H3.95768V5.54102H16.041V4.04102ZM2.29102 5.54102H3.95768V4.04102H2.29102V5.54102ZM16.041 5.54102H17.7077V4.04102H16.041V5.54102ZM14.4264 18.4577C15.2613 18.4577 15.9527 17.8094 16.0064 16.9763L14.5095 16.8797C14.5067 16.9236 14.4703 16.9577 14.4264 16.9577V18.4577ZM3.99227 16.9763C4.04602 17.8094 4.73744 18.4577 5.57232 18.4577V16.9577C5.52838 16.9577 5.49199 16.9236 5.48916 16.8797L3.99227 16.9763ZM7.37435 8.95768V13.541H8.87435V8.95768H7.37435ZM11.1243 8.95768V13.541H12.6243V8.95768H11.1243ZM9.99937 3.04102C11.2021 3.04102 12.2145 3.86359 12.5014 4.97796L13.954 4.60407C13.5008 2.84306 11.9031 1.54102 9.99937 1.54102V3.04102ZM7.49738 4.97796C7.78419 3.86359 8.79666 3.04102 9.99937 3.04102V1.54102C8.09567 1.54102 6.49797 2.84306 6.04472 4.60407L7.49738 4.97796Z"
              fill="#868C98"
            />
          </svg>
        </button>
      </div>
    ),
  };

  const role = localStorage.getItem("role");

  return (
    <div className="h-screen px-8">
      <div className="flex flex-col flex-wrap justify-between gap-4 py-3 md:flex-row md:items-center">
        <div className="w-full">
          <div className="mb-4 flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
            <h3 className="text-lg font-medium">Coach Filters</h3>
            <div className="flex gap-2">
              <button
                onClick={() => setShowInvitationCard(true)}
                className="inline-flex items-center gap-2 rounded-lg bg-[#1D275F] px-4 py-2 text-sm font-semibold text-white hover:bg-blue-700"
              >
                <span>+</span>
                Add new
              </button>
              <HistoryComponent
                title="Coach History"
                emptyMessage="No coach history found"
                activityType={activityLogTypes.coach_management}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {/* Name Search */}
            <div>
              <label className="mb-1 block text-xs font-medium text-gray-700">
                Name
              </label>
              <div className="relative flex items-center">
                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                  <BiSearch className="text-gray-500" />
                </div>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={handleSearch}
                  className="w-full rounded-md border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  placeholder="Search coaches by name"
                />
                {searchQuery && (
                  <button
                    onClick={() => {
                      setSearchQuery("");
                      setFilterValues({
                        ...filterValues,
                        name: "",
                      });
                      getData(currentPage, pageSize);
                    }}
                    className="absolute right-2 rounded-full p-1 hover:bg-gray-100"
                  >
                    <AiOutlineClose className="text-gray-500" />
                  </button>
                )}
              </div>
            </div>

            {/* Sport Filter */}
            <div>
              <label className="mb-1 block text-xs font-medium text-gray-700">
                Sport
              </label>
              <select
                className="w-full rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700"
                value={filterValues.sport}
                onChange={onSportSelect}
              >
                <option value="">All Sports</option>
                {sports?.map((sport) => (
                  <option key={sport.id} value={sport.id}>
                    {sport.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Status Filter */}
            <div>
              <label className="mb-1 block text-xs font-medium text-gray-700">
                Status
              </label>
              <select
                className="w-full rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700"
                value={filterValues.status}
                onChange={onStatusSelect}
              >
                <option value="">All Statuses</option>
                <option value="0">Inactive</option>
                <option value="1">Active</option>
              </select>
            </div>

            {/* Filter Buttons - Moved below the filter fields */}
            <div className="col-span-full mt-4 flex justify-end gap-3">
              <button
                onClick={() => {
                  setFilterValues({
                    name: "",
                    sport: "",
                    status: "",
                  });
                  setSearchQuery("");
                  getData(1, pageSize);
                }}
                className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Clear Filters
              </button>
              <button
                onClick={() => applyFilters()}
                className="rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700"
              >
                Apply Filters
              </button>
            </div>
          </div>
        </div>
      </div>

      {loading ? (
        <SkeletonLoader />
      ) : (
        <DataTable
          columns={columns}
          data={data}
          loading={loading}
          renderCustomCell={renderCustomCell}
          rowClassName="hover:bg-gray-50 bg-gray-100 px-4 py-3 cursor-pointer"
          emptyMessage="No coaches available"
          loadingMessage="Loading coaches..."
          onClick={(row) => navigate(`/${role}/view-coach/${row.id}`)}
        />
      )}
      <PaginationBar
        currentPage={currentPage}
        pageCount={pageCount}
        pageSize={pageSize}
        canPreviousPage={canPreviousPage}
        canNextPage={canNextPage}
        updatePageSize={(newPageSize) => {
          setPageSize(newPageSize);
          getData(currentPage, newPageSize);
        }}
        previousPage={previousPage}
        nextPage={nextPage}
        gotoPage={(pageNum) => getData(pageNum, pageSize)}
      />
      {/* <ModalSidebar
        isModalActive={showAddSidebar}
        closeModalFn={() => setShowAddSidebar(false)}
      >
        <AddAdminEmailPage setSidebar={setShowAddSidebar} getData={getData} />
      </ModalSidebar> */}
      {/* {showEditSidebar && (
        <ModalSidebar
          isModalActive={showEditSidebar}
          closeModalFn={() => setShowEditSidebar(false)}
        >
          <EditAdminEmailPage
            activeId={activeEditId}
            setSidebar={setShowEditSidebar}
          />
        </ModalSidebar>
      )} */}

      {showTimesAvailableModal && selectedCoach && (
        <TimeSlotPicker
          showTimesAvailableModal={showTimesAvailableModal}
          setShowTimesAvailableModal={setShowTimesAvailableModal}
          selectedTimes={selectedTimes}
          setSelectedTimes={setSelectedTimes}
          title={`${selectedCoach?.first_name} ${selectedCoach?.last_name}'s availability`}
          isSubmitting={submittingAvailability}
          onSave={async () => {
            try {
              setSubmittingAvailability(true);
              const formatSelectedTimes = () => {
                if (!selectedTimes) return [];
                return selectedTimes.filter(
                  (daySlot) => daySlot.timeslots.length > 0
                );
              };
              const formattedData = formatSelectedTimes();
              sdk.setTable("coach");
              await sdk.callRestAPI(
                {
                  id: selectedCoach.id,
                  availability: JSON.stringify(formattedData),
                },
                "PUT"
              );

              await logActivity(sdk, {
                user_id,
                activity_type: activityLogTypes.coach_management,
                action_type: actionLogTypes.UPDATE,
                data: {
                  coach_id: selectedCoach.id,
                  availability: formattedData,
                },
                club_id: club?.id,
                description: `Updated coach availability for ${selectedCoach?.first_name} ${selectedCoach?.last_name}`,
              });

              showToast(
                globalDispatch,
                "Availability updated successfully",
                3000,
                "success"
              );
              getData(currentPage, pageSize);
            } catch (error) {
              console.error("Error saving availability:", error);
              showToast(dispatch, error.message, 3000, "error");
            } finally {
              setSubmittingAvailability(false);
              setShowTimesAvailableModal(false);
              setSelectedCoach(null);
            }
          }}
        />
      )}

      <DeleteModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onDelete={() => handleDelete(activeDeleteId)}
        message="Are you sure you want to delete this coach?"
        loading={deletingCoach}
        title="Delete Coach"
      />

      {showInvitationCard && (
        <InvitationCard
          title="Invite Coach"
          onClose={() => setShowInvitationCard(false)}
          link={`${window.location.origin}/coach/signup?club_id=${club?.id}`}
          message="Please use this following link to invite a Coach to join our website. Once the Coach has signed up,their details can be fetched and managed in our system."
        />
      )}
    </div>
  );
};

export default ListCoaches;

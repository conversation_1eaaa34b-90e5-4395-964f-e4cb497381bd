import React from "react";
import { AuthContext, tokenExpireError } from "Context/Auth";
import MkdSDK from "Utils/MkdSDK";
import { GlobalContext, showToast } from "Context/Global";
import { PaginationBar } from "Components/PaginationBar";
import _ from "lodash";
import SkeletonLoader from "Components/Skeleton/Skeleton";
import { Link } from "react-router-dom";
import { InteractiveButton } from "Components/InteractiveButton";
import HistoryComponent from "Components/HistoryComponent";
import { actionLogTypes, activityLogTypes, logActivity } from "Utils/utils";
import DataTable from "Components/Shared/DataTable";
import FormattedPhoneNumber from "Components/Shared/FormattedPhoneNumber";
import { useNavigate } from "react-router-dom";
let sdk = new MkdSDK();

// Utility function to calculate age group based on date of birth
// Used in the filter UI

const columns = [
  {
    header: "Name",
    accessor: "name",
  },
  {
    header: "Email",
    accessor: "email",
  },
  {
    header: "Phone number",
    accessor: "phone_number",
  },
  {
    header: "Membership status",
    accessor: "slug",
  },
  {
    header: "NTRP",
    accessor: "ntrp",
  },
  {
    header: "",
    accessor: "actions",
  },
];

const ListUsers = ({ club }) => {
  const navigate = useNavigate();
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { dispatch } = React.useContext(AuthContext);
  const [data, setCurrentTableData] = React.useState([]);
  const [pageSize, setPageSize] = React.useState(10);
  const [pageCount, setPageCount] = React.useState(0);
  // Total count of data items
  const [currentPage, setPage] = React.useState(0);
  const [canPreviousPage, setCanPreviousPage] = React.useState(false);
  const [canNextPage, setCanNextPage] = React.useState(false);
  // Filter state for search inputs
  const [filterValues, setFilterValues] = React.useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    ageGroup: "",
    status: "",
    ntrp: "",
    membership: "",
  });
  const [loading, setLoading] = React.useState(true);
  const [showAddSidebar, setShowAddSidebar] = React.useState(false);
  const dropdownFilterRef = React.useRef(null);
  const [showDeleteModal, setShowDeleteModal] = React.useState(false);
  const [selectedUser, setSelectedUser] = React.useState(null);
  // User profiles state is managed by the getProfiles function
  const user_id = localStorage.getItem("user");
  const userRole = localStorage.getItem("role");

  function previousPage() {
    getData(currentPage - 1, pageSize);
  }

  function nextPage() {
    getData(currentPage + 1, pageSize);
  }

  // console.log("club_id", globalState.clubProfile.club?.id);
  async function getData(pageNum, limitNum, data = {}, filters = []) {
    setLoading(showAddSidebar ? false : true);
    console.log("filters", filters);
    try {
      sdk.setTable("profile");

      const result = await sdk.callRestAPI(
        {
          payload: {
            ...data,
          },
          page: pageNum,
          limit: limitNum,
          filter: [...filters, `role,cs,user`, `club_id,eq,${club?.id}`],
          join: ["user|user_id"],
        },
        "PAGINATE"
      );

      if (result) {
        setLoading(false);
      }
      const { list, limit, num_pages, page } = result;

      setCurrentTableData(list);
      setPageSize(limit);
      setPageCount(num_pages);
      setPage(page);
      setCanPreviousPage(page > 1);
      setCanNextPage(page + 1 <= num_pages);
    } catch (error) {
      setLoading(false);
      console.log("ERROR", error);
      tokenExpireError(dispatch, error.message);
    }
  }

  // Apply filters from all search fields
  const applyFilters = () => {
    const activeFilters = [];

    // Add first name filter if it exists
    if (filterValues.firstName) {
      activeFilters.push(
        `${sdk._project_id}_user.first_name,cs,${filterValues.firstName}`
      );
    }

    // Add last name filter if it exists
    if (filterValues.lastName) {
      activeFilters.push(
        `${sdk._project_id}_user.last_name,cs,${filterValues.lastName}`
      );
    }

    // Add email filter if it exists
    if (filterValues.email) {
      activeFilters.push(
        `${sdk._project_id}_user.email,cs,${filterValues.email}`
      );
    }

    // Add phone filter if it exists
    if (filterValues.phone) {
      activeFilters.push(
        `${sdk._project_id}_user.phone,cs,${filterValues.phone}`
      );
    }

    // Add age group filter if it exists
    if (filterValues.ageGroup && filterValues.ageGroup !== "") {
      activeFilters.push(
        `${sdk._project_id}_age_group,cs,${filterValues.ageGroup}`
      );
    }

    // Add status filter if it exists
    if (filterValues.status !== "") {
      activeFilters.push(`${sdk._project_id}_status,eq,${filterValues.status}`);
    }

    // Add NTRP filter if it exists
    if (filterValues.ntrp) {
      activeFilters.push(`ntrp,cs,${filterValues.ntrp}`);
    }

    // Add membership filter if it exists
    if (filterValues.membership) {
      activeFilters.push(`slug,cs,${filterValues.membership}`);
    }

    // Apply all filters
    getData(1, pageSize, {}, activeFilters);
  };

  // We're using the filter system for searching users instead of individual search functions

  React.useEffect(() => {
    if (club?.id) {
      getData(1, pageSize);
    }
  }, [club?.id]);

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "users",
      },
    });
  }, []);
  const handleClickOutside = (event) => {
    if (
      dropdownFilterRef.current &&
      !dropdownFilterRef.current.contains(event.target)
    ) {
      setOpenFilter(false);
    }
  };

  React.useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // No longer needed as we have a clear filters button in the UI

  const renderCustomCell = {
    name: (row) => (
      <div className="flex items-center gap-3">
        <img
          src={row.user?.photo || "/default-avatar.png"}
          alt={`${row.user?.first_name} ${row.user?.last_name}`}
          className="h-10 w-10 rounded-full object-cover"
        />
        <span className="font-medium text-gray-900">
          {_.truncate(row.user?.first_name, { length: 15 })}{" "}
          {_.truncate(row.user?.last_name, { length: 15 })}
        </span>
      </div>
    ),
    slug: (row) => (
      <span className="text-gray-600">{row.slug || "Member"}</span>
    ),
    ntrp: (row) => <span className="text-gray-600">{row.ntrp}</span>,
    phone_number: (row) => (
      <FormattedPhoneNumber
        phoneNumber={row.user?.phone}
        className="text-gray-600"
      />
    ),
    email: (row) => <span className="text-gray-600">{row.user?.email}</span>,
    actions: (row) => (
      <div className="flex items-center justify-end gap-3">
        <button
          onClick={(e) => {
            e.stopPropagation();
            setSelectedUser(row);
            setShowDeleteModal(true);
          }}
          className="rounded-full p-2 hover:bg-gray-100"
        >
          <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M4.74071 16.928L3.99227 16.9763L4.74071 16.928ZM15.258 16.928L16.0064 16.9763L16.0064 16.9763L15.258 16.928ZM2.29102 4.04102C1.8768 4.04102 1.54102 4.3768 1.54102 4.79102C1.54102 5.20523 1.8768 5.54102 2.29102 5.54102V4.04102ZM17.7077 5.54102C18.1219 5.54102 18.4577 5.20523 18.4577 4.79102C18.4577 4.3768 18.1219 4.04102 17.7077 4.04102V5.54102ZM8.87435 8.95768C8.87435 8.54347 8.53856 8.20768 8.12435 8.20768C7.71014 8.20768 7.37435 8.54347 7.37435 8.95768H8.87435ZM7.37435 13.541C7.37435 13.9552 7.71014 14.291 8.12435 14.291C8.53856 14.291 8.87435 13.9552 8.87435 13.541H7.37435ZM12.6243 8.95768C12.6243 8.54347 12.2886 8.20768 11.8743 8.20768C11.4601 8.20768 11.1243 8.54347 11.1243 8.95768H12.6243ZM11.1243 13.541C11.1243 13.9552 11.4601 14.291 11.8743 14.291C12.2886 14.291 12.6243 13.9552 12.6243 13.541H11.1243ZM12.5014 4.97796C12.6046 5.3791 13.0135 5.62059 13.4146 5.51734C13.8158 5.4141 14.0573 5.00521 13.954 4.60407L12.5014 4.97796ZM3.20924 4.8393L3.99227 16.9763L5.48916 16.8797L4.70613 4.74273L3.20924 4.8393ZM5.57232 18.4577H14.4264V16.9577H5.57232V18.4577ZM16.0064 16.9763L16.7895 4.8393L15.2926 4.74273L14.5095 16.8797L16.0064 16.9763ZM16.041 4.04102H3.95768V5.54102H16.041V4.04102ZM2.29102 5.54102H3.95768V4.04102H2.29102V5.54102ZM16.041 5.54102H17.7077V4.04102H16.041V5.54102ZM14.4264 18.4577C15.2613 18.4577 15.9527 17.8094 16.0064 16.9763L14.5095 16.8797C14.5067 16.9236 14.4703 16.9577 14.4264 16.9577V18.4577ZM3.99227 16.9763C4.04602 17.8094 4.73744 18.4577 5.57232 18.4577V16.9577C5.52838 16.9577 5.49199 16.9236 5.48916 16.8797L3.99227 16.9763ZM7.37435 8.95768V13.541H8.87435V8.95768H7.37435ZM11.1243 8.95768V13.541H12.6243V8.95768H11.1243ZM9.99937 3.04102C11.2021 3.04102 12.2145 3.86359 12.5014 4.97796L13.954 4.60407C13.5008 2.84306 11.9031 1.54102 9.99937 1.54102V3.04102ZM7.49738 4.97796C7.78419 3.86359 8.79666 3.04102 9.99937 3.04102V1.54102C8.09567 1.54102 6.49797 2.84306 6.04472 4.60407L7.49738 4.97796Z"
              fill="#868C98"
            />
          </svg>
        </button>
      </div>
    ),
  };

  const DeleteProfileModal = ({ isOpen, onClose }) => {
    const [deleteLoading, setDeleteLoading] = React.useState(false);
    const onDelete = async () => {
      try {
        setDeleteLoading(true);
        sdk.setTable("profile");
        const response = await sdk.callRestAPI(
          { id: Number(selectedUser.id) },
          "DELETE"
        );
        // Also delete the associated user
        sdk.setTable("user");
        await sdk.callRestAPI({ id: Number(selectedUser.user_id) }, "DELETE");
        await logActivity(sdk, {
          user_id,
          activity_type: activityLogTypes.user_management,
          action_type: actionLogTypes.DELETE,
          data: selectedUser,
          club_id: club?.id,
          description: "Deleted user",
        });
        if (!response.error) {
          showToast(dispatch, "User deleted successfully", 3000, "success");
          onClose();
          setSelectedUser(null);
          getData(currentPage, pageSize);
        }
      } catch (error) {
        showToast(dispatch, error?.message, 3000, "error");
        console.log(error);
      } finally {
        setDeleteLoading(false);
      }
    };
    return (
      <div
        className={`fixed inset-0 z-50 flex items-center justify-center ${
          isOpen ? "" : "hidden"
        }`}
      >
        <div className="fixed inset-0 bg-black opacity-50"></div>
        <div className="relative z-50 w-full max-w-md rounded-3xl bg-white p-6">
          <h2 className="mb-4 text-xl font-medium">Delete user</h2>
          <p className="mb-6">Are you sure you want to delete this user?</p>
          <div className="flex justify-end gap-3 border-t pt-4">
            <button
              onClick={onClose}
              className="rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50"
            >
              Cancel
            </button>
            <InteractiveButton
              onClick={() => {
                onDelete();
              }}
              className="rounded-lg bg-red-600 px-4 py-2 text-white hover:bg-red-700"
              loading={deleteLoading}
            >
              Yes, delete
            </InteractiveButton>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="h-full px-8">
      <div className="flex flex-col flex-wrap justify-between gap-4 py-3 md:flex-row md:items-center">
        <div className="w-full">
          <div className="mb-4 flex items-center justify-between">
            <h3 className="text-lg font-medium">User Filters</h3>
            <div className="flex gap-2">
              <button
                onClick={() => setShowAddSidebar(true)}
                className="inline-flex items-center gap-2 rounded-lg bg-[#1D275F] px-4 py-2 text-sm font-semibold text-white hover:bg-blue-700"
              >
                <span>+</span>
                Add new
              </button>
              <HistoryComponent
                title="User History"
                emptyMessage="No user history found"
                activityType={activityLogTypes.user_management}
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-4">
            {/* First Name */}
            <div>
              <label className="mb-1 block text-xs font-medium text-gray-700">
                First Name
              </label>
              <input
                type="text"
                placeholder="Search by first name"
                className="w-full rounded-md border border-gray-200 px-3 py-2 text-sm text-gray-700 placeholder:text-gray-500"
                value={filterValues.firstName}
                onChange={(e) => {
                  setFilterValues({
                    ...filterValues,
                    firstName: e.target.value.trim(),
                  });
                }}
              />
            </div>

            {/* Last Name */}
            <div>
              <label className="mb-1 block text-xs font-medium text-gray-700">
                Last Name
              </label>
              <input
                type="text"
                placeholder="Search by last name"
                className="w-full rounded-md border border-gray-200 px-3 py-2 text-sm text-gray-700 placeholder:text-gray-500"
                value={filterValues.lastName}
                onChange={(e) => {
                  setFilterValues({
                    ...filterValues,
                    lastName: e.target.value.trim(),
                  });
                }}
              />
            </div>

            {/* Email */}
            <div>
              <label className="mb-1 block text-xs font-medium text-gray-700">
                Email
              </label>
              <input
                type="text"
                placeholder="Search by email"
                className="w-full rounded-md border border-gray-200 px-3 py-2 text-sm text-gray-700 placeholder:text-gray-500"
                value={filterValues.email}
                onChange={(e) => {
                  setFilterValues({
                    ...filterValues,
                    email: e.target.value.trim(),
                  });
                }}
              />
            </div>

            {/* Phone */}
            <div>
              <label className="mb-1 block text-xs font-medium text-gray-700">
                Phone
              </label>
              <input
                type="text"
                placeholder="Search by phone"
                className="w-full rounded-md border border-gray-200 px-3 py-2 text-sm text-gray-700 placeholder:text-gray-500"
                value={filterValues.phone}
                onChange={(e) => {
                  setFilterValues({
                    ...filterValues,
                    phone: e.target.value.trim(),
                  });
                }}
              />
            </div>

            {/* Status */}
            <div>
              <label className="mb-1 block text-xs font-medium text-gray-700">
                Status
              </label>
              <select
                className="w-full rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700"
                value={filterValues.status}
                onChange={(e) => {
                  setFilterValues({
                    ...filterValues,
                    status: e.target.value,
                  });
                }}
              >
                <option value="">All Statuses</option>
                <option value="0">Inactive</option>
                <option value="1">Active</option>
              </select>
            </div>

            {/* NTRP */}
            <div>
              <label className="mb-1 block text-xs font-medium text-gray-700">
                NTRP
              </label>
              <input
                type="text"
                placeholder="Search by NTRP"
                className="w-full rounded-md border border-gray-200 px-3 py-2 text-sm text-gray-700 placeholder:text-gray-500"
                value={filterValues.ntrp}
                onChange={(e) => {
                  setFilterValues({
                    ...filterValues,
                    ntrp: e.target.value.trim(),
                  });
                }}
              />
            </div>

            {/* Filter Buttons - Moved below the filter fields */}
            <div className="col-span-full mt-6 flex justify-end gap-3">
              <button
                onClick={() => {
                  setFilterValues({
                    firstName: "",
                    lastName: "",
                    email: "",
                    phone: "",
                    ageGroup: "",
                    status: "",
                    ntrp: "",
                    membership: "",
                  });
                  getData(1, pageSize);
                }}
                className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Clear Filters
              </button>
              <button
                onClick={() => applyFilters()}
                className="rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700"
              >
                Apply Filters
              </button>
            </div>
          </div>
        </div>
      </div>
      {loading ? (
        <SkeletonLoader />
      ) : (
        <>
          <DataTable
            columns={columns}
            data={data}
            loading={loading}
            renderCustomCell={renderCustomCell}
            rowClassName="hover:bg-gray-50 bg-gray-100 px-4 py-3 cursor-pointer"
            emptyMessage="No users available"
            loadingMessage="Loading users..."
            onClick={(row) => navigate(`/${userRole}/view-user/${row.id}`)}
          />
        </>
      )}
      {pageCount > 0 && (
        <PaginationBar
          currentPage={currentPage}
          pageCount={pageCount}
          pageSize={pageSize}
          canPreviousPage={canPreviousPage}
          canNextPage={canNextPage}
          updatePageSize={(newPageSize) => {
            setPageSize(newPageSize);
            getData(1, newPageSize);
          }}
          previousPage={previousPage}
          nextPage={nextPage}
          gotoPage={(pageNum) => getData(pageNum, pageSize)}
        />
      )}
      <DeleteProfileModal
        isOpen={showDeleteModal}
        onClose={() => {
          setShowDeleteModal(false);
          setSelectedUser(null);
        }}
        selectedUser={selectedUser}
      />
    </div>
  );
};

export default ListUsers;

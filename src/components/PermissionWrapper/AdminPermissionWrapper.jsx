import React from "react";
import { useAdminPermissions } from "Hooks/useAdminPermissions";
import AccessRestricted from "Components/Shared/AccessRestricted";
import LoadingSpinner from "Components/LoadingSpinner";

/**
 * Higher-order component that wraps admin pages with permission checking
 * @param {React.Component} WrappedComponent - The component to wrap
 * @param {string} requiredPermission - The permission module required to access this page
 * @param {string} restrictionMessage - Custom message to show when access is restricted
 */
const withAdminPermission = (
  WrappedComponent,
  requiredPermission,
  restrictionMessage
) => {
  return function AdminPermissionWrapper(props) {
    const { permissions, loading, hasPermission } = useAdminPermissions();
    const userRole = localStorage.getItem("role");

    // Show loading while fetching permissions
    if (loading) {
      return <LoadingSpinner />;
    }

    // Admin users always have access (they can toggle permissions)
    if (userRole === "admin") {
      return <WrappedComponent {...props} />;
    }

    // Check if the required permission is enabled
    if (!hasPermission(requiredPermission)) {
      const defaultMessage = `You don't have permission to access ${requiredPermission.replace(
        /_/g,
        " "
      )} module`;
      return (
        <AccessRestricted message={restrictionMessage || defaultMessage} />
      );
    }

    // Permission granted, render the component
    return <WrappedComponent {...props} />;
  };
};

/**
 * Component wrapper for admin staff specific pages
 * This checks the "staff" permission which controls admin staff management
 */
export const AdminStaffPermissionWrapper = ({
  children,
  restrictionMessage,
}) => {
  const { loading, hasAdminStaffAccess } = useAdminPermissions();
  const userRole = localStorage.getItem("role");

  if (loading) {
    return <LoadingSpinner />;
  }

  // Admin users always have access
  if (userRole === "admin") {
    return children;
  }

  // Check admin staff permission
  if (!hasAdminStaffAccess()) {
    const defaultMessage =
      "You don't have permission to access admin staff management";
    return <AccessRestricted message={restrictionMessage || defaultMessage} />;
  }

  return children;
};

export default withAdminPermission;

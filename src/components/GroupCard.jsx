import { useState, useRef, useEffect } from "react";
import {
  FiUsers,
  FiMoreHorizontal,
  FiUserPlus,
  FiEdit2,
  FiTrash2,
  FiUser,
  FiUserMinus,
  FiChevronRight,
  FiUsers as FiUsersIcon,
  <PERSON>Send,
  FiHeart,
} from "react-icons/fi";

const GroupCard = ({
  group,
  currentUserId,
  onAddMember,
  onEditName,
  onDeleteGroup,
  onViewProfile,
  onRemoveMember,
  onAddExistingUser,
  onAddFamilyMember,
  onInviteToCourtmatch,
  sentInvites = [],
}) => {
  // Filter pending invites for this group
  const pendingInvitesForGroup = sentInvites.filter(
    (invite) =>
      invite.status === "pending" && invite.group_id === group.group_id
  );
  const [showGroupMenu, setShowGroupMenu] = useState(false);
  const [showMemberMenu, setShowMemberMenu] = useState(null);
  const [showAddMemberMenu, setShowAddMemberMenu] = useState(false);
  const [menuPositions, setMenuPositions] = useState({});

  // Reference to the scrollable container
  const scrollContainerRef = useRef(null);

  const groupMenuRef = useRef(null);
  const memberMenuRef = useRef(null);
  const addMemberMenuRef = useRef(null);
  const user_id = localStorage.getItem("user");

  // Check if current user is the group owner
  const isGroupOwner =
    group.group_owner_id &&
    parseInt(group.group_owner_id) === parseInt(currentUserId || user_id);

  useEffect(() => {
    const handleClickOutside = (event) => {
      // Close group menu if clicking outside
      if (
        groupMenuRef.current &&
        !groupMenuRef.current.contains(event.target)
      ) {
        setShowGroupMenu(false);
        setShowAddMemberMenu(false);
      }

      // Close member menu if clicking outside
      if (
        memberMenuRef.current &&
        !memberMenuRef.current.contains(event.target)
      ) {
        setShowMemberMenu(null);
      }

      // Close add member menu if clicking outside
      if (
        addMemberMenuRef.current &&
        !addMemberMenuRef.current.contains(event.target)
      ) {
        setShowAddMemberMenu(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const filteredMembers = group.members.filter((member) => {
    // Filter out other users' family members
    // If member has a guardian and that guardian is not the current user, exclude them
    if (member.guardian && member.guardian != user_id) {
      return false;
    }
    return true;
  });
  return (
    <div className="h-fit max-h-fit rounded-xl border border-gray-100 bg-white p-6 shadow-sm transition-shadow hover:shadow-md">
      <div className="mb-4 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-indigo-600">
            <FiUsers className="h-5 w-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {group.group_name}
            </h3>
            <p className="text-sm text-gray-500">
              {filteredMembers.length} member
              {filteredMembers.length !== 1 ? "s" : ""}
            </p>
          </div>
        </div>
        {isGroupOwner && (
          <div className="relative flex items-center gap-2">
            <button
              onClick={() => setShowGroupMenu(!showGroupMenu)}
              className="rounded p-1 hover:bg-gray-100"
            >
              <svg
                width="25"
                height="24"
                viewBox="0 0 25 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12.3333 13C12.8856 13 13.3333 12.5523 13.3333 12C13.3333 11.4477 12.8856 11 12.3333 11C11.7811 11 11.3333 11.4477 11.3333 12C11.3333 12.5523 11.7811 13 12.3333 13Z"
                  fill="#868C98"
                />
                <path
                  d="M20.5833 13C21.1356 13 21.5833 12.5523 21.5833 12C21.5833 11.4477 21.1356 11 20.5833 11C20.0311 11 19.5833 11.4477 19.5833 12C19.5833 12.5523 20.0311 13 20.5833 13Z"
                  fill="#868C98"
                />
                <path
                  d="M4.08334 13C4.63563 13 5.08334 12.5523 5.08334 12C5.08334 11.4477 4.63563 11 4.08334 11C3.53106 11 3.08334 11.4477 3.08334 12C3.08334 12.5523 3.53106 13 4.08334 13Z"
                  fill="#868C98"
                />
                <path
                  d="M12.3333 13C12.8856 13 13.3333 12.5523 13.3333 12C13.3333 11.4477 12.8856 11 12.3333 11C11.7811 11 11.3333 11.4477 11.3333 12C11.3333 12.5523 11.7811 13 12.3333 13Z"
                  stroke="#868C98"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M20.5833 13C21.1356 13 21.5833 12.5523 21.5833 12C21.5833 11.4477 21.1356 11 20.5833 11C20.0311 11 19.5833 11.4477 19.5833 12C19.5833 12.5523 20.0311 13 20.5833 13Z"
                  stroke="#868C98"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M4.08334 13C4.63563 13 5.08334 12.5523 5.08334 12C5.08334 11.4477 4.63563 11 4.08334 11C3.53106 11 3.08334 11.4477 3.08334 12C3.08334 12.5523 3.53106 13 4.08334 13Z"
                  stroke="#868C98"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </button>
            {showGroupMenu && (
              <div
                ref={groupMenuRef}
                className="absolute right-0 z-10 mt-2 w-48 rounded-xl border-2 border-gray-200 bg-white"
              >
                <div className="py-1">
                  {isGroupOwner && (
                    <div className="relative">
                      <button
                        onClick={() => setShowAddMemberMenu(!showAddMemberMenu)}
                        className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <FiUserPlus className="mr-2" /> Add member
                        <button className="ml-auto flex items-center">
                          <FiChevronRight className="text-lg text-gray-500" />
                        </button>
                      </button>
                      {showAddMemberMenu && (
                        <div
                          ref={addMemberMenuRef}
                          className="absolute left-0 top-0 z-20 ml-2 w-52 translate-x-[-105%] rounded-xl border-2 border-gray-200 bg-white sm:left-full sm:translate-x-0"
                        >
                          <div className="py-1">
                            <button
                              onClick={() => {
                                onAddExistingUser(group.id);
                                setShowAddMemberMenu(false);
                                setShowGroupMenu(false);
                              }}
                              className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            >
                              <FiUsersIcon className="mr-2" /> Add existing user
                            </button>
                            <button
                              onClick={() => {
                                onAddFamilyMember(group.id);
                                setShowAddMemberMenu(false);
                                setShowGroupMenu(false);
                              }}
                              className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            >
                              <FiUsers className="mr-2" /> Add family member
                            </button>
                            <button
                              onClick={() => {
                                onInviteToCourtmatch(group.id);
                                setShowAddMemberMenu(false);
                                setShowGroupMenu(false);
                              }}
                              className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            >
                              <FiSend className="mr-2" /> Invite to CourtMatch
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                  {isGroupOwner && (
                    <button
                      onClick={() => {
                        onEditName(group.id);
                        setShowGroupMenu(false);
                      }}
                      className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <FiEdit2 className="mr-2" /> Edit name
                    </button>
                  )}
                  {isGroupOwner && (
                    <button
                      onClick={() => {
                        onDeleteGroup(group.id);
                        setShowGroupMenu(false);
                      }}
                      className="flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                    >
                      <FiTrash2 className="mr-2" /> Delete group
                    </button>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      <div
        ref={scrollContainerRef}
        className="max-h-[350px] space-y-3 overflow-y-auto rounded-xl border border-gray-200 bg-gray-50/50 p-4"
      >
        {/* Members Section */}
        {filteredMembers.map((member) => {
          // Check if member is a family member
          const isFamilyMember = member.guardian == user_id;

          return (
            <div
              key={member.id}
              className={`group flex items-center justify-between rounded-lg p-3 transition-colors hover:bg-white ${
                isFamilyMember
                  ? "border border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50"
                  : "border border-gray-100 bg-white"
              }`}
            >
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <div className="h-12 w-12 overflow-hidden rounded-full bg-gray-200 shadow-sm ring-2 ring-white">
                    {member.photo ? (
                      <img
                        src={member.photo}
                        alt={`${member.first_name} ${member.last_name}`}
                        className="h-full w-full object-cover"
                      />
                    ) : (
                      <div className="flex h-full w-full items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200">
                        <FiUser className="h-6 w-6 text-gray-400" />
                      </div>
                    )}
                  </div>
                  {/* Family member indicator */}
                  {isFamilyMember && (
                    <div className="absolute -bottom-1 -right-1 flex h-6 w-6 items-center justify-center rounded-full bg-blue-500 ring-2 ring-white">
                      <FiHeart className="h-3 w-3 text-white" />
                    </div>
                  )}
                </div>
                <div className="flex flex-col">
                  <div className="flex items-center gap-2">
                    <span className="font-semibold text-gray-900">
                      {member.first_name} {member.last_name}
                    </span>
                    {isFamilyMember && (
                      <span className="inline-flex items-center gap-1 rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-700">
                        <FiHeart className="h-3 w-3" />
                        Family
                      </span>
                    )}
                  </div>
                  {/* Show family role if available */}
                  {member.family_role && (
                    <span className="text-xs font-medium capitalize text-blue-600">
                      {member.family_role}
                    </span>
                  )}
                  {/* Show email for non-family members or if no family role */}
                  {(!isFamilyMember || !member.family_role) && member.email && (
                    <span className="text-xs text-gray-500">
                      {member.email}
                    </span>
                  )}
                </div>
              </div>
              <div className="relative">
                <button
                  onClick={(e) => {
                    // Toggle menu off if already open
                    if (showMemberMenu === member.id) {
                      setShowMemberMenu(null);
                      return;
                    }

                    // Get button position
                    const buttonRect = e.currentTarget.getBoundingClientRect();

                    // Get container dimensions
                    const containerRect =
                      scrollContainerRef.current.getBoundingClientRect();
                    const containerScrollTop =
                      scrollContainerRef.current.scrollTop;

                    // Calculate button position relative to the container
                    const buttonPositionInContainer =
                      buttonRect.top - containerRect.top + containerScrollTop;
                    const buttonBottomPositionInContainer =
                      buttonPositionInContainer + buttonRect.height;

                    // Calculate available space below the button within the container
                    const spaceBelow =
                      containerRect.height -
                      (buttonBottomPositionInContainer - containerScrollTop);
                    const menuHeight = 100; // Approximate height of the menu

                    // Determine if menu should appear above or below
                    const position = spaceBelow < menuHeight ? "top" : "bottom";

                    // Update menu position state
                    setMenuPositions((prev) => ({
                      ...prev,
                      [member.id]: position,
                    }));

                    // If menu will be below and there's not enough space, scroll the container
                    if (position === "bottom" && spaceBelow < menuHeight) {
                      const scrollNeeded = menuHeight - spaceBelow + 10; // Add some padding
                      scrollContainerRef.current.scrollTop += scrollNeeded;
                    }

                    setShowMemberMenu(member.id);
                  }}
                  className="invisible rounded p-1 hover:bg-gray-100 group-hover:visible"
                >
                  <svg
                    width="25"
                    height="24"
                    viewBox="0 0 25 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12.3333 13C12.8856 13 13.3333 12.5523 13.3333 12C13.3333 11.4477 12.8856 11 12.3333 11C11.7811 11 11.3333 11.4477 11.3333 12C11.3333 12.5523 11.7811 13 12.3333 13Z"
                      fill="#868C98"
                    />
                    <path
                      d="M20.5833 13C21.1356 13 21.5833 12.5523 21.5833 12C21.5833 11.4477 21.1356 11 20.5833 11C20.0311 11 19.5833 11.4477 19.5833 12C19.5833 12.5523 20.0311 13 20.5833 13Z"
                      fill="#868C98"
                    />
                    <path
                      d="M4.08334 13C4.63563 13 5.08334 12.5523 5.08334 12C5.08334 11.4477 4.63563 11 4.08334 11C3.53106 11 3.08334 11.4477 3.08334 12C3.08334 12.5523 3.53106 13 4.08334 13Z"
                      fill="#868C98"
                    />
                    <path
                      d="M12.3333 13C12.8856 13 13.3333 12.5523 13.3333 12C13.3333 11.4477 12.8856 11 12.3333 11C11.7811 11 11.3333 11.4477 11.3333 12C11.3333 12.5523 11.7811 13 12.3333 13Z"
                      stroke="#868C98"
                      stroke-width="1.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M20.5833 13C21.1356 13 21.5833 12.5523 21.5833 12C21.5833 11.4477 21.1356 11 20.5833 11C20.0311 11 19.5833 11.4477 19.5833 12C19.5833 12.5523 20.0311 13 20.5833 13Z"
                      stroke="#868C98"
                      stroke-width="1.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M4.08334 13C4.63563 13 5.08334 12.5523 5.08334 12C5.08334 11.4477 4.63563 11 4.08334 11C3.53106 11 3.08334 11.4477 3.08334 12C3.08334 12.5523 3.53106 13 4.08334 13Z"
                      stroke="#868C98"
                      stroke-width="1.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </button>

                {showMemberMenu === member.id && (
                  <div
                    ref={memberMenuRef}
                    className={`absolute right-0 z-50 w-48 rounded-xl border-2 border-gray-200 bg-white ${
                      menuPositions[member.id] === "top"
                        ? "bottom-full mb-2"
                        : "top-full mt-2"
                    }`}
                  >
                    <div className="py-1">
                      <button
                        onClick={() => {
                          onViewProfile(member.id);
                          setShowMemberMenu(null);
                        }}
                        className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <FiUser className="mr-2" /> View profile
                      </button>
                      {isGroupOwner && (
                        <button
                          onClick={() => {
                            onRemoveMember(group, member);
                            setShowMemberMenu(null);
                          }}
                          className="flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                        >
                          <FiUserMinus className="mr-2" /> Remove from group
                        </button>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default GroupCard;

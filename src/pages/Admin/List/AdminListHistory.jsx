import withAdminPermission from "Components/PermissionWrapper/AdminPermissionWrapper";
import React from "react";
import { GlobalContext } from "Context/Global";
import ListHistory from "Components/Shared/ListHistory";

function AdminListHistory() {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "history",
      },
    });
  }, []);
  return (
    <div className="h-screen p-5">
      <ListHistory />
    </div>
  );
}

export default withAdminPermission(
  AdminListHistory,
  "history",
  "You don't have permission to access history"
);

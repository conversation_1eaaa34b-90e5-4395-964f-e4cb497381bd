import { useState, useEffect } from "react";
import MkdSDK from "Utils/MkdSDK";
import Select from "react-select";
import LoadingSpinner from "Components/LoadingSpinner";
import ListStaff from "Components/Shared/ListStaff";
import withAdminPermission from "Components/PermissionWrapper/AdminPermissionWrapper";

let sdk = new MkdSDK();
function AdminListStaff() {
  const [profileSettings, setProfileSettings] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [clubs, setClubs] = useState([]);
  const [selectedClub, setSelectedClub] = useState(null);
  const [sports, setSports] = useState([]);
  async function fetchClubs() {
    setIsLoading(true);
    try {
      sdk.setTable("clubs");
      const response = await sdk.callRestAPI({}, "GETALL");
      setClubs(response.list);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  }
  const handleSelectClub = async (club) => {
    setIsLoading(true);
    try {
      setSelectedClub({
        id: club.value,
        name: club.label,
        club_id: club.club_id,
      });
      await fetchSettings(club.value);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchSettings = async (clubId) => {
    setIsLoading(true);
    try {
      const response = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/admin/profile/${clubId}`,
        {},
        "GET"
      );

      setProfileSettings(response?.model?.club);
      setSports(response?.model?.sports);
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchClubs();
  }, []);
  return (
    <div className="h-full bg-white p-4 sm:p-6 lg:p-8">
      {isLoading && <LoadingSpinner />}
      <div className="mb-4 max-w-xl">
        <label className="mb-2 block text-base font-medium text-gray-900">
          Select club
        </label>
        <Select
          className="w-full rounded-lg border border-gray-200 p-2.5 text-sm"
          options={clubs.map((club) => ({
            value: club.user_id,
            label: club.name,
            club_id: club?.id,
          }))}
          isMulti={false}
          onChange={handleSelectClub}
        />
      </div>
      {profileSettings?.id ? (
        <ListStaff club={profileSettings} sports={sports} />
      ) : (
        <div className="flex h-[calc(100vh-200px)] items-center justify-center">
          <div className="text-center">
            <h3 className="mb-2 text-xl font-medium text-gray-900">
              No Club Selected
            </h3>
            <p className="text-gray-600">
              Please select a club from the dropdown above to view and manage
              its details.
            </p>
          </div>
        </div>
      )}
    </div>
  );
}

// Export with permission wrapper
export default withAdminPermission(
  AdminListStaff,
  "staff",
  "You don't have permission to view staff"
);

import React, { useState } from "react";
import { AuthContext, tokenExpireError } from "Context/Auth";
import MkdSDK from "Utils/MkdSDK";
import { useNavigate } from "react-router-dom";
import { GlobalContext } from "Context/Global";
import * as yup from "yup";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { BiSearch } from "react-icons/bi";
import { PaginationBar } from "Components/PaginationBar";
import _ from "lodash";
import SkeletonLoader from "Components/Skeleton/Skeleton";
import LoadingOverlay from "Components/Loading/LoadingOverlay";
import { activityLogTypes, eventTypeOptions } from "Utils/utils";
import TreeSDK from "Utils/TreeSDK";
import DataTable from "Components/Shared/DataTable";
import RightSideModal from "Components/RightSideModal";
import AddFaq from "Components/Shared/AddFaq";
import FaqDetails from "Components/Shared/FaqDetails";
import { showToast } from "Context/Global";
import HistoryComponent from "Components/HistoryComponent";
import withAdminPermission from "Components/PermissionWrapper/AdminPermissionWrapper";
let sdk = new MkdSDK();
let tdk = new TreeSDK();

const columns = [
  {
    header: "Subcategory",
    accessor: "subcategory_id",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "Answer",
    accessor: "answer",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  //   {
  //     header: "Category",
  //     accessor: "category",
  //     isSorted: false,
  //     isSortedDesc: false,
  //     mappingExist: false,
  //     mappings: {},
  //   },
  //   {
  //     header: "Subcategory",
  //     accessor: "subcategory_id",
  //     isSorted: false,
  //     isSortedDesc: false,
  //     mappingExist: false,
  //     // mappings: { 1: "Indoor", 0: "Outdoor" },
  //   },

  {
    header: "Create At",
    accessor: "create_at",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  //   {
  //     header: "Status",
  //     accessor: "status",
  //     isSorted: false,
  //     isSortedDesc: false,
  //     mappingExist: false,
  //     mappings: { 1: "Paid", 0: "Reserved", 2: "Failed" },
  //   },
];

const AdminListGeneralFaqs = () => {
  const { dispatch: globalDispatch, state: globalState } =
    React.useContext(GlobalContext);
  const { dispatch } = React.useContext(AuthContext);
  const [data, setCurrentTableData] = React.useState([]);
  const [pageSize, setPageSize] = React.useState(10);
  const [pageCount, setPageCount] = React.useState(0);
  const [dataTotal, setDataTotal] = React.useState(0);
  const [currentPage, setPage] = React.useState(1);
  const [canPreviousPage, setCanPreviousPage] = React.useState(false);
  const [canNextPage, setCanNextPage] = React.useState(false);
  const [openFilter, setOpenFilter] = React.useState(false);

  const [loading, setLoading] = React.useState(true);
  const [showEditSidebar, setShowEditSidebar] = React.useState(false);
  const [showAddSidebar, setShowAddSidebar] = React.useState(false);
  const [activeEditId, setActiveEditId] = React.useState();
  const navigate = useNavigate();
  const dropdownFilterRef = React.useRef(null);
  const [showDetailsModal, setShowDetailsModal] = React.useState(false);
  const [selectedFaq, setSelectedFaq] = React.useState(null);
  const [showAddDrawer, setShowAddDrawer] = useState(false);

  const [surfaces, setSurfaces] = useState([]);
  const [players, setPlayers] = useState([]);

  const schema = yup.object({
    id: yup.string(),
    email: yup.string(),
    role: yup.string(),
    status: yup.string(),
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  function previousPage() {
    getData(currentPage - 1, pageSize);
  }

  function nextPage() {
    getData(currentPage + 1, pageSize);
  }

  async function getData(pageNum, limitNum, data = {}, filters = []) {
    setLoading(showAddSidebar || showEditSidebar ? false : true);
    try {
      const result = await tdk.getPaginate("faq", {
        page: pageNum,
        limit: limitNum,
        filter: [...filters, `general,eq,1`],
        join: [
          "faq_subcategory|subcategory_id",
          `${sdk._project_id}_faq_category|faq_subcategory.category_id,id`,
        ],
        size: pageSize,
      });
      if (result) {
        setLoading(false);
        setCurrentTableData(result.list);
        setPageSize(result.limit);
        setPageCount(result.num_pages);
        setPage(result.page);
        setDataTotal(result.total);
        setCanPreviousPage(result.page > 1);
        setCanNextPage(result.page + 1 <= result.num_pages);
      }
    } catch (error) {
      setLoading(false);
      console.log("ERROR", error);
      tokenExpireError(dispatch, error.message);
    }
  }

  const onSubmit = (data) => {
    if (data.search) {
      getData(1, pageSize, {}, [
        `first_name,cs,${data.search}`,
        `last_name,cs,${data.search}`,
      ]);
    } else {
      getData(1, pageSize);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "faq",
      },
    });

    getData(1, pageSize, {});
  }, []);

  const handleClickOutside = (event) => {
    if (
      dropdownFilterRef.current &&
      !dropdownFilterRef.current.contains(event.target)
    ) {
      setOpenFilter(false);
    }
  };

  React.useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleOpenDetails = (faq) => {
    setSelectedFaq(faq);
    setShowDetailsModal(true);
  };

  const renderCustomCell = {
    type: (row) => (
      <span className="capitalize">
        {eventTypeOptions.find(
          (option) => option.value == row?.booking?.reservation_type
        )?.label || "--"}
      </span>
    ),
    question: (row) => (
      <span className="capitalize">{row?.faq_subcategory.name || "--"}</span>
    ),
    answer: (row) => <>{_.truncate(row?.answer, { length: 70 }) || "--"}</>,
    category: (row) => <>{row?.faq_category.name || "--"}</>,
    subcategory_id: (row) => <>{row?.faq_subcategory.name || "--"}</>,
    create_at: (row) => {
      if (!row?.create_at) return "--";
      const date = new Date(row.create_at);
      return `${date.getDate()}, ${date.toLocaleString("default", {
        month: "long",
      })}, ${date.getFullYear()}`;
    },
  };

  const handleAddSuccess = () => {
    getData(1, pageSize);
  };

  return (
    <div className="h-screen px-8">
      {loading && <LoadingOverlay />}
      <div className="flex flex-col justify-between gap-4 py-3 md:flex-row md:items-center">
        <div className="flex items-center gap-4">
          <form className="relative flex flex-1 items-center">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <BiSearch className="text-gray-500" />
            </div>
            <input
              type="text"
              className="block w-full rounded-lg border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              placeholder="Search FAQ by answer"
              onChange={(e) => {
                const value = e.target.value.trim();
                if (value) {
                  getData(1, pageSize, {}, [`answer,cs,${value}`]);
                } else {
                  getData(1, pageSize);
                }
              }}
            />
          </form>
        </div>

        <div className="flex items-center gap-4">
          <button
            onClick={() => setShowAddDrawer(true)}
            className="inline-flex max-w-fit items-center gap-2 rounded-lg bg-[#1D275F] px-4 py-2 text-sm font-semibold text-white hover:bg-blue-700"
          >
            <span>+</span>
            Add new
          </button>

          <HistoryComponent
            activityType={activityLogTypes.faq}
            emptyMessage={"No FAQ history found"}
            title={"FAQ History"}
          />
        </div>
      </div>

      {/* Add FAQ Modal */}
      <RightSideModal
        isOpen={showAddDrawer}
        onClose={() => setShowAddDrawer(false)}
        title="Add New FAQ"
        showFooter={false}
      >
        <AddFaq
          onClose={() => setShowAddDrawer(false)}
          onSuccess={handleAddSuccess}
        />
      </RightSideModal>

      {/* FAQ Details Modal */}
      <RightSideModal
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        title="FAQ Details"
        showFooter={false}
      >
        <FaqDetails faq={selectedFaq} />
      </RightSideModal>

      {loading ? (
        <SkeletonLoader />
      ) : (
        <div className="overflow-x-auto">
          <DataTable
            columns={columns}
            data={data}
            loading={loading}
            renderCustomCell={renderCustomCell}
            rowClassName="hover:bg-gray-40 bg-gray-100 px-4 py-3 text-gray-500 cursor-pointer"
            cellClassName="whitespace-nowrap px-6 py-4"
            headerClassName="px-6 py-4 text-left text-sm font-medium text-gray-500"
            emptyMessage="No data available"
            onClick={(row) => handleOpenDetails(row)}
          />
        </div>
      )}
      <PaginationBar
        currentPage={currentPage}
        pageCount={pageCount}
        pageSize={pageSize}
        canPreviousPage={canPreviousPage}
        canNextPage={canNextPage}
        updatePageSize={(newPageSize) => {
          setPageSize(newPageSize);
          getData(1, newPageSize);
        }}
        previousPage={previousPage}
        nextPage={nextPage}
        gotoPage={(pageNum) => getData(pageNum, pageSize)}
      />
    </div>
  );
};

export default withAdminPermission(
  AdminListGeneralFaqs,
  "general_faqs",
  "You don't have permission to access general FAQs"
);

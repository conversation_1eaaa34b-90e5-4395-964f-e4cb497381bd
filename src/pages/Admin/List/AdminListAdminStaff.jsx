import React from "react";
import { AuthContext, tokenExpireError } from "Context/Auth";
import MkdSDK from "Utils/MkdSDK";
import { useNavigate } from "react-router-dom";
import { GlobalContext, showToast } from "Context/Global";
import * as yup from "yup";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { BiSearch } from "react-icons/bi";
import { AiOutlineClose, AiOutlinePlus } from "react-icons/ai";
import { PaginationBar } from "Components/PaginationBar";
import _ from "lodash";
import SkeletonLoader from "Components/Skeleton/Skeleton";
import { Link } from "react-router-dom";
// import TimeSlot from "Components/TimeSlot";
import { fCurrency } from "Utils/formatNumber";
import TimeSlotPicker from "Components/TimeslotPicker";
import DeleteModal from "Components/Modals/DeleteModal";
import TreeSDK from "Utils/TreeSDK";
import DataTable from "../../../components/Shared/DataTable";
import HistoryComponent from "Components/HistoryComponent";
import { activityLogTypes, actionLogTypes, logActivity } from "Utils/utils";
import RightSideModal from "../../../components/RightSideModal";
import RoleAccessManager from "Components/RoleAccessManager";
import { AdminStaffPermissionWrapper } from "Components/PermissionWrapper/AdminPermissionWrapper";

// Format phone number to (XXX) XXX-XXXX format
const formatPhoneNumber = (phoneNumberString) => {
  if (!phoneNumberString) return "--";

  // Strip all non-numeric characters
  const cleaned = phoneNumberString.replace(/\D/g, "");

  // Check if the number has the expected length
  if (cleaned.length < 10) return phoneNumberString;

  // Format the number
  const match = cleaned.match(/^(\d{1})(\d{3})(\d{3})(\d{4})$/);
  if (match) {
    return `(${match[2]}) ${match[3]}-${match[4]}`;
  } else if (cleaned.length === 10) {
    const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
    return `(${match[1]}) ${match[2]}-${match[3]}`;
  }

  return phoneNumberString;
};

let sdk = new MkdSDK();
let tdk = new TreeSDK();
const days = [
  "monday",
  "tuesday",
  "wednesday",
  "thursday",
  "friday",
  "saturday",
  "sunday",
];
const columns = [
  {
    header: "Name",
    accessor: "name",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "Status",
    accessor: "status",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: true,
    mappings: { 1: "Active", 0: "Inactive" },
  },
  {
    header: "Role",
    accessor: "role",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
  },
  {
    header: "Email",
    accessor: "email",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
  },
  {
    header: "Phone",
    accessor: "phone",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
  },
  {
    header: "Bank details",
    accessor: "bank_details",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Action",
    accessor: "",
  },
];

const ListStaff = () => {
  const { dispatch: globalDispatch, state: globalState } =
    React.useContext(GlobalContext);
  const { dispatch } = React.useContext(AuthContext);
  const [data, setCurrentTableData] = React.useState([]);
  const [pageSize, setPageSize] = React.useState(10);
  const [pageCount, setPageCount] = React.useState(0);
  const [dataTotal, setDataTotal] = React.useState(0);
  const [currentPage, setPage] = React.useState(1);
  const [canPreviousPage, setCanPreviousPage] = React.useState(false);
  const [canNextPage, setCanNextPage] = React.useState(false);
  const [openFilter, setOpenFilter] = React.useState(false);
  const [loading, setLoading] = React.useState(true);
  const [showEditSidebar, setShowEditSidebar] = React.useState(false);
  const [showAddSidebar, setShowAddSidebar] = React.useState(false);
  const [showTimesAvailableModal, setShowTimesAvailableModal] =
    React.useState(false);
  const [selectedTimes, setSelectedTimes] = React.useState([]);
  const [selectedStaff, setSelectedStaff] = React.useState(null);
  const [submittingAvailability, setSubmittingAvailability] =
    React.useState(false);
  const [staffs, setStaff] = React.useState([]);
  const [showDeleteModal, setShowDeleteModal] = React.useState(false);
  const [deletingStaff, setDeletingStaff] = React.useState(false);
  const [activeDeleteId, setActiveDeleteId] = React.useState(null);
  const [showContactInfo, setShowContactInfo] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState("");
  const [searchTimeout, setSearchTimeout] = React.useState(null);
  const [showRoleAccess, setShowRoleAccess] = React.useState(false);
  const [roleAccessData, setRoleAccessData] = React.useState([]);
  const [showAddModal, setShowAddModal] = React.useState(false);
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const role = localStorage.getItem("role");
  const user_id = localStorage.getItem("user");
  // console.log("globalState.clubProfile", globalState.clubProfile);
  const navigate = useNavigate();
  const dropdownFilterRef = React.useRef(null);

  // Form validation schema for adding new admin staff
  const schema = yup.object().shape({
    email: yup.string().email("Invalid email").required("Email is required"),
    password: yup
      .string()
      .required("Password is required")
      .min(6, "Password must be at least 6 characters"),
  });

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  function previousPage() {
    getData(currentPage - 1, pageSize);
  }

  function nextPage() {
    getData(currentPage + 1, pageSize);
  }

  async function getData(pageNum, limitNum, data = {}, filters = []) {
    setLoading(showAddSidebar || showEditSidebar ? false : true);
    try {
      const result = await tdk.getPaginate("admin_staff", {
        page: pageNum,
        limit: limitNum,
        filter: [...filters],
        join: ["user|user_id"],
      });

      if (result) {
        setLoading(false);
      }
      const { list, total, limit, num_pages, page } = result;

      setCurrentTableData(list);
      setPageSize(limit);
      setPageCount(num_pages);
      setPage(page);
      setDataTotal(total);
      setCanPreviousPage(page > 1);
      setCanNextPage(page + 1 <= num_pages);
    } catch (error) {
      setLoading(false);
      console.log("ERROR", error);
      tokenExpireError(dispatch, error.message);
    }
  }

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "admin_staff",
      },
    });

    const delay = 700;
    const timeoutId = setTimeout(async () => {
      await getData(currentPage, pageSize, {});
    }, delay);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [currentPage, pageSize]);

  const handleClickOutside = (event) => {
    if (
      dropdownFilterRef.current &&
      !dropdownFilterRef.current.contains(event.target)
    ) {
      setOpenFilter(false);
    }
  };

  React.useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleDelete = async ({ staff_id, user_id: staff_user_id }) => {
    setDeletingStaff(true);
    try {
      sdk.setTable("user");
      await sdk.callRestAPI({ id: staff_user_id }, "DELETE");
      sdk.setTable("staff");
      await sdk.callRestAPI({ id: staff_id }, "DELETE");

      await logActivity(sdk, {
        user_id,
        activity_type: activityLogTypes.staff_management,
        action_type: actionLogTypes.DELETE,
        data: { staff_id, staff_user_id },
        description: "Deleted staff",
      });

      showToast(globalDispatch, "Staff deleted successfully", 3000, "success");
      getData(currentPage, pageSize); // Refresh the data
    } catch (error) {
      console.error("Error deleting staff:", error);
      showToast(globalDispatch, error.message, 3000, "error");
      tokenExpireError(dispatch, error.message);
    }
    setDeletingStaff(false);
  };

  const handleAddStaff = async (formData) => {
    setIsSubmitting(true);
    try {
      const userResponse = await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/staff/register",
        {
          email: formData.email,
          password: formData.password,
          role: "admin_staff",
          verify: 1,
          status: 1,
          club_id: 0,
        },
        "POST"
      );

      // send login credentials email to admin staffs
      await sdk.sendMail({
        email: formData.email,
        subject: "Admin Staff Login Credentials",
        body: `Your login credentials are: <br> Email: ${formData.email} <br> Password: ${formData.password} <br><br> You can log in at: <a href="${window.location.origin}/admin_staff/login" style="color: #1D275F; text-decoration: underline;">Admin Staff Login Portal</a>`,
      });

      await logActivity(sdk, {
        user_id,
        activity_type: activityLogTypes.staff_management,
        action_type: actionLogTypes.CREATE,
        data: { email: formData.email },
        description: "Added new admin staff",
      });

      showToast(
        globalDispatch,
        "Admin staff added successfully",
        3000,
        "success"
      );
      reset(); // Reset form
      setShowAddModal(false); // Close modal
      getData(currentPage, pageSize); // Refresh the data
    } catch (error) {
      console.error("Error adding admin staff:", error);
      showToast(globalDispatch, error.message, 3000, "error");
      tokenExpireError(dispatch, error.message);
    }
    setIsSubmitting(false);
  };

  const handleAvailabilityClick = (staff) => {
    setSelectedStaff(staff);
    setShowTimesAvailableModal(true);
    if (staff.availability) {
      try {
        const availability = JSON.parse(staff.availability);

        // Initialize with all days having empty timeslots
        const initialAvailability = days.map((day) => ({
          day: day.toLowerCase(),
          timeslots: [],
        }));
        // If there's existing availability, update the corresponding days
        if (
          availability &&
          Array.isArray(availability) &&
          availability.length > 0
        ) {
          availability.forEach((dayData) => {
            const index = initialAvailability.findIndex(
              (day) => day.day === dayData.day.toLowerCase()
            );
            if (index !== -1) {
              initialAvailability[index].timeslots = dayData.timeslots;
            }
          });
        }
        setSelectedTimes(initialAvailability);
      } catch (error) {
        console.error("Error parsing availability:", error);
        setSelectedTimes([]);
      }
    } else {
      setSelectedTimes([]);
    }
  };

  React.useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        showContactInfo &&
        !event.target.closest("#contact-info-button") &&
        !event.target.closest(".contact-info-popover")
      ) {
        setShowContactInfo(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [showContactInfo]);

  const handleSearch = (e) => {
    const query = e.target.value;
    setSearchQuery(query);

    // Clear existing timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // Set new timeout for debounced search
    const newTimeout = setTimeout(() => {
      if (query.trim()) {
        // project_table.field,cs,value
        getData(currentPage, pageSize, {}, [
          `${sdk._project_id}_user.first_name,cs,${query}`,
        ]);
      } else {
        getData(currentPage, pageSize);
      }
    }, 500); // 500ms delay

    setSearchTimeout(newTimeout);
  };

  // Fetch role access data for admin_staff (global level)
  const fetchRoleAccessData = async () => {
    try {
      sdk.setTable("club_permissions");
      const result = await sdk.callRestAPI(
        {
          filter: [`club_id,eq,0`], // Admin staff operates globally with club_id 0
        },
        "GETALL"
      );
      if (result.list.length > 0) {
        setRoleAccessData(result.list[0]);
      } else {
        setRoleAccessData(null);
      }
    } catch (error) {
      console.error("Error fetching role access data:", error);
      setRoleAccessData(null);
    }
  };

  // Fetch role access data when component mounts
  React.useEffect(() => {
    fetchRoleAccessData();
  }, []);

  // Define custom cell renderers
  const renderCustomCell = {
    name: (row) => (
      <div className="flex items-center gap-3">
        <img
          src={row.user?.photo || "/default-avatar.png"}
          alt={`${row.user?.first_name} ${row.user?.last_name}`}
          className="h-10 w-10 rounded-full object-cover"
        />
        <span className="font-medium capitalize text-gray-900">
          {!row?.user?.first_name || !row?.user?.last_name
            ? "--"
            : `${row?.user?.first_name} ${row?.user?.last_name}`}
        </span>
      </div>
    ),
    status: (row) => (
      <span className="text-gray-600">
        {row?.user?.status === 1 ? "Active" : "Inactive"}
      </span>
    ),
    role: (row) => (
      <span className="capitalize text-gray-600">
        {row?.user?.role ? row?.user?.role : "--"}
      </span>
    ),
    email: (row) => (
      <span className="text-gray-600">{row?.user?.email || "--"}</span>
    ),
    phone: (row) => (
      <span className="text-gray-600">
        {row?.user?.phone ? formatPhoneNumber(row.user.phone) : "--"}
      </span>
    ),
    bank_details: (row) => (
      <span className="text-gray-600">
        {(() => {
          try {
            if (row?.account_details) {
              const parsedDetails = JSON.parse(row.account_details);
              if (
                parsedDetails &&
                Array.isArray(parsedDetails) &&
                parsedDetails.length > 0 &&
                parsedDetails[0]?.account_number
              ) {
                const accountNumber = parsedDetails[0].account_number;
                const lastFourDigits = accountNumber.slice(-4);
                return `•••• ${lastFourDigits}`;
              } else {
                return "--";
              }
            }
            return "--";
          } catch (error) {
            return "--";
          }
        })()}
      </span>
    ),
    actions: (row) => (
      <div className="flex items-center justify-end gap-3">
        <Link
          to={`/${role}/view-staff/${row.id}`}
          className="rounded-full p-2 hover:bg-gray-100"
        >
          {/* View icon SVG */}
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M6.88016 17.7625C7.97481 16.1634 9.77604 15.1243 11.9993 15.1243C14.2227 15.1243 16.0239 16.1634 17.1185 17.7625M6.88016 17.7625C8.24153 18.9726 10.0346 19.7077 11.9993 19.7077C13.9641 19.7077 15.7572 18.9726 17.1185 17.7625M6.88016 17.7625C5.29173 16.3505 4.29102 14.2918 4.29102 11.9993C4.29102 7.74215 7.74215 4.29102 11.9993 4.29102C16.2565 4.29102 19.7077 7.74215 19.7077 11.9993C19.7077 14.2918 18.707 16.3505 17.1185 17.7625M14.7077 10.3327C14.7077 11.8285 13.4951 13.041 11.9993 13.041C10.5036 13.041 9.29102 11.8285 9.29102 10.3327C9.29102 8.83691 10.5036 7.62435 11.9993 7.62435C13.4951 7.62435 14.7077 8.83691 14.7077 10.3327Z"
              stroke="#868C98"
              strokeWidth="1.5"
              strokeLinejoin="round"
            />
          </svg>
        </Link>
        <button
          onClick={() => handleAvailabilityClick(row)}
          className="rounded-full p-2 hover:bg-gray-100"
        >
          {/* Availability icon SVG */}
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M2.46209 9.95722L3.76445 17.3433C3.86035 17.8872 4.37901 18.2503 4.9229 18.1544L10.1162 17.2387M2.46209 9.95722L1.94114 7.0028C1.84524 6.45891 2.20841 5.94025 2.7523 5.84434L14.0776 3.84739C14.6215 3.75149 15.1401 4.11466 15.236 4.65855L15.757 7.61297L2.46209 9.95722ZM16.0002 11.7509V14.0009L18.0002 16.0009M22.2502 14.0009C22.2502 17.4527 19.452 20.2509 16.0002 20.2509C12.5485 20.2509 9.75025 17.4527 9.75025 14.0009C9.75025 10.5491 12.5485 7.75092 16.0002 7.75092C19.452 7.75092 22.2502 10.5491 22.2502 14.0009Z"
              stroke="#868C98"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>

        <button
          onClick={() => {
            setShowDeleteModal(true);
            setActiveDeleteId({
              staff_id: row.id,
              user_id: row.user_id,
            });
          }}
          className="rounded-full p-2 hover:bg-gray-100"
        >
          <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M4.74071 16.928L3.99227 16.9763L4.74071 16.928ZM15.258 16.928L16.0064 16.9763L16.0064 16.9763L15.258 16.928ZM2.29102 4.04102C1.8768 4.04102 1.54102 4.3768 1.54102 4.79102C1.54102 5.20523 1.8768 5.54102 2.29102 5.54102V4.04102ZM17.7077 5.54102C18.1219 5.54102 18.4577 5.20523 18.4577 4.79102C18.4577 4.3768 18.1219 4.04102 17.7077 4.04102V5.54102ZM8.87435 8.95768C8.87435 8.54347 8.53856 8.20768 8.12435 8.20768C7.71014 8.20768 7.37435 8.54347 7.37435 8.95768H8.87435ZM7.37435 13.541C7.37435 13.9552 7.71014 14.291 8.12435 14.291C8.53856 14.291 8.87435 13.9552 8.87435 13.541H7.37435ZM12.6243 8.95768C12.6243 8.54347 12.2886 8.20768 11.8743 8.20768C11.4601 8.20768 11.1243 8.54347 11.1243 8.95768H12.6243ZM11.1243 13.541C11.1243 13.9552 11.4601 14.291 11.8743 14.291C12.2886 14.291 12.6243 13.9552 12.6243 13.541H11.1243ZM12.5014 4.97796C12.6046 5.3791 13.0135 5.62059 13.4146 5.51734C13.8158 5.4141 14.0573 5.00521 13.954 4.60407L12.5014 4.97796ZM3.20924 4.8393L3.99227 16.9763L5.48916 16.8797L4.70613 4.74273L3.20924 4.8393ZM5.57232 18.4577H14.4264V16.9577H5.57232V18.4577ZM16.0064 16.9763L16.7895 4.8393L15.2926 4.74273L14.5095 16.8797L16.0064 16.9763ZM16.041 4.04102H3.95768V5.54102H16.041V4.04102ZM2.29102 5.54102H3.95768V4.04102H2.29102V5.54102ZM16.041 5.54102H17.7077V4.04102H16.041V5.54102ZM14.4264 18.4577C15.2613 18.4577 15.9527 17.8094 16.0064 16.9763L14.5095 16.8797C14.5067 16.9236 14.4703 16.9577 14.4264 16.9577V18.4577ZM3.99227 16.9763C4.04602 17.8094 4.73744 18.4577 5.57232 18.4577V16.9577C5.52838 16.9577 5.49199 16.9236 5.48916 16.8797L3.99227 16.9763ZM7.37435 8.95768V13.541H8.87435V8.95768H7.37435ZM11.1243 8.95768V13.541H12.6243V8.95768H11.1243ZM9.99937 3.04102C11.2021 3.04102 12.2145 3.86359 12.5014 4.97796L13.954 4.60407C13.5008 2.84306 11.9031 1.54102 9.99937 1.54102V3.04102ZM7.49738 4.97796C7.78419 3.86359 8.79666 3.04102 9.99937 3.04102V1.54102C8.09567 1.54102 6.49797 2.84306 6.04472 4.60407L7.49738 4.97796Z"
              fill="#868C98"
            />
          </svg>
        </button>
      </div>
    ),
  };

  // Update columns configuration
  const tableColumns = [
    {
      header: "Name",
      accessor: "name",
    },
    {
      header: "Status",
      accessor: "status",
    },
    {
      header: "Role",
      accessor: "role",
    },
    {
      header: "Email",
      accessor: "email",
    },
    {
      header: "Phone",
      accessor: "phone",
    },
    {
      header: "Bank details",
      accessor: "bank_details",
    },
    {
      header: "Actions",
      accessor: "actions",
    },
  ];
  console.log("roleAccessData", roleAccessData);
  return (
    <div className="h-screen p-5">
      <div className="flex w-full flex-col justify-between gap-4 py-3 md:flex-row md:items-center">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:gap-4">
          <div className="relative flex max-w-md flex-1 items-center">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <BiSearch className="text-gray-500" />
            </div>
            <input
              type="text"
              value={searchQuery}
              onChange={handleSearch}
              className="block w-full min-w-[200px] rounded-md border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              placeholder="Search staff by name or email"
            />
            {searchQuery && (
              <button
                onClick={() => {
                  setSearchQuery("");
                  getData(currentPage, pageSize);
                }}
                className="absolute right-2 rounded-full p-1 hover:bg-gray-100"
              >
                <AiOutlineClose className="text-gray-500" />
              </button>
            )}
          </div>
        </div>
        <div className="flex items-center gap-4">
          <button
            onClick={() => setShowRoleAccess(true)}
            className="inline-flex items-center gap-2 rounded-lg border border-gray-200 px-4 py-2 text-sm font-semibold text-gray-700 hover:bg-gray-50"
          >
            <svg
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M4.75038 16.8788V11.6684M4.75038 8.54214V3.12305M9.9992 16.6703V10.6259M9.99922 7.49978V3.33138M15.248 16.8785V13.3354M15.248 10.2095V3.12305M3.12109 11.46H6.45753M8.33253 7.70801H11.6659M13.5409 13.1271H16.8742"
                stroke="#868C98"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            Manage role access
          </button>
          <button
            onClick={() => setShowAddModal(true)}
            className="inline-flex items-center gap-2 rounded-lg bg-[#1D275F] px-4 py-2 text-sm font-semibold text-white hover:bg-blue-700"
          >
            <span className="">+</span>
            Add new
          </button>
        </div>

        <div className=" ">
          <HistoryComponent
            title="Staff History"
            emptyMessage="No staff history found"
            activityType={activityLogTypes.staff_management}
          />
        </div>
      </div>
      {loading ? (
        <SkeletonLoader />
      ) : (
        <DataTable
          columns={tableColumns}
          data={data}
          loading={loading}
          renderCustomCell={renderCustomCell}
          emptyMessage="No staff members found"
          loadingMessage="Loading staff members..."
        />
      )}
      <PaginationBar
        currentPage={currentPage}
        pageCount={pageCount}
        pageSize={pageSize}
        canPreviousPage={canPreviousPage}
        canNextPage={canNextPage}
        updatePageSize={(newPageSize) => {
          setPageSize(newPageSize);
          getData(currentPage, newPageSize);
        }}
        previousPage={previousPage}
        nextPage={nextPage}
        gotoPage={(pageNum) => getData(pageNum, pageSize)}
      />
      {/* <ModalSidebar
        isModalActive={showAddSidebar}
        closeModalFn={() => setShowAddSidebar(false)}
      >
        <AddAdminEmailPage setSidebar={setShowAddSidebar} getData={getData} />
      </ModalSidebar> */}
      {/* {showEditSidebar && (
        <ModalSidebar
          isModalActive={showEditSidebar}
          closeModalFn={() => setShowEditSidebar(false)}
        >
          <EditAdminEmailPage
            activeId={activeEditId}
            setSidebar={setShowEditSidebar}
          />
        </ModalSidebar>
      )} */}

      {showTimesAvailableModal && selectedStaff && (
        <TimeSlotPicker
          showTimesAvailableModal={showTimesAvailableModal}
          setShowTimesAvailableModal={setShowTimesAvailableModal}
          selectedTimes={selectedTimes}
          setSelectedTimes={setSelectedTimes}
          title={`${selectedStaff?.user?.first_name} ${selectedStaff?.user?.last_name}'s availability`}
          isSubmitting={submittingAvailability}
          onSave={async (formattedTimes) => {
            try {
              setSubmittingAvailability(true);
              const formatSelectedTimes = () => {
                if (!selectedTimes) return [];
                return selectedTimes.filter(
                  (daySlot) => daySlot.timeslots.length > 0
                );
              };
              const formattedData = formatSelectedTimes();
              sdk.setTable("staff");
              await sdk.callRestAPI(
                {
                  id: selectedStaff.id,
                  availability: JSON.stringify(formattedData),
                },
                "PUT"
              );
              await logActivity(sdk, {
                user_id,
                activity_type: activityLogTypes.staff_management,
                action_type: actionLogTypes.UPDATE,
                data: {
                  staff_id: selectedStaff.id,
                  availability: formattedData,
                },
                description: `Updated availability for ${selectedStaff?.user?.first_name} ${selectedStaff?.user?.last_name}`,
              });
              showToast(
                globalDispatch,
                "Availability updated successfully",
                3000,
                "success"
              );
              getData(currentPage, pageSize);
            } catch (error) {
              console.error("Error saving availability:", error);
              showToast(dispatch, error.message, 3000, "error");
            } finally {
              setSubmittingAvailability(false);
              setShowTimesAvailableModal(false);
              setSelectedStaff(null);
            }
          }}
        />
      )}

      <DeleteModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onDelete={() => handleDelete(activeDeleteId)}
        message="Are you sure you want to delete this staff?"
        loading={deletingStaff}
        title="Delete Staff"
      />

      {/* Add New Admin Staff Modal */}
      <RightSideModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        title="Add New Admin Staff"
        primaryButtonText="Add Staff"
        onPrimaryAction={handleSubmit(handleAddStaff)}
        submitting={isSubmitting}
        primaryButtonDisabled={isSubmitting}
      >
        <div className="flex flex-col gap-4">
          <div className="flex flex-col gap-1">
            <label
              htmlFor="email"
              className="text-sm font-medium text-gray-700"
            >
              Email
            </label>
            <input
              id="email"
              type="email"
              {...register("email")}
              className="rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              placeholder="Enter email address"
            />
            {errors.email && (
              <p className="mt-1 text-xs text-red-600">
                {errors.email.message}
              </p>
            )}
          </div>

          <div className="flex flex-col gap-1">
            <label
              htmlFor="password"
              className="text-sm font-medium text-gray-700"
            >
              Password
            </label>
            <input
              id="password"
              type="password"
              {...register("password")}
              className="rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              placeholder="Enter password"
            />
            {errors.password && (
              <p className="mt-1 text-xs text-red-600">
                {errors.password.message}
              </p>
            )}
          </div>

          <p className="mt-2 text-xs text-gray-500">
            After adding the staff member, they will be able to log in with
            these credentials. You can manage their permissions from the role
            access settings.
          </p>
        </div>
      </RightSideModal>

      <RoleAccessManager
        isOpen={showRoleAccess}
        onClose={() => {
          setShowRoleAccess(false);
          fetchRoleAccessData(); // Refresh role access data when modal closes
        }}
        roleAccessData={roleAccessData}
        club={{ id: 0 }} // Admin staff operates globally, so use club_id 0
        roleLabel="Admin Staff" // Use "Admin Staff" instead of "Staff" for admin portal
      />
    </div>
  );
};

// Wrap the component with admin staff permission check
const AdminListAdminStaffWithPermissions = () => {
  return (
    <AdminStaffPermissionWrapper restrictionMessage="You don't have permission to access admin staff management. This module is disabled by default and can be enabled by an admin.">
      <ListStaff />
    </AdminStaffPermissionWrapper>
  );
};

export default AdminListAdminStaffWithPermissions;

import SportList from "Components/ClubListUI/SportList";
import { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { GlobalContext } from "Context/Global";
import MkdSDK from "Utils/MkdSDK";
import LoadingSpinner from "Components/LoadingSpinner";
import React from "react";

let sdk = new MkdSDK();
export default function AdminListSports() {
  const [clubUser, setClubUser] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [club, setClub] = useState(null);
  const [sports, setSports] = useState([]);
  const location = useLocation();
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const clubId = location.state?.clubId;

  const fetchSettings = async () => {
    if (!clubId) return;

    setIsLoading(true);
    try {
      const response = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/admin/profile/${clubId}`,
        {},
        "GET"
      );
      setClub(response?.model?.club);
      setSports(response?.model?.sports || []);
      setClubUser(response?.model?.user);
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "sports",
      },
    });
    fetchSettings();
  }, [clubId]);

  return (
    <div className="h-screen p-8">
      {isLoading && <LoadingSpinner />}
      <SportList
        club={club}
        fetchSettings={fetchSettings}
        sports={sports}
        isAdmin={true}
        clubUser={clubUser}
      />
    </div>
  );
}

import { useEffect, useState } from "react";
import { Tab } from "@headlessui/react";
import { BsSearch } from "react-icons/bs";
import { BiSearch } from "react-icons/bi";
import { GlobalContext } from "Context/Global";
import React from "react";
import AdminChat from "Components/SupportChat/AdminChat";
import AdminListTickets from "Components/SupportChat/AdminTickets";

export default function ClubCustomerSupportPage() {
  const [selectedTab, setSelectedTab] = useState(0);
  const [searchQuery, setSearchQuery] = useState("");
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [activeChatTab, setActiveChatTab] = useState("open");

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "customer-support",
      },
    });
  }, []);

  // Update activeChatTab when tab changes
  React.useEffect(() => {
    if (selectedTab === 0) {
      setActiveChatTab("open");
    } else if (selectedTab === 1) {
      setActiveChatTab("resolved");
    }
  }, [selectedTab]);

  return (
    <div className="flex h-screen w-full flex-col bg-gray-50">
      <div className="flex h-full flex-col gap-4 bg-white px-5 py-5">
        <Tab.Group onChange={setSelectedTab}>
          <Tab.List className="flex space-x-4 border-b border-gray-200">
            <Tab
              className={({ selected }) =>
                `px-1 pb-2 ${
                  selected
                    ? "border-b-2 border-blue-600 text-blue-600"
                    : "text-gray-500"
                }`
              }
            >
              Open chats
            </Tab>
            <Tab
              className={({ selected }) =>
                `px-1 pb-2 ${
                  selected
                    ? "border-b-2 border-blue-600 text-blue-600"
                    : "text-gray-500"
                }`
              }
            >
              Resolved chats
            </Tab>
            <Tab
              className={({ selected }) =>
                `px-1 pb-2 ${
                  selected
                    ? "border-b-2 border-blue-600 text-blue-600"
                    : "text-gray-500"
                }`
              }
            >
              Open tickets
            </Tab>
            <Tab
              className={({ selected }) =>
                `px-1 pb-2 ${
                  selected
                    ? "border-b-2 border-blue-600 text-blue-600"
                    : "text-gray-500"
                }`
              }
            >
              Resolved tickets
            </Tab>
          </Tab.List>

          {/* <Tab.Panels className="mt-4 h-full">
            <Tab.Panel className={"h-full"}>
              <ClubListChats activeTab="open" />
            </Tab.Panel>
            <Tab.Panel className="h-full">
              <ClubListChats activeTab="resolved" />
            </Tab.Panel>
            <Tab.Panel>
              <AdminListTickets status={0} />
            </Tab.Panel>
            <Tab.Panel>
              <AdminListTickets status={1} />
            </Tab.Panel>
          </Tab.Panels> */}
        </Tab.Group>
      </div>
    </div>
  );
}

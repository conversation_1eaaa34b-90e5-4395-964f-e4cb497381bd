import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "Utils/MkdSDK";
import { GlobalContext, showToast } from "Context/Global";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { InteractiveButton } from "Components/InteractiveButton";
import LoadingSpinner from "Components/LoadingSpinner";
import TreeSDK from "Utils/TreeSDK";
import ImageCropModal from "Components/Modals/ImageCropModal";

let sdk = new MkdSDK();
let tdk = new TreeSDK();
const CustomAdminStaffProfile = () => {
  const schema = yup
    .object({
      email: yup.string().email().required(),
    })
    .required();

  const { dispatch } = React.useContext(AuthContext);
  const [oldPhoto, setOldPhoto] = useState("");
  const [submitLoading, setSubmitLoading] = useState(false);
  const [defaultValues, setDefaultValues] = useState({});
  const [loading, setLoading] = useState(true);
  const [editingField, setEditingField] = useState(null);
  const [editValue, setEditValue] = useState("");
  const [profilePictureLoading, setProfilePictureLoading] = useState(false);
  const [tempImageUrl, setTempImageUrl] = useState(null);
  const [isCropModalOpen, setIsCropModalOpen] = useState(false);
  const [originalFileType, setOriginalFileType] = useState(null);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const userId = localStorage.getItem("user");
  async function fetchData() {
    setLoading(true);
    try {
      const profileResponse = await tdk.getList("profile", {
        filter: [`user_id,eq,${userId}`],
        join: ["user|user_id"],
      });
      const profileData = profileResponse?.list?.[0];

      if (profileData) {
        // Combine profile and user data
        const userData = profileData.user || {};

        // Store the profile ID separately to ensure we use the correct ID
        const profileId = profileData.id;

        // Create a combined result object
        const result = {
          ...profileData,
          ...userData,
          profile_id: profileId, // Store profile ID separately
          user_id: userData.id, // Store user ID separately
        };

        setDefaultValues(result);

        // Set values from user table
        setValue("email", userData?.email);
        setValue("first_name", userData?.first_name);
        setValue("last_name", userData?.last_name);
        setValue("phone", userData?.phone);
        setValue("bio", userData?.bio);
        setOldPhoto(userData?.photo);

        // Set values from profile table
        setValue("gender", profileData?.gender);
        setValue("address", profileData?.address);
        setValue("city", profileData?.city);
        setValue("state", profileData?.state);
        setValue("zip_code", profileData?.zip_code);

        dispatch({
          type: "UPDATE_PROFILE",
          payload: result,
        });
        setLoading(false);
      }
    } catch (error) {
      tokenExpireError(
        dispatch,
        error.response.data.message
          ? error.response.data.message
          : error.message
      );
    }
  }

  // List of fields that belong to the user table
  const userTableFields = [
    "email",
    "first_name",
    "last_name",
    "phone",
    "bio",
    "photo",
    "alternative_phone",
    "age_group",
    "family_role",
    "password",
    "verify",
    "status",
  ];

  // List of fields that belong to the profile table
  const profileTableFields = [
    "gender",
    "address",
    "city",
    "state",
    "zip_code",
    "date_of_birth",
    "country",
    "house_no",
  ];

  const handleEdit = async (field, value) => {
    try {
      setSubmitLoading(true);
      const data = {
        [field]: value,
      };

      // Determine which table to use based on the field
      const isUserField = userTableFields.includes(field);
      const isProfileField = profileTableFields.includes(field);

      if (isUserField) {
        // User table fields
        sdk.setTable("user");
        const result = await sdk.callRestAPI(
          {
            id: defaultValues?.user_id, // Use the user ID from the user object
            ...data,
          },
          "PUT"
        );

        if (!result.error) {
          showToast(globalDispatch, "Profile Updated", 4000);
          setEditingField(null);
          setEditValue("");
          fetchData();
        } else {
          handleValidationErrors(result);
        }
      } else if (isProfileField) {
        // Profile table fields
        sdk.setTable("profile");
        const result = await sdk.callRestAPI(
          {
            id: defaultValues?.profile_id, // Use the profile ID we stored separately
            ...data,
          },
          "PUT"
        );

        if (!result.error) {
          showToast(globalDispatch, "Profile Updated", 4000);
          setEditingField(null);
          setEditValue("");
          fetchData();
        } else {
          handleValidationErrors(result);
        }
      } else {
        // Field not recognized
        showToast(
          globalDispatch,
          "Unknown field type: " + field,
          4000,
          "error"
        );
        setSubmitLoading(false);
        return;
      }

      setSubmitLoading(false);
    } catch (error) {
      setSubmitLoading(false);
      setError(field, {
        type: "manual",
        message: error?.message ? error?.message : error.message,
      });
      tokenExpireError(
        dispatch,
        error?.message ? error?.message : error.message
      );
    }
  };

  // Helper function to handle validation errors
  const handleValidationErrors = (result) => {
    if (result.validation) {
      const keys = Object.keys(result.validation);
      for (let i = 0; i < keys.length; i++) {
        const field = keys[i];
        setError(field, {
          type: "manual",
          message: result.validation[field],
        });
      }
    }
  };

  const handleChangePhoto = (file) => {
    try {
      // Check file size before cropping (2MB limit)
      if (file.size > 2 * 1024 * 1024) {
        showToast(
          globalDispatch,
          "File size exceeds 2MB limit. Please choose a smaller file.",
          3000,
          "error"
        );
        return;
      }

      // Store the original file type
      setOriginalFileType(file.type);

      // Create a temporary URL for the image
      const reader = new FileReader();
      reader.onload = () => {
        setTempImageUrl(reader.result);
        setIsCropModalOpen(true);
      };
      reader.readAsDataURL(file);
    } catch (error) {
      showToast(globalDispatch, error?.message, 3000, "error");
      console.log(error);
    }
  };

  const handleCropComplete = async (croppedBlob) => {
    try {
      setProfilePictureLoading(true);

      // Use the stored original file type
      const isPNG = originalFileType === "image/png";

      // Create a File object from the cropped blob
      const croppedFile = new File(
        [croppedBlob],
        `cropped_profile.${isPNG ? "png" : "jpg"}`,
        { type: isPNG ? "image/png" : "image/jpeg" }
      );

      // Upload the cropped image
      let formData = new FormData();
      formData.append("file", croppedFile);
      let uploadResult = await sdk.uploadImage(formData);

      // Update the profile photo
      handleEdit("photo", uploadResult?.url);
    } catch (error) {
      showToast(globalDispatch, error?.message, 3000, "error");
      console.log(error);
    } finally {
      setProfilePictureLoading(false);
    }
  };

  const handleRemovePhoto = () => {
    // Photo is a user field, so we need to use the user table
    handleEdit("photo", null);
    setDefaultValues({ ...defaultValues, photo: null });
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "profile",
      },
    });

    fetchData();
  }, []);

  return (
    <div className="p-5">
      {loading || (profilePictureLoading && <LoadingSpinner />)}
      <div className="">
        <div className="">
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-gray-900">
              Profile details
            </h2>
          </div>

          {/* Image Crop Modal */}
          <ImageCropModal
            isOpen={isCropModalOpen}
            onClose={() => setIsCropModalOpen(false)}
            image={tempImageUrl}
            onCropComplete={handleCropComplete}
          />

          <div className="space-y-6">
            {/* Profile Picture Section */}
            <div className="flex items-center gap-4">
              <img
                src={oldPhoto || "/default-avatar.png"}
                alt="Profile"
                className="h-24 w-24 rounded-full object-cover"
              />
              <div>
                <div className="flex flex-col items-start justify-between gap-2">
                  <p className="font-medium text-gray-700">Profile Picture</p>
                  <div className="flex gap-2">
                    <button
                      onClick={handleRemovePhoto}
                      disabled={!oldPhoto}
                      className="rounded-xl border border-red-600 px-3 py-1.5 text-red-600 disabled:opacity-50"
                    >
                      Remove
                    </button>
                    <label className="cursor-pointer rounded-xl border border-gray-300 px-3 py-1.5 text-gray-700">
                      Change Photo
                      <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => handleChangePhoto(e.target.files[0])}
                        className="hidden"
                      />
                    </label>
                  </div>
                  <p className="text-xs text-gray-500">
                    Min 400x400px, PNG or JPEG
                  </p>
                </div>
              </div>
            </div>

            {/* Personal Information */}
            <div className="space-y-4">
              {[
                { key: "first_name", label: "First Name" },
                { key: "last_name", label: "Last Name" },
                {
                  key: "gender",
                  label: "Gender",
                  type: "select",
                  options: ["male", "female", "other"],
                },
                {
                  key: "email",
                  label: "Email",
                  note: "Your email is not shared with other users.",
                },
                { key: "phone", label: "Phone" },
                { key: "address", label: "Address" },
                { key: "city", label: "City" },
                { key: "state", label: "State" },
                { key: "zip_code", label: "Zip Code" },
                { key: "bio", label: "Bio", type: "textarea" },
              ].map((field) => (
                <div key={field.key}>
                  {editingField === field.key ? (
                    <div>
                      <label className="flex justify-between">
                        <span className="font-medium text-gray-700">
                          {field.label}
                        </span>
                        <button
                          onClick={() => setEditingField(null)}
                          className="text-primaryBlue underline hover:text-primaryBlue"
                        >
                          Cancel
                        </button>
                      </label>
                      {field.type === "select" ? (
                        <select
                          value={editValue}
                          onChange={(e) => setEditValue(e.target.value)}
                          className="mt-1 w-full rounded-md border border-gray-300 p-2"
                        >
                          <option value="">
                            Select {field.label.toLowerCase()}
                          </option>
                          {field.options.map((option) => (
                            <option key={option} value={option}>
                              {option.charAt(0).toUpperCase() + option.slice(1)}
                            </option>
                          ))}
                        </select>
                      ) : field.type === "textarea" ? (
                        <textarea
                          value={editValue}
                          onChange={(e) => setEditValue(e.target.value)}
                          rows={4}
                          className="mt-1 w-full rounded-md border border-gray-300 p-2"
                        />
                      ) : (
                        <input
                          type="text"
                          value={editValue}
                          onChange={(e) => setEditValue(e.target.value)}
                          className="mt-1 w-full rounded-xl border border-gray-300 p-2"
                        />
                      )}
                      {field.note && (
                        <p className="mt-1 text-xs text-gray-500">
                          {field.note}
                        </p>
                      )}
                      <div className="mt-2">
                        <InteractiveButton
                          loading={submitLoading}
                          onClick={() => handleEdit(field.key, editValue)}
                          className="rounded-xl bg-primaryBlue px-4 py-2 font-medium text-white hover:bg-primaryBlue"
                        >
                          Save
                        </InteractiveButton>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <div className="flex items-center justify-between">
                        <p className="font-medium text-gray-500">
                          {field.label}
                        </p>
                        <button
                          onClick={() => {
                            setEditingField(field.key);
                            setEditValue(defaultValues?.[field.key] || "");
                          }}
                          className="text-primaryBlue hover:text-indigo-800"
                        >
                          Edit
                        </button>
                      </div>
                      <p className="mt-1">
                        {defaultValues?.[field.key] || "--"}
                      </p>
                      {field.note && (
                        <p className="mt-1 text-xs text-gray-500">
                          {field.note}
                        </p>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomAdminStaffProfile;

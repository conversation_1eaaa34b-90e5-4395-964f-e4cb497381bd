import React, { useEffect, useContext, useState } from "react";
import { Routes, Route } from "react-router-dom";
import { AuthContext } from "Context/Auth";
import { GlobalContext } from "Context/Global";

import PrivateRoute from "./PrivateRoutes";
import PublicRoute from "./PublicRoutes";
import { PublicWrapper } from "Components/PublicWrapper";
import { AuthWrapper } from "Components/AuthWrapper";
import { NotFoundPage } from "Pages/404";
import { SnackBar } from "Components/SnackBar";
import { SessionExpiredModal } from "Components/SessionExpiredModal";

// generatePagesRoutes
import { ClubWrapper } from "Components/ClubWrapper";
import { StaffWrapper } from "Components/StaffWrapper";
import { UserWrapper } from "Components/UserWrapper";
import { CoachWrapper } from "Components/CoachWrapper";
import { AdminWrapper } from "Components/AdminWrapper";

import {
  AddAdminCmsPage,
  AddAdminEmailPage,
  AddAdminPhotoPage,
  AdminCmsListPage,
  AdminEmailListPage,
  AdminStripeInvoicesListPageV2,
  AdminPhotoListPage,
  EditAdminCmsPage,
  EditAdminEmailPage,
  UserMagicLoginPage,
  MagicLoginVerifyPage,
  CustomAdminLoginPage,
  CustomAdminSignUpPage,
  CustomAdminProfilePage,
  CustomCoachLoginPage,
  CustomCoachSignUpPage,
  CustomCoachProfilePage,
  CustomUserLoginPage,
  CustomUserSignUpPage,
  CustomUserProfilePage,
  CustomStaffLoginPage,
  CustomStaffSignUpPage,
  CustomStaffProfilePage,
  CustomClubLoginPage,
  CustomClubSignUpPage,
  CustomClubProfilePage,
  ClubListCoachTablePage,
  ClubAddCoachTablePage,
  ClubEditCoachTablePage,
  ClubViewCoachTablePage,
  ClubListClinicBookingsTablePage,
  ClubAddClinicBookingsTablePage,
  ClubEditClinicBookingsTablePage,
  ClubViewClinicBookingsTablePage,
  ClubListStripeInvoiceTablePage,
  ClubAddStripeInvoiceTablePage,
  ClubEditStripeInvoiceTablePage,
  ClubViewStripeInvoiceTablePage,
  ClubListStaffTablePage,
  ClubAddStaffTablePage,
  ClubEditStaffTablePage,
  ClubViewStaffTablePage,
  ClubListReservationTablePage,
  ClubAddReservationTablePage,
  ClubEditReservationTablePage,
  ClubViewReservationTablePage,
  ClubListEmailTablePage,
  ClubAddEmailTablePage,
  ClubEditEmailTablePage,
  ClubViewEmailTablePage,
  ClubListFindABuddyRequestsTablePage,
  ClubAddFindABuddyRequestsTablePage,
  ClubEditFindABuddyRequestsTablePage,
  ClubViewFindABuddyRequestsTablePage,
  ClubListBuddyTablePage,
  ClubAddBuddyTablePage,
  ClubEditBuddyTablePage,
  ClubViewBuddyTablePage,
  CoachViewPagesTablePage,
  CoachListReservationTablePage,
  UserListPagesTablePage,
  ClubForgotPage,
  ClubResetPage,
  ClubDashboardPage,
  StaffForgotPage,
  StaffResetPage,
  StaffDashboardPage,
  UserForgotPage,
  UserResetPage,
  UserDashboardPage,
  CoachForgotPage,
  CoachResetPage,
  CoachDashboardPage,
  AddAdminStripeProductPage,
  AdminStripeChargesListPage,
  AdminStripeInvoicesListPage,
  AdminStripeOrdersListPage,
  AdminStripePricesListPage,
  AdminStripeProductsListPage,
  AdminStripeSubscriptionsListPage,
  EditAdminStripeProductPage,
  AdminForgotPage,
  AdminResetPage,
  AdminDashboardPage,
  AdminUserListPage,
  AddAdminUserPage,
  AddAdminStripePricePage,
  EditAdminUserPage,
  EditAdminStripePricePage,
  ClubListDailyScheduler,
  ClubCourtManagement,
  ClubListAvailability,
  ClubListUI,
  ClubListUsers,
  ClubViewUserPage,
  ClubListClinicsTable,
  ClubAddClinicsTablePage,
  ClubListFindABuddyTablePage,
  ClubListHistoryTablePage,
  ClubListInvoiceTablePage,
  ClubListCustomRequests,
  ClubListLessons,
  ClubVerifyEmail,
  ProfileSetUpPage,
  ClubMembership,
  ClubListPricing,
  ClubViewCoachPage,
  AdminCustomerSupportPage,
  AdminListCourtManagement,
  AdminListAvailability,
  AdminListUI,
  AdminListcoaches,
  AdminListStaff,
  AdminListReservation,
  AdminListLessons,
  AdminListProgramsClinics,
  AdminAddClinicsTablePage,
  ClubAddLessonTablePage,
  AdminListCustomRequests,
  AdminListEmail,
  AdminListFindABuddy,
  AdminAddFindABuddy,
  AdminListInvoicing,
  AdminClubPricing,
  AdminListMembershipModules,
  PrivacyPolicy,
  TermsAndConditions,
  AdminFeesPage,
  ClubLandingPage,
  OAuth,
  CustomSignUpForm,
  UserReserveCourt,
  UserLessons,
  UserProgramsClinics,
  UserListMyGroups,
  UserMyReservation,
  UserMembership,
  UserClubCalendar,
  UserClinicBooking,
  UserListFindABuddy,
  PaymentSuccessConfirmation,
  UserAddCustomRequest,
  CoachProfileSetup,
  UserAddFindABuddyRequest,
  CoachVerifyEmail,
  CoachListStatistics,
  CoachListAvailability,
  UserAcceptInvite,
  PaymentReceipt,
  ReservationPayment,
  StaffClubUI,
  StaffAvailability,
  StaffCourtManagement,
  StaffDailyScheduler,
  StaffVerifyEmail,
  StaffListUsers,
  StaffListCoaches,
  StaffListStaff,
  StaffViewCoach,
  ClubViewStaff,
  StaffViewStaff,
  StaffListReservations,
  StaffListLessons,
  StaffListClinics,
  StaffListFindABuddy,
  StaffListCustomRequest,
  StaffListInvoice,
  StaffListHistory,
  StaffListEmail,
  StaffListPricing,
  StaffCustomSignUpForm,
  AdminViewStaff,
  StaffProfileSetup,
  ClubListFaqs,
  StaffListFaqs,
  ClubCustomerSupportPage,
  AdminListGeneralFaqs,
  CustomAdminStaffLoginPage,
  CustomAdminStaffSignUpPage,
  AdminStaffForgotPage,
  AdminStaffResetPage,
  AdminStaffDashboardPage,
  ClubListSport,
  AdminListAdminStaff,
  LandingPage,
  PublicFAQPage,
  CoachSetUpStripeMobile,
  StripeOnboardingComplete,
  AdminStaffVerifyEmail,
  AdminStaffOnboarding,
  UserVerifyEmail,
  AdminListSports,
  AdminListHistory,
  CustomAdminStaffProfilePage,
} from "./LazyLoad";
import AdminListDailyScheduler from "Pages/Admin/List/AdminListDailyScheduler";
import { AdminStaffWrapper } from "Components/AdminStaffWrapper";

export const DynamicWrapper = ({ isAuthenticated, role, children }) => {
  if (!isAuthenticated) {
    return <PublicWrapper>{children}</PublicWrapper>;
  }
  if (isAuthenticated) {
    if (role === "club") {
      return <ClubWrapper>{children}</ClubWrapper>;
    }

    if (role === "staff") {
      return <StaffWrapper>{children}</StaffWrapper>;
    }

    if (role === "user") {
      return <UserWrapper>{children}</UserWrapper>;
    }

    if (role === "coach") {
      return <CoachWrapper>{children}</CoachWrapper>;
    }

    if (role === "admin") {
      return <AdminWrapper>{children}</AdminWrapper>;
    }

    if (role === "admin_staff") {
      return <AdminStaffWrapper>{children}</AdminStaffWrapper>;
    }
  }
};

export const NotFound = ({ isAuthenticated, role }) => {
  // if (!isAuthenticated) {
  //   console.log({ isAuthenticated, role });
  //   return;
  // }
  if (!isAuthenticated) {
    return (
      <PublicWrapper>
        <NotFoundPage />
      </PublicWrapper>
    );
  }
  if (isAuthenticated) {
    if (role === "club") {
      return (
        <ClubWrapper>
          <NotFoundPage />
          {/* <Navigate to="/club/login" /> */}
        </ClubWrapper>
      );
    }

    if (role === "staff") {
      return (
        <StaffWrapper>
          <NotFoundPage />
        </StaffWrapper>
      );
    }

    if (role === "user") {
      return (
        <UserWrapper>
          <NotFoundPage />
        </UserWrapper>
      );
    }

    if (role === "coach") {
      return (
        <CoachWrapper>
          <NotFoundPage />
        </CoachWrapper>
      );
    }

    if (role === "admin") {
      return (
        <AdminWrapper>
          <NotFoundPage />
        </AdminWrapper>
      );
    }

    if (role === "admin_staff") {
      return (
        <AdminStaffWrapper>
          <NotFoundPage />
        </AdminStaffWrapper>
      );
    }
  }
};

export default () => {
  const { state } = useContext(AuthContext);
  const {
    state: { isOpen },
    dispatch,
  } = useContext(GlobalContext);
  const [screenSize, setScreenSize] = useState(window.innerWidth);

  function setDimension(e) {
    if (e.currentTarget.innerWidth >= 1024) {
      toggleSideBar(true);
    } else toggleSideBar(false);
    setScreenSize(e.currentTarget.innerWidth);
  }

  // const toTop = () => {
  //   containerRef.current.scrollTo(0, 0);
  // };

  const toggleSideBar = (open) => {
    if (isOpen && screenSize < 1024) {
      dispatch({
        type: "OPEN_SIDEBAR",
        payload: { isOpen: open },
      });
    } else if (!isOpen && screenSize >= 1024) {
      dispatch({
        type: "OPEN_SIDEBAR",
        payload: { isOpen: open },
      });
    }
  };

  useEffect(() => {
    window.addEventListener("resize", setDimension);

    return () => {
      window.removeEventListener("resize", setDimension);
    };
  }, [screenSize]);

  return (
    <div
      onClick={() => {
        isOpen ? toggleSideBar(false) : null;
      }}
      // className={`h-screen overflow-x-hidden bg-gradient-to-br from-[#FCF3F9] to-[#EAF8FB]`}
    >
      <Routes>
        <Route
          exact
          path="/"
          element={
            <PublicRoute
              path="/"
              element={
                <PublicWrapper>
                  <LandingPage />
                </PublicWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/faq"
          element={
            <PublicRoute
              path="/faq"
              element={
                <PublicWrapper>
                  <PublicFAQPage />
                </PublicWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/add-cms"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/add-cms"}
              element={
                <AdminWrapper>
                  <AddAdminCmsPage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/add-email"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/add-email"}
              element={
                <AdminWrapper>
                  <AddAdminEmailPage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/add-photo"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/add-photo"}
              element={
                <AdminWrapper>
                  <AddAdminPhotoPage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/cms"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/cms"}
              element={
                <AdminWrapper>
                  <AdminCmsListPage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/general-faqs"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/general-faqs"}
              element={
                <AdminWrapper>
                  <AdminListGeneralFaqs />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/court-management"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/court-management"}
              element={
                <AdminWrapper>
                  <AdminListCourtManagement />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/daily-scheduler"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/daily-scheduler"}
              element={
                <AdminWrapper>
                  <AdminListDailyScheduler />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/availability"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/availability"}
              element={
                <AdminWrapper>
                  <AdminListAvailability />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/club-ui"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/club-ui"}
              element={
                <AdminWrapper>
                  <AdminListUI />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          path="/admin/club-ui/membership"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/club-ui/membership"
              element={
                <AdminWrapper>
                  <AdminListMembershipModules />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          path="/admin/club-ui/pricing"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/club-ui/pricing"
              element={
                <AdminWrapper>
                  <AdminClubPricing />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          path="/admin/club-ui/sports"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/club-ui/sports"
              element={
                <AdminWrapper>
                  <AdminListSports />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          path="/admin/club-ui/custom-signup-form"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/club-ui/custom-signup-form"
              element={
                <AdminWrapper>
                  <CustomSignUpForm />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/coaches"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/coaches"}
              element={
                <AdminWrapper>
                  <AdminListcoaches />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/view-coach/:id"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/view-coach/:id"}
              element={
                <AdminWrapper>
                  <ClubViewCoachPage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/view-user/:id"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/view-user/:id"}
              element={
                <AdminWrapper>
                  <ClubViewUserPage />
                </AdminWrapper>
              }
            />
          }
        />
        {/* <Route
          exact
          path="/admin/email"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/email"}
              element={
                <AdminWrapper>
                  <AdminEmailListPage />
                </AdminWrapper>
              }
            />
          }
        /> */}
        <Route
          exact
          path="/admin/staff"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/staff"}
              element={
                <AdminWrapper>
                  <AdminListStaff />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/admin_staff"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/admin_staff"}
              element={
                <AdminWrapper>
                  <AdminListAdminStaff />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/invoicing"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/invoicing"}
              element={
                <AdminWrapper>
                  <AdminListInvoicing />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/reservations"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/reservations"}
              element={
                <AdminWrapper>
                  <AdminListReservation />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/lessons"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/lessons"}
              element={
                <AdminWrapper>
                  <AdminListLessons />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/program-clinics"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/program-clinics"}
              element={
                <AdminWrapper>
                  <AdminListProgramsClinics />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/view-staff/:id"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/view-staff/:id"}
              element={
                <AdminWrapper>
                  <AdminViewStaff />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/program-clinics/add"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/program-clinics/add"}
              element={
                <AdminWrapper>
                  <AdminAddClinicsTablePage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/photo"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/photo"}
              element={
                <AdminWrapper>
                  <AdminPhotoListPage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/edit-cms/:id"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/edit-cms/:id"}
              element={
                <AdminWrapper>
                  <EditAdminCmsPage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/edit-email/:id"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/edit-email/:id"}
              element={
                <AdminWrapper>
                  <EditAdminEmailPage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          path="/magic-login/:role"
          element={
            <PublicRoute
              path="/magic-login/:role"
              element={
                <AuthWrapper>
                  <UserMagicLoginPage />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          path="/magic-login/verify"
          element={
            <PublicRoute
              path="/magic-login/verify"
              element={
                <AuthWrapper>
                  <MagicLoginVerifyPage />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          path="/admin/login"
          element={
            <PublicRoute
              path="/admin/login"
              element={
                <AuthWrapper>
                  <CustomAdminLoginPage />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          path="/admin/signup"
          element={
            <PublicRoute
              path="/admin/signup"
              element={
                <AuthWrapper>
                  <CustomAdminSignUpPage />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/profile"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/profile"}
              element={
                <AdminWrapper>
                  <CustomAdminProfilePage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          path="/coach/login"
          element={
            <PublicRoute
              path="/coach/login"
              element={
                <AuthWrapper>
                  <CustomCoachLoginPage />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          path="/coach/verify-email"
          element={
            <PublicRoute
              path="/coach/verify-email"
              element={
                <AuthWrapper>
                  <CoachVerifyEmail />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          path="/coach/signup"
          element={
            <PublicRoute
              path="/coach/signup"
              element={
                <AuthWrapper>
                  <CustomCoachSignUpPage />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          path="/coach/setup-stripe"
          element={
            <PublicRoute
              path="/coach/setup-stripe"
              element={
                <AuthWrapper>
                  <CoachSetUpStripeMobile />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/coach/profile"
          element={
            <PrivateRoute
              access="coach"
              path={"/coach/profile"}
              element={
                // <CoachWrapper>
                <CustomCoachProfilePage />
                // </CoachWrapper>
              }
            />
          }
        />
        <Route
          path="/user/login"
          element={
            <PublicRoute
              path="/user/login"
              element={
                <AuthWrapper>
                  <CustomUserLoginPage />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          path="/user/verify-email"
          element={
            <PublicRoute
              path="/user/verify-email"
              element={
                <AuthWrapper>
                  <UserVerifyEmail />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          path="/user/signup"
          element={
            <PublicRoute
              path="/user/signup"
              element={
                <AuthWrapper>
                  <CustomUserSignUpPage />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/profile"
          element={
            <PrivateRoute
              access="user"
              path={"/user/profile"}
              element={
                // <UserWrapper>
                <CustomUserProfilePage />
                // </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/clinic-booking/:id"
          element={
            <PrivateRoute
              access="user"
              path={"/user/clinic-booking/:id"}
              element={
                // <UserWrapper>
                <UserClinicBooking />
                // </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/create-request"
          element={
            <PrivateRoute
              access="user"
              path={"/user/create-request"}
              element={<UserAddFindABuddyRequest />}
            />
          }
        />
        <Route
          exact
          path="/user/payment-success/:reservation_id"
          element={
            <PrivateRoute
              access="user"
              path={"/user/payment-success/:reservation_id"}
              element={<PaymentSuccessConfirmation />}
            />
          }
        />
        <Route
          exact
          path="/user/payment-receipt/:reservation_id"
          element={
            <PrivateRoute
              access="user"
              path={"/user/payment-receipt/:reservation_id"}
              element={<PaymentReceipt />}
            />
          }
        />
        <Route
          exact
          path="/user/find-a-buddy"
          element={
            <PrivateRoute
              access="user"
              path={"/user/find-a-buddy"}
              element={
                <UserWrapper>
                  <UserListFindABuddy />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          path="/user/membership/buy"
          element={
            <PrivateRoute
              access="user"
              path="/user/membership/buy"
              element={<UserMembership />}
            />
          }
        />
        <Route
          path="/user/reservation-payment/:reservationId"
          element={
            <PrivateRoute
              access="user"
              path="/user/reservation-payment/:reservationId"
              element={<ReservationPayment />}
            />
          }
        />
        <Route
          path="/staff/login"
          element={
            <PublicRoute
              path="/staff/login"
              element={
                <AuthWrapper>
                  <CustomStaffLoginPage />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          path="/staff/signup"
          element={
            <PublicRoute
              path="/staff/signup"
              element={
                <AuthWrapper>
                  <CustomStaffSignUpPage />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          path="/staff/verify-email"
          element={
            <PublicRoute
              path="/staff/verify-email"
              element={
                <AuthWrapper>
                  <StaffVerifyEmail />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/staff/profile"
          element={
            <PrivateRoute
              access="staff"
              path={"/staff/profile"}
              element={
                // <StaffWrapper>
                <CustomStaffProfilePage />
                // </StaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/staff/profile-setup"
          element={
            <PrivateRoute
              access="staff"
              path={"/staff/profile-setup"}
              element={
                <PrivateRoute
                  access="staff"
                  path="/staff/profile-setup"
                  element={<StaffProfileSetup />}
                />
              }
            />
          }
        />
        <Route
          exact
          path="/staff/users"
          element={
            <PrivateRoute
              access="staff"
              path={"/staff/users"}
              element={
                <StaffWrapper>
                  <StaffListUsers />
                </StaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/staff/coaches"
          element={
            <PrivateRoute
              access="staff"
              path={"/staff/coaches"}
              element={
                <StaffWrapper>
                  <StaffListCoaches />
                </StaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/stripe_onboarding"
          element={
            <PublicRoute
              access="staff"
              path={"/stripe_onboarding"}
              element={
                <AuthWrapper>
                  <StripeOnboardingComplete />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/staff/staff"
          element={
            <PrivateRoute
              access="staff"
              path={"/staff/staff"}
              element={
                <StaffWrapper>
                  <StaffListStaff />
                </StaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/staff/view-coach/:id"
          element={
            <PrivateRoute
              access="staff"
              path={"/staff/view-coach/:id"}
              element={
                <StaffWrapper>
                  <StaffViewCoach />
                </StaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/staff/view-staff/:id"
          element={
            <PrivateRoute
              access="staff"
              path={"/staff/view-staff/:id"}
              element={
                <StaffWrapper>
                  <StaffViewStaff />
                </StaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/staff/reservations"
          element={
            <PrivateRoute
              access="staff"
              path={"/staff/reservations"}
              element={
                <StaffWrapper>
                  <StaffListReservations />
                </StaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/staff/lessons"
          element={
            <PrivateRoute
              access="staff"
              path={"/staff/lessons"}
              element={
                <StaffWrapper>
                  <StaffListLessons />
                </StaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/staff/program-clinics"
          element={
            <PrivateRoute
              access="staff"
              path={"/staff/program-clinics"}
              element={
                <StaffWrapper>
                  <StaffListClinics />
                </StaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/staff/custom-requests"
          element={
            <PrivateRoute
              access="staff"
              path={"/staff/custom-requests"}
              element={
                <StaffWrapper>
                  <StaffListCustomRequest />
                </StaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/staff/history"
          element={
            <PrivateRoute
              access="staff"
              path={"/staff/history"}
              element={
                <StaffWrapper>
                  <StaffListHistory />
                </StaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/staff/invoicing"
          element={
            <PrivateRoute
              access="staff"
              path={"/staff/invoicing"}
              element={
                <StaffWrapper>
                  <StaffListInvoice />
                </StaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/staff/email"
          element={
            <PrivateRoute
              access="staff"
              path={"/staff/email"}
              element={
                <StaffWrapper>
                  <StaffListEmail />
                </StaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/staff/club-ui/custom-signup-form"
          element={
            <PrivateRoute
              access="staff"
              path={"/staff/club-ui/custom-signup-form"}
              element={
                <StaffWrapper>
                  <StaffCustomSignUpForm />
                </StaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/staff/club-ui/pricing"
          element={
            <PrivateRoute
              access="staff"
              path={"/staff/club-ui/pricing"}
              element={
                <StaffWrapper>
                  <StaffListPricing />
                </StaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/staff/find-a-buddy"
          element={
            <PrivateRoute
              access="staff"
              path={"/staff/find-a-buddy"}
              element={
                <StaffWrapper>
                  <StaffListFindABuddy />
                </StaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/view-staff/:id"
          element={
            <PrivateRoute
              access="club"
              path={"/club/view-staff/:id"}
              element={
                <ClubWrapper>
                  <ClubViewStaff />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/faq"
          element={
            <PrivateRoute
              access="club"
              path={"/club/faq"}
              element={
                <ClubWrapper>
                  <ClubListFaqs />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          path="/club/login"
          element={
            <PublicRoute
              path="/club/login"
              element={
                <AuthWrapper>
                  <CustomClubLoginPage />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          path="/club/signup"
          element={
            <PublicRoute
              path="/club/signup"
              element={
                <AuthWrapper>
                  <CustomClubSignUpPage />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          path="/club/daily-scheduler"
          element={
            <PrivateRoute
              access="club"
              path="/club/daily-scheduler"
              element={
                <ClubWrapper>
                  <ClubListDailyScheduler />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          path="/club/court-management"
          element={
            <PrivateRoute
              access="club"
              path="/club/court-management"
              element={
                <ClubWrapper>
                  <ClubCourtManagement />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          path="/club/availability"
          element={
            <PrivateRoute
              access="club"
              path="/club/availability"
              element={
                <ClubWrapper>
                  <ClubListAvailability />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          path="/club/view-coach/:id"
          element={
            <PrivateRoute
              access="club"
              path="/club/view-coach/:id"
              element={
                <ClubWrapper>
                  <ClubViewCoachPage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          path="/club/club-ui/membership"
          element={
            <PrivateRoute
              access="club"
              path="/club/club-ui/membership"
              element={
                <ClubWrapper>
                  <ClubMembership />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          path="/club/club-ui/sports"
          element={
            <PrivateRoute
              access="club"
              path="/club/club-ui/sports"
              element={
                <ClubWrapper>
                  <ClubListSport />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          path="/club/club-ui/custom-signup-form"
          element={
            <PrivateRoute
              access="club"
              path="/club/club-ui/custom-signup-form"
              element={
                <ClubWrapper>
                  <CustomSignUpForm />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          path="/club/club-ui/pricing"
          element={
            <PrivateRoute
              access="club"
              path="/club/club-ui/pricing"
              element={
                <ClubWrapper>
                  <ClubListPricing />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          path="/club/club-ui"
          element={
            <PrivateRoute
              access="club"
              path="/club/club-ui"
              element={
                <ClubWrapper>
                  <ClubListUI />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          path="/club/users"
          element={
            <PrivateRoute
              access="club"
              path="/club/users"
              element={
                <ClubWrapper>
                  <ClubListUsers />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          path="/club/view-user/:id"
          element={
            <PrivateRoute
              access="club"
              path="/club/view-user/:id"
              element={
                <ClubWrapper>
                  <ClubViewUserPage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/profile"
          element={
            <PrivateRoute
              access="club"
              path={"/club/profile"}
              element={
                <ClubWrapper>
                  <CustomClubProfilePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/profile-setup"
          element={
            <PrivateRoute
              access="club"
              path={"/club/profile-setup"}
              element={
                <PrivateRoute
                  access="club"
                  path="/club/profile-setup"
                  element={<ProfileSetUpPage />}
                />
              }
            />
          }
        />
        <Route
          exact
          path="/club/coaches"
          element={
            <PrivateRoute
              access="club"
              path="/club/coaches"
              element={
                <ClubWrapper>
                  <ClubListCoachTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/program-clinics"
          element={
            <PrivateRoute
              access="club"
              path="/club/program-clinics"
              element={
                <ClubWrapper>
                  <ClubListClinicsTable />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/program-clinics/add"
          element={
            <PrivateRoute
              access="club"
              path="/club/program-clinics/add"
              element={
                <ClubWrapper>
                  <ClubAddClinicsTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/find-a-buddy"
          element={
            <PrivateRoute
              access="club"
              path="/club/find-a-buddy"
              element={
                <ClubWrapper>
                  <ClubListFindABuddyTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/invoicing"
          element={
            <PrivateRoute
              access="club"
              path="/club/invoicing"
              element={
                <ClubWrapper>
                  <ClubListInvoiceTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/add-coach"
          element={
            <PrivateRoute
              access="club"
              path="/club/add-coach"
              element={
                <ClubWrapper>
                  <ClubAddCoachTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/edit-coach/:id"
          element={
            <PrivateRoute
              access="club"
              path="/club/edit-coach/:id"
              element={
                <ClubWrapper>
                  <ClubEditCoachTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/view-coach/:id"
          element={
            <PrivateRoute
              access="club"
              path="/club/view-coach/:id"
              element={
                <ClubWrapper>
                  <ClubViewCoachTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/clinic_bookings"
          element={
            <PrivateRoute
              access="club"
              path="/club/clinic_bookings"
              element={
                <ClubWrapper>
                  <ClubListClinicBookingsTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/history"
          element={
            <PrivateRoute
              access="club"
              path="/club/history"
              element={
                <ClubWrapper>
                  <ClubListHistoryTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/add-clinic_bookings"
          element={
            <PrivateRoute
              access="club"
              path="/club/add-clinic_bookings"
              element={
                <ClubWrapper>
                  <ClubAddClinicBookingsTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/edit-clinic_bookings/:id"
          element={
            <PrivateRoute
              access="club"
              path="/club/edit-clinic_bookings/:id"
              element={
                <ClubWrapper>
                  <ClubEditClinicBookingsTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/view-clinic_bookings/:id"
          element={
            <PrivateRoute
              access="club"
              path="/club/view-clinic_bookings/:id"
              element={
                <ClubWrapper>
                  <ClubViewClinicBookingsTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/stripe_invoice"
          element={
            <PrivateRoute
              access="club"
              path="/club/stripe_invoice"
              element={
                <ClubWrapper>
                  <ClubListStripeInvoiceTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/add-stripe_invoice"
          element={
            <PrivateRoute
              access="club"
              path="/club/add-stripe_invoice"
              element={
                <ClubWrapper>
                  <ClubAddStripeInvoiceTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/edit-stripe_invoice/:id"
          element={
            <PrivateRoute
              access="club"
              path="/club/edit-stripe_invoice/:id"
              element={
                <ClubWrapper>
                  <ClubEditStripeInvoiceTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/view-stripe_invoice/:id"
          element={
            <PrivateRoute
              access="club"
              path="/club/view-stripe_invoice/:id"
              element={
                <ClubWrapper>
                  <ClubViewStripeInvoiceTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/staff"
          element={
            <PrivateRoute
              access="club"
              path="/club/staff"
              element={
                <ClubWrapper>
                  <ClubListStaffTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/add-staff"
          element={
            <PrivateRoute
              access="club"
              path="/club/add-staff"
              element={
                <ClubWrapper>
                  <ClubAddStaffTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/edit-staff/:id"
          element={
            <PrivateRoute
              access="club"
              path="/club/edit-staff/:id"
              element={
                <ClubWrapper>
                  <ClubEditStaffTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/view-staff/:id"
          element={
            <PrivateRoute
              access="club"
              path="/club/view-staff/:id"
              element={
                <ClubWrapper>
                  <ClubViewStaffTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/reservations"
          element={
            <PrivateRoute
              access="club"
              path="/club/reservations"
              element={
                <ClubWrapper>
                  <ClubListReservationTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/add-reservation"
          element={
            <PrivateRoute
              access="club"
              path="/club/add-reservation"
              element={
                <ClubWrapper>
                  <ClubAddReservationTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/edit-reservation/:id"
          element={
            <PrivateRoute
              access="club"
              path="/club/edit-reservation/:id"
              element={
                <ClubWrapper>
                  <ClubEditReservationTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/view-reservation/:id"
          element={
            <PrivateRoute
              access="club"
              path="/club/view-reservation/:id"
              element={
                <ClubWrapper>
                  <ClubViewReservationTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/email"
          element={
            <PrivateRoute
              access="club"
              path="/club/email"
              element={
                <ClubWrapper>
                  <ClubListEmailTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/custom-requests"
          element={
            <PrivateRoute
              access="club"
              path="/club/custom-requests"
              element={
                <ClubWrapper>
                  <ClubListCustomRequests />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/lessons"
          element={
            <PrivateRoute
              access="club"
              path="/club/lessons"
              element={
                <ClubWrapper>
                  <ClubListLessons />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/lessons/add"
          element={
            <PrivateRoute
              access="club"
              path="/club/lessons/add"
              element={
                <ClubWrapper>
                  <ClubAddLessonTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/add-email"
          element={
            <PrivateRoute
              access="club"
              path="/club/add-email"
              element={
                <ClubWrapper>
                  <ClubAddEmailTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/edit-email/:id"
          element={
            <PrivateRoute
              access="club"
              path="/club/edit-email/:id"
              element={
                <ClubWrapper>
                  <ClubEditEmailTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/view-email/:id"
          element={
            <PrivateRoute
              access="club"
              path="/club/view-email/:id"
              element={
                <ClubWrapper>
                  <ClubViewEmailTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/find_a_buddy_requests"
          element={
            <PrivateRoute
              access="club"
              path="/club/find_a_buddy_requests"
              element={
                <ClubWrapper>
                  <ClubListFindABuddyRequestsTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/add-find_a_buddy_requests"
          element={
            <PrivateRoute
              access="club"
              path="/club/add-find_a_buddy_requests"
              element={
                <ClubWrapper>
                  <ClubAddFindABuddyRequestsTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/edit-find_a_buddy_requests/:id"
          element={
            <PrivateRoute
              access="club"
              path="/club/edit-find_a_buddy_requests/:id"
              element={
                <ClubWrapper>
                  <ClubEditFindABuddyRequestsTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/view-find_a_buddy_requests/:id"
          element={
            <PrivateRoute
              access="club"
              path="/club/view-find_a_buddy_requests/:id"
              element={
                <ClubWrapper>
                  <ClubViewFindABuddyRequestsTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/buddy"
          element={
            <PrivateRoute
              access="club"
              path="/club/buddy"
              element={
                <ClubWrapper>
                  <ClubListBuddyTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/add-buddy"
          element={
            <PrivateRoute
              access="club"
              path="/club/add-buddy"
              element={
                <ClubWrapper>
                  <ClubAddBuddyTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/edit-buddy/:id"
          element={
            <PrivateRoute
              access="club"
              path="/club/edit-buddy/:id"
              element={
                <ClubWrapper>
                  <ClubEditBuddyTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/view-buddy/:id"
          element={
            <PrivateRoute
              access="club"
              path="/club/view-buddy/:id"
              element={
                <ClubWrapper>
                  <ClubViewBuddyTablePage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/coach/view-pages/:id"
          element={
            <PrivateRoute
              access="coach"
              path="/coach/view-pages/:id"
              element={
                <CoachWrapper>
                  <CoachViewPagesTablePage />
                </CoachWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/coach/reservation"
          element={
            <PrivateRoute
              access="coach"
              path="/coach/reservation"
              element={
                <CoachWrapper>
                  <CoachListReservationTablePage />
                </CoachWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/pages"
          element={
            <PrivateRoute
              access="user"
              path="/user/pages"
              element={
                <UserWrapper>
                  <UserListPagesTablePage />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/club-calendar"
          element={
            <PrivateRoute
              access="user"
              path="/user/club-calendar"
              element={
                <UserWrapper>
                  <UserClubCalendar />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          path="/club/forgot"
          element={
            <PublicRoute
              path="/club/forgot"
              element={
                <AuthWrapper>
                  <ClubForgotPage />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          path="/club/verify-email"
          element={
            <PublicRoute
              path="/club/verify-email"
              element={
                <AuthWrapper>
                  <ClubVerifyEmail />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          path="/club/reset"
          element={
            <PublicRoute
              path="/club/reset"
              element={
                <AuthWrapper>
                  <ClubResetPage />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/dashboard"
          element={
            <PrivateRoute
              access="club"
              path={"/club/dashboard"}
              element={
                <ClubWrapper>
                  <ClubDashboardPage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/staff/faq"
          element={
            <PrivateRoute
              access="staff"
              path={"/staff/faq"}
              element={
                <StaffWrapper>
                  <StaffListFaqs />
                </StaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/club/customer-support"
          element={
            <PrivateRoute
              access="club"
              path="/club/customer-support"
              element={
                <ClubWrapper>
                  <ClubCustomerSupportPage />
                </ClubWrapper>
              }
            />
          }
        />
        <Route
          path="/staff/forgot"
          element={
            <PublicRoute
              path="/staff/forgot"
              element={
                <AuthWrapper>
                  <StaffForgotPage />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          path="/staff/reset"
          element={
            <PublicRoute
              path="/staff/reset"
              element={
                <AuthWrapper>
                  <StaffResetPage />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/staff/dashboard"
          element={
            <PrivateRoute
              access="staff"
              path={"/staff/dashboard"}
              element={
                <StaffWrapper>
                  <StaffDashboardPage />
                </StaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/staff/club-ui"
          element={
            <PrivateRoute
              access="staff"
              path={"/staff/club-ui"}
              element={
                <StaffWrapper>
                  <StaffClubUI />
                </StaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/staff/availability"
          element={
            <PrivateRoute
              access="staff"
              path={"/staff/availability"}
              element={
                <StaffWrapper>
                  <StaffAvailability />
                </StaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/staff/court-management"
          element={
            <PrivateRoute
              access="staff"
              path={"/staff/court-management"}
              element={
                <StaffWrapper>
                  <StaffCourtManagement />
                </StaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/staff/daily-scheduler"
          element={
            <PrivateRoute
              access="staff"
              path={"/staff/daily-scheduler"}
              element={
                <StaffWrapper>
                  <StaffDailyScheduler />
                </StaffWrapper>
              }
            />
          }
        />
        <Route
          path="/user/forgot"
          element={
            <PublicRoute
              path="/user/forgot"
              element={
                <AuthWrapper>
                  <UserForgotPage />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          path="/login/oauth"
          element={
            <PublicRoute
              path="/login/oauth"
              element={
                <AuthWrapper>
                  <OAuth />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          path="/user/reset"
          element={
            <PublicRoute
              path="/user/reset"
              element={
                <AuthWrapper>
                  <UserResetPage />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/dashboard"
          element={
            <PrivateRoute
              access="user"
              path={"/user/dashboard"}
              element={
                <UserWrapper>
                  <UserDashboardPage />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/create-custom-request"
          element={
            <PrivateRoute
              access="user"
              path={"/user/create-custom-request"}
              element={
                // <UserWrapper>
                <UserAddCustomRequest />
                // </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/my-reservations"
          element={
            <PrivateRoute
              access="user"
              path={"/user/my-reservations"}
              element={
                <UserWrapper>
                  <UserMyReservation />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/reserve-court"
          element={
            <PrivateRoute
              access="user"
              path={"/user/reserve-court"}
              element={
                // <UserWrapper>
                <UserReserveCourt />
                // </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/lessons"
          element={
            <PrivateRoute
              access="user"
              path={"/user/lessons"}
              element={
                <UserWrapper>
                  <UserLessons />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/program-clinics"
          element={
            <PrivateRoute
              access="user"
              path={"/user/program-clinics"}
              element={
                <UserWrapper>
                  <UserProgramsClinics />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/my-groups"
          element={
            <PrivateRoute
              access="user"
              path="/user/my-groups"
              element={
                <UserWrapper>
                  <UserListMyGroups />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/user/group/accept-invite/:id"
          element={
            <PrivateRoute
              access="user"
              path="/user/group/accept-invite/:id"
              element={
                <UserWrapper>
                  <UserAcceptInvite />
                </UserWrapper>
              }
            />
          }
        />
        <Route
          path="/coach/forgot"
          element={
            <PublicRoute
              path="/coach/forgot"
              element={
                <AuthWrapper>
                  <CoachForgotPage />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          path="/coach/reset"
          element={
            <PublicRoute
              path="/coach/reset"
              element={
                <AuthWrapper>
                  <CoachResetPage />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/coach/profile-setup"
          element={
            <PrivateRoute
              access="coach"
              path={"/coach/profile-setup"}
              element={
                // <CoachWrapper>
                <CoachProfileSetup />
                // </CoachWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/coach/availability"
          element={
            <PrivateRoute
              access="coach"
              path={"/coach/availability"}
              element={
                <CoachWrapper>
                  <CoachListAvailability />
                </CoachWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/coach/statistics"
          element={
            <PrivateRoute
              access="coach"
              path={"/coach/statistics"}
              element={
                <CoachWrapper>
                  <CoachListStatistics />
                </CoachWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/coach/dashboard"
          element={
            <PrivateRoute
              access="coach"
              path={"/coach/dashboard"}
              element={
                <CoachWrapper>
                  <CoachDashboardPage />
                </CoachWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/add-price"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/add-price"
              element={
                <AdminWrapper>
                  <AddAdminStripePricePage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/add-product"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/add-product"
              element={
                <AdminWrapper>
                  <AddAdminStripeProductPage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/invoice"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/invoice"
              element={
                <AdminWrapper>
                  <AdminStripeInvoicesListPage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/order"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/order"
              element={
                <AdminWrapper>
                  <AdminStripeOrdersListPage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/price"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/price"
              element={
                <AdminWrapper>
                  <AdminStripePricesListPage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/product"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/product"
              element={
                <AdminWrapper>
                  <AdminStripeProductsListPage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/customer-support"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/customer-support"
              element={
                <AdminWrapper>
                  <AdminCustomerSupportPage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/fee-settings"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/fee-settings"
              element={
                <AdminWrapper>
                  <AdminFeesPage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/email"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/email"
              element={
                <AdminWrapper>
                  <AdminListEmail />
                </AdminWrapper>
              }
            />
          }
        />
        {/* <Route
          exact
          path="/admin/staffs"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/staffs"
              element={
                <AdminWrapper>
                  <AdminListStaffs />
                </AdminWrapper>
              }
            />
          }
        /> */}
        <Route
          exact
          path="/admin/custom-requests"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/custom-requests"
              element={
                <AdminWrapper>
                  <AdminListCustomRequests />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/add-find-a-buddy"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/add-find-a-buddy"
              element={
                <AdminWrapper>
                  <AdminAddFindABuddy />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/find-a-buddy"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/find-a-buddy"
              element={
                <AdminWrapper>
                  <AdminListFindABuddy />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/subscription"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/subscription"
              element={
                <AdminWrapper>
                  <AdminStripeSubscriptionsListPage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/edit-price/:id"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/edit-price/:id"
              element={
                <AdminWrapper>
                  <EditAdminStripePricePage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/edit-product/:id"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/edit-product/:id"
              element={
                <AdminWrapper>
                  <EditAdminStripeProductPage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          path="/admin/forgot"
          element={
            <PublicRoute
              path="/admin/forgot"
              element={
                <AuthWrapper>
                  <AdminForgotPage />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          path="/admin/reset"
          element={
            <PublicRoute
              path="/admin/reset"
              element={
                <AuthWrapper>
                  <AdminResetPage />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          path="/privacy-policy"
          element={
            <PublicRoute path="/privacy-policy" element={<PrivacyPolicy />} />
          }
        />
        <Route
          path="/terms-and-conditions"
          element={
            <PublicRoute
              path="/terms-and-conditions"
              element={<TermsAndConditions />}
            />
          }
        />
        <Route
          exact
          path="/admin/dashboard"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/dashboard"}
              element={
                <AdminWrapper>
                  <AdminDashboardPage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/users"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/users"
              element={
                <AdminWrapper>
                  <AdminUserListPage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/add-user"
          element={
            <PrivateRoute
              access="admin"
              path="/admin/add-user"
              element={
                <AdminWrapper>
                  <AddAdminUserPage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin/edit-user/:id"
          element={
            <PrivateRoute
              access="admin"
              path={"/admin/edit-user/:id"}
              element={
                <AdminWrapper>
                  <EditAdminUserPage />
                </AdminWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin_staff/dashboard"
          element={
            <PrivateRoute
              access="admin_staff"
              path={"/admin_staff/dashboard"}
              element={
                <AdminStaffWrapper>
                  <AdminStaffDashboardPage />
                </AdminStaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin_staff/profile"
          element={
            <PrivateRoute
              access="admin_staff"
              path={"/admin_staff/profile"}
              element={
                <AdminStaffWrapper>
                  <CustomAdminStaffProfilePage />
                </AdminStaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin_staff/onboarding"
          element={
            <PrivateRoute
              access="admin_staff"
              path={"/admin_staff/onboarding"}
              element={<AdminStaffOnboarding />}
            />
          }
        />
        <Route
          exact
          path="/admin_staff/login"
          element={
            <PublicRoute
              access="admin_staff"
              path={"/admin_staff/login"}
              element={
                <AuthWrapper>
                  <CustomAdminStaffLoginPage />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin_staff/signup"
          element={
            <PublicRoute
              access="admin_staff"
              path={"/admin_staff/signup"}
              element={
                <AuthWrapper>
                  <CustomAdminStaffSignUpPage />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin_staff/forgot"
          element={
            <PublicRoute
              access="admin_staff"
              path={"/admin_staff/forgot"}
              element={
                <AuthWrapper>
                  <AdminStaffForgotPage />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin_staff/reset"
          element={
            <PublicRoute
              access="admin_staff"
              path={"/admin_staff/reset"}
              element={
                <AuthWrapper>
                  <AdminStaffResetPage />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          path="/admin_staff/verify-email"
          element={
            <PublicRoute
              path="/admin_staff/verify-email"
              element={
                <AuthWrapper>
                  <AdminStaffVerifyEmail />
                </AuthWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin_staff/customer-support"
          element={
            <PrivateRoute
              access="admin_staff"
              path={"/admin_staff/customer-support"}
              element={
                <AdminStaffWrapper>
                  <AdminCustomerSupportPage />
                </AdminStaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin_staff/club-ui/sports"
          element={
            <PrivateRoute
              access="admin_staff"
              path={"/admin_staff/club-ui/sports"}
              element={
                <AdminStaffWrapper>
                  <AdminListSports />
                </AdminStaffWrapper>
              }
            />
          }
        />

        <Route
          exact
          path="/admin_staff/daily-scheduler"
          element={
            <PrivateRoute
              access="admin_staff"
              path={"/admin_staff/daily-scheduler"}
              element={
                <AdminStaffWrapper>
                  <AdminListDailyScheduler />
                </AdminStaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin_staff/court-management"
          element={
            <PrivateRoute
              access="admin_staff"
              path={"/admin_staff/court-management"}
              element={
                <AdminStaffWrapper>
                  <AdminListCourtManagement />
                </AdminStaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin_staff/availability"
          element={
            <PrivateRoute
              access="admin_staff"
              path={"/admin_staff/availability"}
              element={
                <AdminStaffWrapper>
                  <AdminListAvailability />
                </AdminStaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin_staff/club-ui"
          element={
            <PrivateRoute
              access="admin_staff"
              path={"/admin_staff/club-ui"}
              element={
                <AdminStaffWrapper>
                  <AdminListUI />
                </AdminStaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin_staff/users"
          element={
            <PrivateRoute
              access="admin_staff"
              path={"/admin_staff/users"}
              element={
                <AdminStaffWrapper>
                  <AdminUserListPage />
                </AdminStaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin_staff/coaches"
          element={
            <PrivateRoute
              access="admin_staff"
              path={"/admin_staff/coaches"}
              element={
                <AdminStaffWrapper>
                  <AdminListcoaches />
                </AdminStaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin_staff/staff"
          element={
            <PrivateRoute
              access="admin_staff"
              path={"/admin_staff/staff"}
              element={
                <AdminStaffWrapper>
                  <AdminListStaff />
                </AdminStaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin_staff/reservations"
          element={
            <PrivateRoute
              access="admin_staff"
              path={"/admin_staff/reservations"}
              element={
                <AdminStaffWrapper>
                  <AdminListReservation />
                </AdminStaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin_staff/lessons"
          element={
            <PrivateRoute
              access="admin_staff"
              path={"/admin_staff/lessons"}
              element={
                <AdminStaffWrapper>
                  <AdminListLessons />
                </AdminStaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin_staff/program-clinics"
          element={
            <PrivateRoute
              access="admin_staff"
              path={"/admin_staff/program-clinics"}
              element={
                <AdminStaffWrapper>
                  <AdminListProgramsClinics />
                </AdminStaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin_staff/find-a-buddy"
          element={
            <PrivateRoute
              access="admin_staff"
              path={"/admin_staff/find-a-buddy"}
              element={
                <AdminStaffWrapper>
                  <AdminListFindABuddy />
                </AdminStaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin_staff/custom-requests"
          element={
            <PrivateRoute
              access="admin_staff"
              path={"/admin_staff/custom-requests"}
              element={
                <AdminStaffWrapper>
                  <AdminListCustomRequests />
                </AdminStaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin_staff/email"
          element={
            <PrivateRoute
              access="admin_staff"
              path={"/admin_staff/email"}
              element={
                <AdminStaffWrapper>
                  <AdminListEmail />
                </AdminStaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin_staff/general-faqs"
          element={
            <PrivateRoute
              access="admin_staff"
              path={"/admin_staff/general-faqs"}
              element={
                <AdminStaffWrapper>
                  <AdminListGeneralFaqs />
                </AdminStaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin_staff/invoicing"
          element={
            <PrivateRoute
              access="admin_staff"
              path={"/admin_staff/invoicing"}
              element={
                <AdminStaffWrapper>
                  <AdminListInvoicing />
                </AdminStaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin_staff/history"
          element={
            <PrivateRoute
              access="admin_staff"
              path={"/admin_staff/history"}
              element={
                <AdminStaffWrapper>
                  <AdminListHistory />
                </AdminStaffWrapper>
              }
            />
          }
        />
        <Route
          exact
          path="/admin_staff/fee-settings"
          element={
            <PrivateRoute
              access="admin_staff"
              path={"/admin_staff/fee-settings"}
              element={
                <AdminStaffWrapper>
                  <AdminFeesPage />
                </AdminStaffWrapper>
              }
            />
          }
        />
        <Route
          path="/club/:club_id"
          element={
            <PublicRoute path="/club/:club_id" element={<ClubLandingPage />} />
          }
        />
        <Route
          path={"*"}
          element={<PublicRoute path={"*"} element={<NotFound {...state} />} />}
        />
      </Routes>
      <SessionExpiredModal />
      <SnackBar />
    </div>
  );
};
